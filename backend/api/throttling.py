"""
Custom throttling classes for API rate limiting.
"""
from rest_framework.throttling import User<PERSON><PERSON><PERSON>hrottle, AnonRateThrottle
from django.core.cache import cache
from django.conf import settings
import time


class LoginRateThrottle(AnonRateThrottle):
    """
    Rate limiting for login attempts.
    """
    scope = 'login'


class RegisterRateThrottle(AnonRateThrottle):
    """
    Rate limiting for user registration.
    """
    scope = 'register'


class PasswordResetRateThrottle(AnonRateThrottle):
    """
    Rate limiting for password reset requests.
    """
    scope = 'password_reset'


class OrderRateThrottle(UserRateThrottle):
    """
    Rate limiting for order creation.
    """
    scope = 'order'


class PaymentRateThrottle(UserRateThrottle):
    """
    Rate limiting for payment processing.
    """
    scope = 'payment'


class BurstRateThrottle(UserRateThrottle):
    """
    Short burst rate limiting for sensitive operations.
    """
    scope = 'burst'
    rate = '10/min'


class IPBasedThrottle(AnonRateThrottle):
    """
    IP-based rate limiting for additional security.
    """
    def get_cache_key(self, request, view):
        """
        Get cache key based on IP address.
        """
        if request.user.is_authenticated:
            ident = request.user.pk
        else:
            ident = self.get_ident(request)
        
        return self.cache_format % {
            'scope': self.scope,
            'ident': ident
        }


class CustomRateThrottle:
    """
    Custom rate limiting implementation with Redis/cache backend.
    """
    
    def __init__(self, rate='60/min'):
        self.rate = rate
        self.num_requests, self.duration = self.parse_rate(rate)
    
    def parse_rate(self, rate):
        """
        Parse rate string like '60/min' into (num_requests, duration_seconds).
        """
        if rate is None:
            return (None, None)
        
        num, period = rate.split('/')
        num_requests = int(num)
        
        duration_map = {
            's': 1,
            'sec': 1,
            'm': 60,
            'min': 60,
            'h': 3600,
            'hour': 3600,
            'd': 86400,
            'day': 86400,
        }
        
        duration = duration_map.get(period, 60)
        return (num_requests, duration)
    
    def allow_request(self, request, view=None):
        """
        Check if request should be allowed based on rate limiting.
        """
        if self.rate is None:
            return True
        
        key = self.get_cache_key(request, view)
        if key is None:
            return True
        
        history = cache.get(key, [])
        now = time.time()
        
        # Remove old entries
        while history and history[0] <= now - self.duration:
            history.pop(0)
        
        if len(history) >= self.num_requests:
            return False
        
        # Add current request
        history.append(now)
        cache.set(key, history, self.duration)
        
        return True
    
    def get_cache_key(self, request, view):
        """
        Generate cache key for rate limiting.
        """
        if request.user.is_authenticated:
            ident = f"user_{request.user.pk}"
        else:
            ident = f"ip_{self.get_client_ip(request)}"
        
        return f"throttle_{self.rate}_{ident}"
    
    def get_client_ip(self, request):
        """
        Get client IP address from request.
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def wait(self):
        """
        Return time to wait before next request is allowed.
        """
        return self.duration


def rate_limit_decorator(rate='60/min'):
    """
    Decorator for rate limiting view functions.
    
    Usage:
    @rate_limit_decorator('10/min')
    def my_view(request):
        pass
    """
    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            throttle = CustomRateThrottle(rate)
            if not throttle.allow_request(request):
                from django.http import JsonResponse
                return JsonResponse({
                    'error': 'Rate limit exceeded',
                    'detail': f'Rate limit of {rate} exceeded. Please try again later.'
                }, status=429)
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


class SecurityThrottle:
    """
    Advanced security-focused throttling with suspicious activity detection.
    """
    
    def __init__(self):
        self.suspicious_threshold = 50  # requests per minute
        self.block_duration = 3600  # 1 hour
    
    def check_suspicious_activity(self, request):
        """
        Check for suspicious activity patterns.
        """
        ip = self.get_client_ip(request)
        key = f"security_check_{ip}"
        
        # Get request count for this IP
        count = cache.get(key, 0)
        
        if count > self.suspicious_threshold:
            # Block this IP
            block_key = f"blocked_ip_{ip}"
            cache.set(block_key, True, self.block_duration)
            return False
        
        # Increment counter
        cache.set(key, count + 1, 60)  # Reset every minute
        return True
    
    def is_blocked(self, request):
        """
        Check if IP is currently blocked.
        """
        ip = self.get_client_ip(request)
        block_key = f"blocked_ip_{ip}"
        return cache.get(block_key, False)
    
    def get_client_ip(self, request):
        """
        Get client IP address from request.
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
