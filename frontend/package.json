{"name": "ecommerce-react", "version": "0.1.0", "private": true, "dependencies": {"@headlessui/react": "^2.2.9", "@heroicons/react": "^2.2.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@tailwindcss/forms": "^0.5.3", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.3.4", "framer-motion": "^12.23.22", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.64.0", "react-hot-toast": "^2.5.2", "react-intersection-observer": "^9.16.0", "react-router-dom": "^6.8.2", "react-scripts": "5.0.1", "tailwindcss": "^3.2.7", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "theme": "node switch-theme.js", "theme:dark": "node switch-theme.js modernDark", "theme:ocean": "node switch-theme.js oceanBreeze", "theme:sunset": "node switch-theme.js sunsetGlow", "theme:forest": "node switch-theme.js forestGreen", "theme:purple": "node switch-theme.js royalPurple"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.13", "postcss": "^8.4.21"}}