"""
Typesense utilities for the eCommerce platform.

This module provides functions for interacting with Typesense search engine,
including client initialization, schema creation, and document operations.
"""
try:
    import typesense  # type: ignore
except Exception:
    typesense = None  # graceful fallback when not installed

from django.conf import settings
import logging
import os

logger = logging.getLogger(__name__)

def _typesense_enabled() -> bool:
    return os.environ.get('TYPESENSE_ENABLED', 'False').lower() == 'true'

def get_typesense_client():
    """
    Initialize and return a Typesense client using settings from Django settings.

    Returns:
        typesense.Client: Configured Typesense client
    """
    if not typesense or not _typesense_enabled():
        raise RuntimeError("Typesense is not enabled or the 'typesense' package is not installed")
    try:
        return typesense.Client(settings.TYPESENSE)
    except Exception as e:
        logger.error(f"Failed to initialize Typesense client: {str(e)}")
        raise

def create_product_schema():
    """
    Create the product collection schema in Typesense if it doesn't exist.
    
    Returns:
        dict: Response from Typesense API
    """
    if not typesense or not _typesense_enabled():
        logger.info("Typesense disabled or not installed; skipping schema creation")
        return None

    client = get_typesense_client()

    # Define the schema for products
    schema = {
        'name': settings.TYPESENSE['collection_name'],
        'fields': [
            {'name': 'id', 'type': 'string'},
            {'name': 'title', 'type': 'string'},
            {'name': 'slug', 'type': 'string'},
            {'name': 'summary', 'type': 'string', 'optional': True},
            {'name': 'description', 'type': 'string', 'optional': True},
            {'name': 'price', 'type': 'float'},
            {'name': 'discount_value', 'type': 'float'},
            {'name': 'stock_quantity', 'type': 'int32'},
            {'name': 'is_active', 'type': 'bool'},
            {'name': 'category_id', 'type': 'string'},
            {'name': 'category_name', 'type': 'string'},
            {'name': 'tags', 'type': 'string[]', 'optional': True},
            {'name': 'created_at', 'type': 'int64'},  # Unix timestamp
            {'name': 'updated_at', 'type': 'int64'},  # Unix timestamp
        ],
        'default_sorting_field': 'created_at'
    }
    
    try:
        # Check if collection exists
        try:
            client.collections[settings.TYPESENSE['collection_name']].retrieve()
            logger.info(f"Collection {settings.TYPESENSE['collection_name']} already exists")
            return None
        except typesense.exceptions.ObjectNotFound:
            # Create collection if it doesn't exist
            return client.collections.create(schema)
    except Exception as e:
        logger.error(f"Failed to create Typesense schema: {str(e)}")
        raise

def index_product(product):
    """
    Index a product in Typesense.
    
    Args:
        product: Product model instance
        
    Returns:
        dict: Response from Typesense API
    """
    if not typesense or not _typesense_enabled():
        logger.debug("Typesense disabled or not installed; skipping index for product %s", getattr(product, 'id', '?'))
        return None

    client = get_typesense_client()
    collection_name = settings.TYPESENSE['collection_name']

    # Convert Django model to Typesense document
    document = {
        'id': str(product.id),
        'title': product.title,
        'slug': product.slug,
        'summary': product.summary or '',
        'description': product.description or '',
        'price': float(product.price),
        'discount_value': float(product.discount_value),
        'stock_quantity': product.stock_quantity,
        'is_active': product.is_active,
        'category_id': str(product.category.id),
        'category_name': product.category.name,
        'tags': product.tags if product.tags else [],
        'created_at': int(product.created_at.timestamp()),
        'updated_at': int(product.updated_at.timestamp()),
    }
    
    try:
        # Upsert document (create or update)
        return client.collections[collection_name].documents.upsert(document)
    except Exception as e:
        logger.error(f"Failed to index product {product.id}: {str(e)}")
        raise

def delete_product(product_id):
    """
    Delete a product from Typesense.
    
    Args:
        product_id: UUID of the product to delete
        
    Returns:
        dict: Response from Typesense API
    """
    if not typesense or not _typesense_enabled():
        logger.debug("Typesense disabled or not installed; skipping delete for product %s", product_id)
        return None

    client = get_typesense_client()
    collection_name = settings.TYPESENSE['collection_name']

    try:
        return client.collections[collection_name].documents[str(product_id)].delete()
    except Exception as e:
        logger.error(f"Failed to delete product {product_id} from Typesense: {str(e)}")
        raise

def search_products(query, filters=None, sort_by=None, page=1, per_page=10):
    """
    Search for products in Typesense.
    
    Args:
        query (str): Search query
        filters (dict, optional): Filters to apply
        sort_by (str, optional): Field to sort by
        page (int, optional): Page number
        per_page (int, optional): Results per page
        
    Returns:
        dict: Search results from Typesense
    """
    if not typesense or not _typesense_enabled():
        raise RuntimeError("Typesense is not enabled or the 'typesense' package is not installed")

    client = get_typesense_client()
    collection_name = settings.TYPESENSE['collection_name']

    search_parameters = {
        'q': query,
        'query_by': 'title,summary,description,category_name,tags',
        'page': page,
        'per_page': per_page,
    }
    
    # Add filters if provided
    if filters:
        filter_str = []
        for key, value in filters.items():
            if isinstance(value, list):
                filter_str.append(f"{key}:[{','.join(map(str, value))}]")
            else:
                filter_str.append(f"{key}:{value}")
        
        if filter_str:
            search_parameters['filter_by'] = ' && '.join(filter_str)
    
    # Add sorting if provided
    if sort_by:
        search_parameters['sort_by'] = sort_by
    
    try:
        return client.collections[collection_name].documents.search(search_parameters)
    except Exception as e:
        logger.error(f"Failed to search products: {str(e)}")
        raise
