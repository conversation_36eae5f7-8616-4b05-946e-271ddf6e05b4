# Generated by Django 4.2.7 on 2025-10-09 17:10

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0007_add_promocode_management'),
    ]

    operations = [
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('contact_person', models.CharField(blank=True, max_length=255, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('address', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTime<PERSON>ield(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.Alter<PERSON>ield(
            model_name='inventorychange',
            name='change_type',
            field=models.CharField(choices=[('add', 'Addition'), ('subtract', 'Subtraction'), ('order', 'Order Placement'), ('return', 'Order Return'), ('adjustment', 'Manual Adjustment'), ('initial', 'Initial Stock'), ('batch_add', 'Batch Addition'), ('batch_subtract', 'Batch Subtraction'), ('expiry', 'Expired Stock Removal')], max_length=20),
        ),
        migrations.CreateModel(
            name='InventoryBatch',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('batch_number', models.CharField(max_length=100)),
                ('quantity_received', models.IntegerField()),
                ('quantity_remaining', models.IntegerField()),
                ('cost_per_unit', models.DecimalField(decimal_places=2, max_digits=10)),
                ('manufacturing_date', models.DateField(blank=True, null=True)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('received_date', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventory_batches', to='api.product')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventory_batches', to='api.supplier')),
            ],
            options={
                'ordering': ['expiry_date', 'received_date'],
                'unique_together': {('product', 'batch_number', 'supplier')},
            },
        ),
        migrations.AddField(
            model_name='inventorychange',
            name='batch',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='inventory_changes', to='api.inventorybatch'),
        ),
        migrations.CreateModel(
            name='SalesReport',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('report_date', models.DateField()),
                ('report_type', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('yearly', 'Yearly')], max_length=10)),
                ('total_orders', models.IntegerField(default=0)),
                ('total_revenue', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_units_sold', models.IntegerField(default=0)),
                ('total_discount_given', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('new_customers', models.IntegerField(default=0)),
                ('returning_customers', models.IntegerField(default=0)),
                ('top_selling_product_units', models.IntegerField(default=0)),
                ('average_order_value', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('top_selling_product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='top_sales_reports', to='api.product')),
            ],
            options={
                'ordering': ['-report_date'],
                'unique_together': {('report_date', 'report_type')},
            },
        ),
    ]
