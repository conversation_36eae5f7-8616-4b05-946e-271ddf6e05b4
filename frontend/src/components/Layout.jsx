import React from 'react';
import { Toaster } from 'react-hot-toast';
import Navbar from './Navbar';
import Footer from './Footer';
import { useAuth } from '../contexts/AuthContext.jsx';

/**
 * Layout component that wraps all pages with a consistent structure
 * Includes Navbar, Footer, and toast notifications
 */
const Layout = ({ children }) => {
  const { currentUser } = useAuth();
  return (
    <div className="flex min-h-screen flex-col bg-gray-50">
      {/* Toast notifications */}
      <Toaster position="top-right" />

      {/* Navigation */}
      <Navbar userId={currentUser?.id} />

      {/* Main Content */}
      <main className="flex-grow">
        {children}
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default Layout;
