import React, { useState, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { 
  TruckIcon, 
  ShieldCheckIcon, 
  CreditCardIcon, 
  ChatBubbleLeftRightIcon,
  GiftIcon,
  ClockIcon,
  StarIcon,
  HeartIcon
} from '@heroicons/react/24/outline';

const FeaturesSection = () => {
  const [hoveredFeature, setHoveredFeature] = useState(null);
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, threshold: 0.1 });

  const features = [
    {
      id: 1,
      icon: TruckIcon,
      title: "Free Shipping",
      description: "Free delivery on orders over $50",
      color: "from-blue-500 to-cyan-500",
      bgColor: "bg-blue-50",
      stats: "2-3 days delivery"
    },
    {
      id: 2,
      icon: ShieldCheckIcon,
      title: "Secure Payment",
      description: "100% secure payment processing",
      color: "from-green-500 to-emerald-500",
      bgColor: "bg-green-50",
      stats: "SSL encrypted"
    },
    {
      id: 3,
      icon: CreditCardIcon,
      title: "Easy Returns",
      description: "30-day hassle-free returns",
      color: "from-purple-500 to-pink-500",
      bgColor: "bg-purple-50",
      stats: "No questions asked"
    },
    {
      id: 4,
      icon: ChatBubbleLeftRightIcon,
      title: "24/7 Support",
      description: "Round-the-clock customer service",
      color: "from-orange-500 to-red-500",
      bgColor: "bg-orange-50",
      stats: "Live chat available"
    },
    {
      id: 5,
      icon: GiftIcon,
      title: "Gift Cards",
      description: "Perfect gifts for your loved ones",
      color: "from-pink-500 to-rose-500",
      bgColor: "bg-pink-50",
      stats: "Never expire"
    },
    {
      id: 6,
      icon: ClockIcon,
      title: "Quick Delivery",
      description: "Same-day delivery in select cities",
      color: "from-indigo-500 to-blue-500",
      bgColor: "bg-indigo-50",
      stats: "Within 4 hours"
    },
    {
      id: 7,
      icon: StarIcon,
      title: "Quality Guarantee",
      description: "Premium quality products only",
      color: "from-yellow-500 to-orange-500",
      bgColor: "bg-yellow-50",
      stats: "5-star rated"
    },
    {
      id: 8,
      icon: HeartIcon,
      title: "Wishlist",
      description: "Save your favorite items",
      color: "from-red-500 to-pink-500",
      bgColor: "bg-red-50",
      stats: "Unlimited saves"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    }
  };

  return (
    <div ref={ref} className="bg-white py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Why Choose Our Platform?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We're committed to providing you with the best shopping experience through our premium services and features.
          </p>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {features.map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <motion.div
                key={feature.id}
                variants={itemVariants}
                onHoverStart={() => setHoveredFeature(feature.id)}
                onHoverEnd={() => setHoveredFeature(null)}
                className="group cursor-pointer"
              >
                <motion.div
                  whileHover={{ y: -10 }}
                  whileTap={{ scale: 0.95 }}
                  className={`relative ${feature.bgColor} rounded-2xl p-6 text-center transition-all duration-300 hover:shadow-xl border border-gray-100 overflow-hidden`}
                >
                  {/* Background Pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute inset-0 bg-gradient-to-br from-black/5 to-black/10" />
                  </div>

                  {/* Animated Background */}
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={hoveredFeature === feature.id ? { scale: 1, opacity: 0.1 } : { scale: 0, opacity: 0 }}
                    className={`absolute inset-0 bg-gradient-to-br ${feature.color} rounded-2xl`}
                  />

                  {/* Icon */}
                  <motion.div
                    animate={hoveredFeature === feature.id ? {
                      scale: [1, 1.2, 1],
                      rotate: [0, 10, -10, 0]
                    } : {}}
                    transition={{ duration: 0.5 }}
                    className="relative z-10 mb-4"
                  >
                    <div className={`w-16 h-16 mx-auto bg-gradient-to-br ${feature.color} rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300`}>
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>
                  </motion.div>

                  {/* Content */}
                  <div className="relative z-10">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-gray-800">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600 mb-3 group-hover:text-gray-700">
                      {feature.description}
                    </p>
                    
                    {/* Stats Badge */}
                    <motion.div
                      initial={{ scale: 0.8, opacity: 0 }}
                      animate={hoveredFeature === feature.id ? { scale: 1, opacity: 1 } : { scale: 0.8, opacity: 0 }}
                      className={`inline-block bg-gradient-to-r ${feature.color} text-white px-3 py-1 rounded-full text-sm font-medium`}
                    >
                      {feature.stats}
                    </motion.div>
                  </div>

                  {/* Floating Elements */}
                  <motion.div
                    animate={hoveredFeature === feature.id ? {
                      y: [0, -10, 0],
                      opacity: [0, 1, 0]
                    } : {}}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="absolute top-4 right-4 w-2 h-2 bg-white rounded-full"
                  />
                  
                  <motion.div
                    animate={hoveredFeature === feature.id ? {
                      y: [0, -15, 0],
                      opacity: [0, 0.7, 0]
                    } : {}}
                    transition={{ duration: 2.5, repeat: Infinity, delay: 0.5 }}
                    className="absolute bottom-4 left-4 w-1.5 h-1.5 bg-white rounded-full"
                  />
                </motion.div>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 border border-blue-100">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Experience the Difference?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Join thousands of satisfied customers who trust us for their shopping needs. Start exploring our amazing collection today!
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              Start Shopping Now
            </motion.button>
          </div>
        </motion.div>

        {/* Trust Indicators */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : {}}
          transition={{ duration: 0.6, delay: 1 }}
          className="mt-12 pt-8 border-t border-gray-200"
        >
          <div className="flex items-center justify-center space-x-8 text-gray-500">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse" />
              <span className="text-sm">99.9% Uptime</span>
            </div>
            <div className="flex items-center space-x-2">
              <StarIcon className="w-4 h-4 text-yellow-400" />
              <span className="text-sm">4.9/5 Rating</span>
            </div>
            <div className="flex items-center space-x-2">
              <HeartIcon className="w-4 h-4 text-red-400" />
              <span className="text-sm">50K+ Happy Customers</span>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default FeaturesSection;
