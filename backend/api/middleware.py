"""
Custom middleware for security and rate limiting.
"""
import json
import time
from django.http import JsonResponse
from django.core.cache import cache
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin
from django.middleware.csrf import get_token
from .throttling import SecurityThrottle


class SecurityMiddleware(MiddlewareMixin):
    """
    Enhanced security middleware with rate limiting and suspicious activity detection.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.security_throttle = SecurityThrottle()
        super().__init__(get_response)
    
    def process_request(self, request):
        """
        Process incoming request for security checks.
        """
        # Check if IP is blocked
        if self.security_throttle.is_blocked(request):
            return JsonResponse({
                'error': 'Access denied',
                'detail': 'Your IP has been temporarily blocked due to suspicious activity.'
            }, status=403)
        
        # Check for suspicious activity
        if not self.security_throttle.check_suspicious_activity(request):
            return JsonResponse({
                'error': 'Rate limit exceeded',
                'detail': 'Too many requests. Your IP has been temporarily blocked.'
            }, status=429)
        
        return None
    
    def process_response(self, request, response):
        """
        Process response to add security headers.
        """
        # Add security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Add CSRF token to response headers for API endpoints
        if request.path.startswith('/api/'):
            csrf_token = get_token(request)
            response['X-CSRFToken'] = csrf_token
        
        return response


class RateLimitMiddleware(MiddlewareMixin):
    """
    Global rate limiting middleware.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        """
        Apply global rate limiting.
        """
        # Skip rate limiting for static files and admin
        if (request.path.startswith('/static/') or
            request.path.startswith('/media/') or
            request.path.startswith('/admin/')):
            return None

        # Get client identifier
        # Check if user attribute exists (after authentication middleware)
        if hasattr(request, 'user') and request.user.is_authenticated:
            client_id = f"user_{request.user.pk}"
            rate_limit = 1000  # requests per hour for authenticated users
        else:
            client_id = f"ip_{self.get_client_ip(request)}"
            rate_limit = 100   # requests per hour for anonymous users
        
        # Check rate limit
        cache_key = f"global_rate_limit_{client_id}"
        current_count = cache.get(cache_key, 0)
        
        if current_count >= rate_limit:
            return JsonResponse({
                'error': 'Rate limit exceeded',
                'detail': f'Global rate limit of {rate_limit} requests per hour exceeded.'
            }, status=429)
        
        # Increment counter
        cache.set(cache_key, current_count + 1, 3600)  # 1 hour
        
        return None
    
    def get_client_ip(self, request):
        """
        Get client IP address from request.
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class CSRFExemptMiddleware(MiddlewareMixin):
    """
    Middleware to handle CSRF exemption for specific API endpoints.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        # Define endpoints that should be exempt from CSRF
        self.csrf_exempt_paths = [
            '/api/auth/login/',
            '/api/auth/register/',
            '/api/auth/password/reset/',
            '/api/webhooks/',  # For payment webhooks
        ]
        super().__init__(get_response)
    
    def process_request(self, request):
        """
        Exempt specific paths from CSRF protection.
        """
        if any(request.path.startswith(path) for path in self.csrf_exempt_paths):
            setattr(request, '_dont_enforce_csrf_checks', True)
        return None


class APILoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log API requests for security monitoring.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        """
        Log incoming API requests.
        """
        if request.path.startswith('/api/'):
            # Log request details
            log_data = {
                'timestamp': time.time(),
                'method': request.method,
                'path': request.path,
                'ip': self.get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'user_id': request.user.pk if hasattr(request, 'user') and request.user.is_authenticated else None,
            }
            
            # Store in cache for monitoring (keep for 24 hours)
            cache_key = f"api_log_{time.time()}_{self.get_client_ip(request)}"
            cache.set(cache_key, log_data, 86400)
        
        return None
    
    def get_client_ip(self, request):
        """
        Get client IP address from request.
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class CORSMiddleware(MiddlewareMixin):
    """
    Enhanced CORS middleware with additional security.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.allowed_origins = getattr(settings, 'CORS_ALLOWED_ORIGINS', [])
        super().__init__(get_response)
    
    def process_response(self, request, response):
        """
        Add CORS headers with security considerations.
        """
        origin = request.META.get('HTTP_ORIGIN')
        
        if origin and origin in self.allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, PATCH, DELETE, OPTIONS'
            response['Access-Control-Allow-Headers'] = (
                'Accept, Accept-Encoding, Authorization, Content-Type, '
                'DNT, Origin, User-Agent, X-CSRFToken, X-Requested-With'
            )
            response['Access-Control-Expose-Headers'] = 'X-CSRFToken'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        
        return response


class RequestSizeMiddleware(MiddlewareMixin):
    """
    Middleware to limit request size for security.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.max_size = 10 * 1024 * 1024  # 10MB default
        super().__init__(get_response)
    
    def process_request(self, request):
        """
        Check request size limits.
        """
        content_length = request.META.get('CONTENT_LENGTH')
        
        if content_length:
            try:
                content_length = int(content_length)
                if content_length > self.max_size:
                    return JsonResponse({
                        'error': 'Request too large',
                        'detail': f'Request size exceeds maximum allowed size of {self.max_size} bytes.'
                    }, status=413)
            except ValueError:
                pass
        
        return None
