import React, { useState, useEffect, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import {
  ChartBarIcon,
  EyeIcon,
  ShoppingCartIcon,
  CursorArrowRaysIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon,
  CalendarIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

// Import components
import Layout from '../components/Layout.jsx';

// Import services
import { getUserAnalytics } from '../services/api';
import { useAuth } from '../contexts/AuthContext.jsx';

const UserAnalyticsPage = () => {
  const { userId } = useParams();
  const { currentUser } = useAuth();
  const [analytics, setAnalytics] = useState({
    user: {},
    activity_summary: {},
    viewed_products: [],
    recent_activities: [],
    daily_breakdown: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timeRange, setTimeRange] = useState('week');

  // Check if user can view this analytics page
  const canViewAnalytics = currentUser && (
    currentUser.is_staff ||
    currentUser.groups?.some(group => group.name === 'manager') ||
    (userId && currentUser.id === userId) ||
    !userId
  );


  const fetchAnalyticsData = useCallback(async () => {
    try {
      setLoading(true);

      const targetUserId = userId || currentUser.id;
      const response = await getUserAnalytics(targetUserId, `?range=${timeRange}`);
      setAnalytics(response.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching user analytics:', err);
      setError('Failed to load analytics data. Please try again later.');
      toast.error('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  }, [timeRange, userId, currentUser]);


  useEffect(() => {
    if (!canViewAnalytics) {
      setError('Permission denied. You can only view your own analytics.');
      setLoading(false);
      return;
    }
    fetchAnalyticsData();
  }, [fetchAnalyticsData, canViewAnalytics]);

  if (!canViewAnalytics) {
    return (
      <Layout>
        <div className="mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">Access Denied</h1>
            <p className="mt-4 text-lg text-gray-500">You don't have permission to view this analytics page.</p>
          </div>


        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="mb-8 sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
              {userId ? `Analytics for ${analytics.user.username || 'User'}` : 'Your Analytics'}
            </h1>
            <p className="mt-2 text-sm text-gray-500">
              Detailed activity tracking and behavior patterns
            </p>
          </div>

          <div className="mt-4 sm:mt-0">
            <div className="flex items-center space-x-4">
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm"
              >
                <option value="day">Last 24 Hours</option>
                <option value="week">Last 7 Days</option>
                <option value="month">Last 30 Days</option>
                <option value="year">Last Year</option>
              </select>

              <button
                onClick={fetchAnalyticsData}
                className="inline-flex items-center rounded-md bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700"
              >
                <ArrowPathIcon className="mr-2 -ml-1 h-5 w-5" />
                Refresh
              </button>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center py-12">
            <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-primary-600"></div>
          </div>
        ) : error ? (
          <div className="rounded-md bg-red-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-8">
            {/* Activity Summary */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-5">
              <div className="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 rounded-md bg-blue-100 p-3">
                    <EyeIcon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Page Views</dt>
                      <dd className="text-3xl font-semibold text-gray-900">{analytics.activity_summary?.page_views || 0}</dd>
                    </dl>
                  </div>
                </div>
              </div>

              <div className="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 rounded-md bg-green-100 p-3">
                    <CursorArrowRaysIcon className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Product Views</dt>
                      <dd className="text-3xl font-semibold text-gray-900">{analytics.activity_summary?.product_views || 0}</dd>
                    </dl>
                  </div>
                </div>
              </div>

              <div className="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 rounded-md bg-purple-100 p-3">
                    <MagnifyingGlassIcon className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Searches</dt>
                      <dd className="text-3xl font-semibold text-gray-900">{analytics.activity_summary?.searches || 0}</dd>
                    </dl>
                  </div>
                </div>
              </div>

              <div className="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 rounded-md bg-orange-100 p-3">
                    <ShoppingCartIcon className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Cart Additions</dt>
                      <dd className="text-3xl font-semibold text-gray-900">{analytics.activity_summary?.add_to_cart || 0}</dd>
                    </dl>
                  </div>
                </div>
              </div>

              <div className="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 rounded-md bg-yellow-100 p-3">
                    <ChartBarIcon className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Purchases</dt>
                      <dd className="text-3xl font-semibold text-gray-900">{analytics.activity_summary?.purchases || 0}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            {/* Total Activities */}
            <div className="rounded-lg bg-white p-6 shadow">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Total Activities</h3>
                  <p className="text-sm text-gray-500">All tracked interactions in the selected time period</p>
                </div>
                <div className="flex items-center">
                  <ClockIcon className="h-8 w-8 text-primary-500 mr-2" />
                  <span className="text-3xl font-bold text-primary-600">
                    {analytics.activity_summary?.total_activities || 0}
                  </span>
                </div>
              </div>
            </div>

            {/* Most Viewed Products */}
            <div className="rounded-lg bg-white p-6 shadow">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Most Viewed Products</h3>
              <div className="space-y-3">
                {analytics.viewed_products?.length > 0 ? (
                  analytics.viewed_products.map((product, index) => (
                    <div key={product.product__id} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <span className="flex h-6 w-6 items-center justify-center rounded-full bg-primary-100 text-xs font-medium text-primary-600">
                          {index + 1}
                        </span>
                        <span className="ml-3 text-sm text-gray-900">{product.product__title}</span>
                      </div>
                      <span className="text-sm font-medium text-gray-500">{product.view_count} views</span>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-500">No product views recorded yet</p>
                )}
              </div>
            </div>

            {/* Daily Activity Breakdown */}
            <div className="rounded-lg bg-white p-6 shadow">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Daily Activity Breakdown</h3>
              <div className="space-y-4">
                {analytics.daily_breakdown?.length > 0 ? (
                  analytics.daily_breakdown.map((day) => (
                    <div key={day.date} className="border-l-4 border-primary-200 pl-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <CalendarIcon className="h-5 w-5 text-gray-400 mr-2" />
                          <span className="text-sm font-medium text-gray-900">{day.date}</span>
                        </div>
                        <span className="text-sm text-gray-500">{day.activities} activities</span>
                      </div>
                      <div className="mt-2 grid grid-cols-4 gap-4 text-xs text-gray-500">
                        <div>Page Views: {day.page_views}</div>
                        <div>Product Views: {day.product_views}</div>
                        <div>Cart Adds: {day.add_to_cart}</div>
                        <div>Total: {day.activities}</div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-500">No daily activity data available</p>
                )}
              </div>
            </div>

            {/* Recent Activities */}
            <div className="overflow-hidden rounded-lg bg-white shadow">
              <div className="px-4 py-5 sm:px-6">
                <h2 className="text-lg font-medium text-gray-900">Recent Activities</h2>
                <p className="mt-1 text-sm text-gray-500">Latest interactions and behaviors</p>
              </div>
              <div className="border-t border-gray-200">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Action</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Product</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Page</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Time</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {analytics.recent_activities?.length > 0 ? (
                        analytics.recent_activities.map((activity) => (
                          <tr key={activity.id}>
                            <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                              {activity.action.replace('_', ' ').charAt(0).toUpperCase() + activity.action.replace('_', ' ').slice(1)}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                              {activity.product ? activity.product.title : 'N/A'}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                              {activity.page || 'N/A'}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                              {new Date(activity.timestamp).toLocaleString()}
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan="4" className="px-6 py-4 text-center text-sm text-gray-500">
                            No recent activities recorded
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default UserAnalyticsPage;
