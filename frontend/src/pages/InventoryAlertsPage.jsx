import React, { useEffect, useState } from 'react';
import Layout from '../components/Layout.jsx';
import toast from 'react-hot-toast';
import { inventoryService } from '../services/api';

const Pill = ({ text, tone='gray' }) => {
  const colors = {
    red: 'bg-red-100 text-red-800',
    amber: 'bg-amber-100 text-amber-800',
    gray: 'bg-gray-100 text-gray-800'
  };
  return (
    <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${colors[tone] || colors.gray}`}>{text}</span>
  );
};

const InventoryAlertsPage = () => {
  const [loading, setLoading] = useState(true);
  const [alerts, setAlerts] = useState([]);
  const [summary, setSummary] = useState(null);

  const load = async () => {
    setLoading(true);
    try {
      const [activeRes, summaryRes] = await Promise.all([
        inventoryService.listActiveAlerts(),
        inventoryService.getAlertDashboardSummary(),
      ]);
      setAlerts(activeRes.data?.results || activeRes.data || []);
      setSummary(summaryRes.data);
    } catch (e) {
      console.error(e);
      toast.error('Failed to load alerts');
    } finally { setLoading(false); }
  };

  useEffect(()=>{ load(); }, []);

  const ack = async (id) => {
    try { await inventoryService.acknowledgeAlert(id); toast.success('Acknowledged'); load(); }
    catch (e) { toast.error(e?.response?.data?.error || 'Failed'); }
  };
  const dismiss = async (id) => {
    try { await inventoryService.dismissAlert(id); toast.success('Dismissed'); load(); }
    catch (e) { toast.error(e?.response?.data?.error || 'Failed'); }
  };

  return (
    <Layout>
      <div className="container mx-auto py-10 px-4 space-y-8">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Inventory Alerts</h1>
          <button onClick={load} className="px-3 py-2 rounded bg-gray-100 hover:bg-gray-200 text-sm">Refresh</button>
        </div>

        {summary && (
          <div className="bg-white border rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-3">Dashboard Summary</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div><div className="text-gray-500">Active Alerts</div><div className="text-xl font-semibold">{summary.total_active}</div></div>
              <div><div className="text-gray-500">Critical</div><div className="text-xl font-semibold">{summary.by_priority?.critical || 0}</div></div>
              <div><div className="text-gray-500">Low Stock</div><div className="text-xl font-semibold">{summary.by_type?.low_stock || 0}</div></div>
              <div><div className="text-gray-500">Expiry</div><div className="text-xl font-semibold">{summary.by_type?.expiry || 0}</div></div>
            </div>
          </div>
        )}

        <div className="bg-white rounded-lg shadow border border-gray-100">
          <div className="px-6 py-4 border-b">
            <h2 className="text-lg font-semibold">Active Alerts</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="text-left text-sm text-gray-500">
                  <th className="px-6 py-3">Title</th>
                  <th className="px-6 py-3">Product</th>
                  <th className="px-6 py-3">Priority</th>
                  <th className="px-6 py-3">Age</th>
                  <th className="px-6 py-3">Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr><td className="px-6 py-6" colSpan={5}>Loading...</td></tr>
                ) : alerts.length ? alerts.map(a => (
                  <tr key={a.id} className="border-t">
                    <td className="px-6 py-3">{a.title}</td>
                    <td className="px-6 py-3">{a.product_title || '\u2014'}</td>
                    <td className="px-6 py-3">
                      <Pill text={a.priority_display || a.priority} tone={a.priority === 'critical' ? 'red' : a.priority === 'high' ? 'amber' : 'gray'} />
                    </td>
                    <td className="px-6 py-3">{a.days_since_created} days</td>
                    <td className="px-6 py-3 space-x-3">
                      {!a.is_acknowledged && <button onClick={()=>ack(a.id)} className="text-blue-600 hover:underline">Acknowledge</button>}
                      {a.is_active && <button onClick={()=>dismiss(a.id)} className="text-red-600 hover:underline">Dismiss</button>}
                    </td>
                  </tr>
                )) : (
                  <tr><td className="px-6 py-6" colSpan={5}>No active alerts</td></tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default InventoryAlertsPage;

