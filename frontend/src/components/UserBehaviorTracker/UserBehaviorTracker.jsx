import React, { useEffect } from 'react';
import { fetchUserBehavior } from '../../services/api';

const UserBehaviorTracker = ({ userId }) => {
  useEffect(() => {
    const trackBehavior = async () => {
      await fetchUserBehavior(userId);
    };
    trackBehavior();
  }, [userId]);

  return null;  // This component is invisible to the user but tracks behavior
};

export default UserBehaviorTracker;
