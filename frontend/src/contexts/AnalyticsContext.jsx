import React, { createContext, useContext, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import {
  trackEvent,
  trackProductImpression,
  trackProductView,
  trackAddToCart,
  trackCheckoutStart,
  trackPurchase,
  trackSearch
} from '../components/Analytics/GoogleAnalytics';
import { useAuth } from './AuthContext.jsx';
import { trackUserBehavior } from '../services/api';

// Create context
const AnalyticsContext = createContext();

/**
 * Analytics Provider component that manages tracking user behavior
 * and sending data to both Google Analytics and our backend
 */
export const AnalyticsProvider = ({ children }) => {
  const location = useLocation();
  const { currentUser } = useAuth();

  // Track page views
  useEffect(() => {
    const pageName = getPageNameFromPath(location.pathname);
    
    // Track page view in our backend if user is logged in
    if (currentUser) {
      trackUserBehavior({
        userId: currentUser.id,
        action: 'page_view',
        metadata: {
          page: pageName,
          path: location.pathname,
          referrer: document.referrer,
          timestamp: new Date().toISOString()
        }
      });
    }
  }, [location, currentUser]);

  /**
   * Track a user action/event
   * @param {string} action - The action type (e.g., 'click', 'add_to_cart')
   * @param {Object} metadata - Additional data about the action
   * @param {string} productId - Optional product ID if action is related to a product
   */
  const trackAction = (action, metadata = {}, productId = null) => {
    // Track in Google Analytics
    trackEvent(action, metadata);

    // Track in our backend if user is logged in
    if (currentUser) {
      trackUserBehavior({
        userId: currentUser.id,
        action,
        productId,
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString()
        }
      });
    }
  };

  /**
   * Track product impressions in lists
   * @param {Array} products - Array of products being displayed
   * @param {string} listName - Name of the list (e.g., 'search_results', 'category_page')
   */
  const trackProductImpressions = (products, listName) => {
    products.forEach((product, index) => {
      trackProductImpression(product, listName, index + 1);

      // Also track in backend
      if (currentUser) {
        trackUserBehavior({
          userId: currentUser.id,
          action: 'product_impression',
          productId: product.id,
          metadata: {
            list_name: listName,
            position: index + 1,
            timestamp: new Date().toISOString()
          }
        });
      }
    });
  };

  /**
   * Track product detail view
   * @param {Object} product - Product object
   * @param {string} source - Source of the view
   */
  const trackProductDetailView = (product, source = 'direct') => {
    trackProductView(product, source);

    // Track in backend
    if (currentUser) {
      trackUserBehavior({
        userId: currentUser.id,
        action: 'view',
        productId: product.id,
        metadata: {
          source,
          product_name: product.title || product.name,
          product_category: product.category,
          product_price: product.price,
          timestamp: new Date().toISOString()
        }
      });
    }
  };

  /**
   * Track add to cart action
   * @param {Object} product - Product object
   * @param {number} quantity - Quantity added
   * @param {string} source - Source of the action
   */
  const trackAddToCartAction = (product, quantity = 1, source = 'product_page') => {
    trackAddToCart(product, quantity, source);

    // Track in backend
    if (currentUser) {
      trackUserBehavior({
        userId: currentUser.id,
        action: 'add_to_cart',
        productId: product.id,
        metadata: {
          quantity,
          source,
          product_name: product.title || product.name,
          product_price: product.price,
          total_value: product.price * quantity,
          timestamp: new Date().toISOString()
        }
      });
    }
  };

  /**
   * Track checkout start
   * @param {Array} cartItems - Array of cart items
   * @param {number} totalValue - Total cart value
   */
  const trackCheckoutStartAction = (cartItems, totalValue) => {
    trackCheckoutStart(cartItems, totalValue);

    // Track in backend
    if (currentUser) {
      trackUserBehavior({
        userId: currentUser.id,
        action: 'checkout_start',
        metadata: {
          cart_items_count: cartItems.length,
          total_value: totalValue,
          items: cartItems.map(item => ({
            product_id: item.product?.id || item.id,
            quantity: item.quantity,
            price: item.product?.price || item.price
          })),
          timestamp: new Date().toISOString()
        }
      });
    }
  };

  /**
   * Track purchase completion
   * @param {Object} orderData - Order data
   */
  const trackPurchaseAction = (orderData) => {
    trackPurchase(orderData);

    // Track in backend
    if (currentUser) {
      trackUserBehavior({
        userId: currentUser.id,
        action: 'purchase',
        metadata: {
          order_id: orderData.id,
          total_value: orderData.total_price,
          items_count: orderData.items.length,
          payment_method: orderData.payment_method,
          timestamp: new Date().toISOString()
        }
      });
    }
  };

  /**
   * Track search action
   * @param {string} searchTerm - Search term
   * @param {number} resultCount - Number of results
   */
  const trackSearchAction = (searchTerm, resultCount = 0) => {
    trackSearch(searchTerm, resultCount);

    // Track in backend
    if (currentUser) {
      trackUserBehavior({
        userId: currentUser.id,
        action: 'search',
        metadata: {
          search_term: searchTerm,
          result_count: resultCount,
          timestamp: new Date().toISOString()
        }
      });
    }
  };

  // Helper function to get page name from path
  const getPageNameFromPath = (path) => {
    if (path === '/') return 'home';
    
    // Remove leading slash and split by remaining slashes
    const segments = path.substring(1).split('/');
    
    if (segments.length === 1) return segments[0];
    
    // Handle product detail pages
    if (segments[0] === 'products' && segments.length > 1) {
      return 'product_detail';
    }
    
    return segments.join('_');
  };

  return (
    <AnalyticsContext.Provider value={{
      trackAction,
      trackProductImpressions,
      trackProductDetailView,
      trackAddToCartAction,
      trackCheckoutStartAction,
      trackPurchaseAction,
      trackSearchAction
    }}>
      {children}
    </AnalyticsContext.Provider>
  );
};

// Custom hook to use analytics
export const useAnalytics = () => {
  const context = useContext(AnalyticsContext);
  if (!context) {
    throw new Error('useAnalytics must be used within an AnalyticsProvider');
  }
  return context;
};

export default AnalyticsContext;
