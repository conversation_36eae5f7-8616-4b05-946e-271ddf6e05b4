import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ShoppingCartIcon,
  TrashIcon,
  PlusIcon,
  MinusIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { useCart } from '../../contexts/CartContext.jsx';



const InteractiveCart = ({ isOpen, onClose }) => {
  const {
    cart,
    cartSummary,
    isUpdating,
    updateQuantity,
    removeFromCart,
    clearCart,
    isEmpty
  } = useCart();

  const empty = typeof isEmpty === 'function'
    ? isEmpty()
    : (cart?.lines?.length || cart?.items?.length || 0) === 0;

  const [updatingItems, setUpdatingItems] = useState(new Set());

  const handleQuantityChange = async (lineId, newQuantity) => {
    setUpdatingItems(prev => new Set(prev).add(lineId));
    try {
      await updateQuantity(lineId, newQuantity);
    } finally {
      setUpdatingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(lineId);
        return newSet;
      });
    }
  };

  const handleRemoveItem = async (lineId) => {
    setUpdatingItems(prev => new Set(prev).add(lineId));
    try {
      await removeFromCart(lineId);
    } finally {
      setUpdatingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(lineId);
        return newSet;
      });
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 overflow-hidden"
      >
        {/* Backdrop */}
        <div 
          className="absolute inset-0 bg-black bg-opacity-50"
          onClick={onClose}
        />
        
        {/* Cart Panel */}
        <motion.div
          initial={{ x: '100%' }}
          animate={{ x: 0 }}
          exit={{ x: '100%' }}
          transition={{ type: 'spring', damping: 25, stiffness: 200 }}
          className="absolute right-0 top-0 h-full w-full max-w-md bg-white shadow-xl"
        >
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 p-4">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <ShoppingCartIcon className="h-5 w-5 mr-2" />
              Shopping Cart
              {cartSummary.itemCount > 0 && (
                <span className="ml-2 bg-primary-600 text-white text-xs rounded-full px-2 py-1">
                  {cartSummary.itemCount}
                </span>
              )}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Cart Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {empty ? (
              <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                <ShoppingCartIcon className="h-16 w-16 mb-4 text-gray-300" />
                <h3 className="text-lg font-medium mb-2">Your cart is empty</h3>
                <p className="text-sm text-center">
                  Add some products to get started!
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                <AnimatePresence>
                  {cart?.lines?.map((line) => (
                    <motion.div
                      key={line.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, x: -100 }}
                      className={`bg-white border border-gray-200 rounded-lg p-4 ${
                        updatingItems.has(line.id) ? 'opacity-50' : ''
                      }`}
                    >
                      <div className="flex items-start space-x-4">
                        {/* Product Image */}
                        <div className="flex-shrink-0">
                          <img
                            src={line.variant.product.thumbnail?.url || '/placeholder-image.jpg'}
                            alt={line.variant.product.name}
                            className="h-16 w-16 object-cover rounded-md"
                          />
                        </div>

                        {/* Product Info */}
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-gray-900 truncate">
                            {line.variant.product.name}
                          </h4>
                          {line.variant.name !== 'Default' && (
                            <p className="text-xs text-gray-500 mt-1">
                              {line.variant.name}
                            </p>
                          )}
                          <p className="text-sm font-medium text-gray-900 mt-1">
                            ${line.variant.pricing.price.gross.amount}
                          </p>
                        </div>

                        {/* Remove Button */}
                        <button
                          onClick={() => handleRemoveItem(line.id)}
                          disabled={updatingItems.has(line.id)}
                          className="text-gray-400 hover:text-red-500 transition-colors disabled:opacity-50"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center justify-between mt-4">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleQuantityChange(line.id, line.quantity - 1)}
                            disabled={line.quantity <= 1 || updatingItems.has(line.id)}
                            className="p-1 rounded-full border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            <MinusIcon className="h-4 w-4" />
                          </button>
                          
                          <span className="text-sm font-medium min-w-[2rem] text-center">
                            {updatingItems.has(line.id) ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600 mx-auto" />
                            ) : (
                              line.quantity
                            )}
                          </span>
                          
                          <button
                            onClick={() => handleQuantityChange(line.id, line.quantity + 1)}
                            disabled={updatingItems.has(line.id)}
                            className="p-1 rounded-full border border-gray-300 hover:bg-gray-50 disabled:opacity-50"
                          >
                            <PlusIcon className="h-4 w-4" />
                          </button>
                        </div>

                        <div className="text-sm font-medium text-gray-900">
                          ${line.totalPrice.gross.amount}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>

                {/* Clear Cart Button */}
                {!empty && (
                  <motion.button
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    onClick={() => clearCart()}
                    disabled={isUpdating}
                    className="w-full text-sm text-red-600 hover:text-red-800 py-2 disabled:opacity-50"
                  >
                    Clear Cart
                  </motion.button>
                )}
              </div>
            )}
          </div>

          {/* Footer */}
          {!empty && (
            <motion.div
              initial={{ y: 100 }}
              animate={{ y: 0 }}
              className="border-t border-gray-200 p-4 space-y-4"
            >
              {/* Total */}
              <div className="flex justify-between items-center text-lg font-semibold">
                <span>Total:</span>
                <span>${cartSummary.totalPrice}</span>
              </div>

              {/* Checkout Button */}
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                disabled={isUpdating}
                className="w-full bg-primary-600 text-white py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors disabled:opacity-50"
              >
                {isUpdating ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" />
                    Updating...
                  </div>
                ) : (
                  'Proceed to Checkout'
                )}
              </motion.button>
            </motion.div>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default InteractiveCart;
