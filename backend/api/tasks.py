from __future__ import annotations
import logging
from typing import Any, Iterable, Optional

from django.conf import settings
from django.core.mail import send_mail
from django.contrib.auth import get_user_model
from django.db import transaction
from django.core.cache import cache
from django.template.loader import render_to_string
from django.utils.html import strip_tags

# Best-effort import: allow code to run even if Celery isn't installed yet.
from django.db import models

try:
    from celery import shared_task
except Exception:  # pragma: no cover - fallback when Celery not available
    def shared_task(*dargs, **dkwargs):
        def _decorator(func):
            return func
        # Used as @shared_task or @shared_task(...)
        if dargs and callable(dargs[0]) and not dkwargs:
            return _decorator(dargs[0])
        return _decorator

logger = logging.getLogger("api")  # use existing 'api' logger to avoid changing LOGGING


def schedule(func, *args, **kwargs):
    """Schedule a task if Celery is available, otherwise run synchronously.

    This provides a safe fallback so the app doesn't crash when Celer<PERSON>
    is not installed or not running. In that case, work executes inline.
    """
    try:
        delay = getattr(func, "delay", None)
        if callable(delay):
            return delay(*args, **kwargs)
        # Fallback: run synchronously
        return func(*args, **kwargs)
    except Exception as e:  # log and swallow to keep request path resilient
        logger.exception(f"Task scheduling/execution failed for {func.__name__}: {e}")
        return None


def schedule_reco_rebuild(user_id: int, event: str = "generic", debounce_seconds: Optional[int] = None) -> bool:
    """Debounced scheduling for user recommendation rebuilds.

    - event: logical trigger type (e.g., 'view', 'cart_change', 'checkout', 'payment_succeeded')
    Uses per-event debounce windows from settings.RECOMMENDATION_REBUILD_DEBOUNCE_WINDOWS,
    falling back to RECOMMENDATION_REBUILD_DEBOUNCE_SECONDS.
    Returns True if a rebuild was scheduled (or executed inline), False if debounced.
    """
    try:
        windows = getattr(settings, "RECOMMENDATION_REBUILD_DEBOUNCE_WINDOWS", {}) or {}
        default_ttl = int(getattr(settings, "RECOMMENDATION_REBUILD_DEBOUNCE_SECONDS", 600))
        ttl = int(debounce_seconds if debounce_seconds is not None else windows.get(event, default_ttl))
        key = f"recommendations:rebuild:debounce:{event}:{user_id}"
        if cache.get(key):
            logger.debug({
                "event": "recommendations_debounced",
                "user_id": str(user_id),
                "trigger": event,
                "ttl": ttl,
            })
            return False
        cache.set(key, True, ttl)
        schedule(build_user_recommendations, user_id)
        return True
    except Exception as e:
        # Fallback: try to run inline if anything above fails
        logger.warning(f"schedule_reco_rebuild fallback for user {user_id} ({event}): {e}")
        try:
            build_user_recommendations(user_id)
            return True
        except Exception:
            logger.exception(f"Inline recommendation build failed for user {user_id} ({event})")
            return False



@shared_task
def send_order_confirmation_email(order_id: int) -> None:
    from .models import Order

    try:
        order = Order.objects.select_related("user").get(id=order_id)
    except Order.DoesNotExist:
        logger.warning(f"send_order_confirmation_email: Order {order_id} does not exist")
        return

    user = order.user
    ctx = {
        "site_name": getattr(settings, "SITE_NAME", "MyEcommerce"),
        "frontend_url": getattr(settings, "FRONTEND_URL", ""),
        "order": order,
        "user": user,
    }
    subject = f"Order Confirmation #{order.id}"
    try:
        html = render_to_string("emails/order_confirmation.html", ctx)
        plain = strip_tags(html)
        send_mail(subject, plain, getattr(settings, "DEFAULT_FROM_EMAIL", None), [user.email], html_message=html, fail_silently=True)
    except Exception:
        # Fallback to plain if template fails
        message = (
            f"Hello {getattr(user, 'first_name', '') or user.username},\n\n"
            f"Thank you for your purchase! Your order #{order.id} has been created.\n"
            f"Total: {order.total_price}. We'll notify you once it's paid and shipped.\n\n"
            f"View your order: {settings.FRONTEND_URL}/orders/{order.id}"
        )
        _send_email_safely(subject, message, [user.email])
    logger.info(f"Order confirmation email enqueued/sent for order_id={order_id}")


@shared_task
def send_payment_status_notification(order_id: int, status: str) -> None:
    from .models import Order

    try:
        order = Order.objects.select_related("user").get(id=order_id)
    except Order.DoesNotExist:
        logger.warning(f"send_payment_status_notification: Order {order_id} does not exist")
        return

    user = order.user
    ctx = {
        "site_name": getattr(settings, "SITE_NAME", "MyEcommerce"),
        "frontend_url": getattr(settings, "FRONTEND_URL", ""),
        "order": order,
        "user": user,
        "status": status,
    }
    subject = f"Payment {status.title()} for Order #{order.id}"
    try:
        html = render_to_string("emails/payment_confirmation.html", ctx)
        plain = strip_tags(html)
        send_mail(subject, plain, getattr(settings, "DEFAULT_FROM_EMAIL", None), [user.email], html_message=html, fail_silently=True)
    except Exception:
        message = (
            f"Hello {getattr(user, 'first_name', '') or user.username},\n\n"
            f"Your payment status for order #{order.id} is: {status}.\n"
            f"Total: {order.total_price}.\n\n"
            f"View your order: {settings.FRONTEND_URL}/orders/{order.id}"
        )
        _send_email_safely(subject, message, [user.email])
    logger.info(f"Payment notification '{status}' enqueued/sent for order_id={order_id}")


@shared_task
def notify_low_stock_alert(alert_id: int) -> None:
    from .models import InventoryAlert

    try:
        alert = (
            InventoryAlert.objects.select_related("product")
            .only("id", "title", "message", "priority", "product_id")
            .get(id=alert_id)
        )
    except InventoryAlert.DoesNotExist:
        logger.warning(f"notify_low_stock_alert: InventoryAlert {alert_id} does not exist")
        return

    # Target recipients: all managers (group name 'manager') or staff as fallback
    User = get_user_model()
    manager_qs = User.objects.filter(is_active=True).filter(
        models.Q(is_staff=True) | models.Q(groups__name__iexact="manager")
    ).distinct()
    recipients = [u.email for u in manager_qs if u.email]

    if not recipients:
        logger.info("No manager/staff recipients found for low stock alert; skipping email")
        return

    subject = f"[Inventory] {alert.title}"
    message = alert.message
    _send_email_safely(subject, message, recipients)
    logger.info(f"Low stock alert emailed for alert_id={alert.id}, recipients={len(recipients)}")


@shared_task
def send_push_notification(user_id: int, title: str, message: str, metadata: Optional[dict[str, Any]] = None) -> None:
    # Placeholder: integrate with FCM/APNS/OneSignal later. For now, log.
    User = get_user_model()
    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        logger.warning(f"send_push_notification: user {user_id} not found")
        return

    logger.info(
        {
            "event": "push_notification",
            "user_id": user.id,
            "title": title,
            "message": message,
            "metadata": metadata or {},
        }
    )


def _send_email_safely(subject: str, message: str, recipients: Iterable[str]) -> None:
    if not recipients:
        return
    from_email = getattr(settings, "NOTIFICATIONS_FROM_EMAIL", "<EMAIL>")
    try:
        send_mail(subject, message, from_email, list(recipients), fail_silently=True)
    except Exception:
        logger.exception("Failed to send email (silenced)")



# =============================
# Recommendation builder tasks
# =============================
from collections import defaultdict
from datetime import timedelta
from django.utils import timezone

@shared_task
def build_user_recommendations(user_id, days: int = 30, limit: int = 20) -> None:
    """Build personalized product recommendations for a single user.

    Combines signals from:
    - Google Analytics (if configured): product views per user
    - In-app UserBehavior: views, clicks, add_to_cart, purchase
    - Active cart contents
    - Recent order history for simple affinity
    Stores results in PersonalizedRecommendation.
    """
    from .models import (
        User, Product, Cart, CartItem, Order, OrderLine, UserBehavior, PersonalizedRecommendation
    )
    from .ga_client import get_user_ga_product_signals

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        logger.warning(f"build_user_recommendations: user {user_id} not found")
        return

    now = timezone.now()
    since = now - timedelta(days=days)

    # 1) Initialize score map
    scores: dict[str, float] = defaultdict(float)

    # 2) GA signals (optional)
    ga_signals = get_user_ga_product_signals(user, days=days)  # {product_id_or_slug: view_count}

    # Create a simple mapping function to resolve product by id or slug
    def _resolve_product_id(key: str):
        try:
            # Try as UUID
            return str(Product.objects.only("id").get(id=key).id)
        except Exception:
            try:
                return str(Product.objects.only("id").get(slug=key).id)
            except Exception:
                return None

    for key, count in ga_signals.items():
        pid = _resolve_product_id(key)
        if pid:
            scores[pid] += 1.0 * float(count)

    # 3) In-app behavior
    behavior_qs = (
        UserBehavior.objects.filter(user=user, action_time__gte=since)
        .values_list("action", "product_id")
    )
    weights = {"view": 1.0, "click": 2.0, "add_to_cart": 3.0, "purchase": 4.0}
    for action, product_id in behavior_qs:
        if product_id:
            scores[str(product_id)] += weights.get(action, 0.5)

    # 4) Active cart signal
    cart = (
        Cart.objects.filter(created_by=user, status="active")
        .prefetch_related("items")
        .first()
    )
    in_cart: set[str] = set()
    if cart:
        for item in cart.items.all():
            in_cart.add(str(item.product_id))
            scores[str(item.product_id)] += 5.0 * float(item.quantity)

    # 5) Recent orders from this user to capture purchase intent
    recent_lines = (
        OrderLine.objects.filter(order__user=user, order__created_at__gte=since)
        .values_list("product_id", "quantity")
    )
    for pid, qty in recent_lines:
        scores[str(pid)] += 2.0 * float(qty)

    # 6) Category affinity boost (top categories from user's behavior/cart)
    top_product_ids = list(scores.keys())
    if top_product_ids:
        from django.db.models import Count, Sum
        cats = (
            Product.objects.filter(id__in=top_product_ids)
            .values_list("category_id", flat=True)
        )
        cat_ids = list(set(cats))
        if cat_ids:
            # Popular products in those categories by total sold recently
            popular = (
                OrderLine.objects.filter(order__created_at__gte=since, product__category_id__in=cat_ids)
                .values("product_id")
                .annotate(qty=Sum("quantity"))
                .order_by("-qty")[:50]
            )
            for row in popular:
                pid = str(row["product_id"]) if row["product_id"] else None
                if pid:
                    scores[pid] += 0.5 * float(row.get("qty") or 0)

    # 7) Remove items not sellable and exclude current cart items from recommendations
    sellable_qs = Product.objects.filter(is_active=True, stock_quantity__gt=0).only("id")
    sellable_ids = {str(p.id) for p in sellable_qs}

    # Prepare final list
    ranked = [
        (pid, score) for pid, score in scores.items() if pid in sellable_ids and pid not in in_cart
    ]
    ranked.sort(key=lambda x: x[1], reverse=True)
    ranked = ranked[:limit]

    # 8) Upsert into PersonalizedRecommendation
    from django.db import transaction
    with transaction.atomic():
        PersonalizedRecommendation.objects.filter(user=user).delete()
        recs = [
            PersonalizedRecommendation(user=user, product_id=pid, recommendation_score=score)
            for pid, score in ranked
        ]
        if recs:
            PersonalizedRecommendation.objects.bulk_create(recs, batch_size=100)

    logger.info(
        {
            "event": "recommendations_built",
            "user_id": str(user.id),
            "count": len(ranked),
        }
    )


@shared_task
def build_recommendations_for_all_users(days: int = 30, limit: int = 20) -> None:
    """Build recommendations for all active users."""
    from .models import User

    user_ids = list(User.objects.filter(is_active=True).values_list("id", flat=True))
    for uid in user_ids:
        try:
            build_user_recommendations(uid, days=days, limit=limit)
        except Exception:
            logger.exception(f"Failed building recommendations for user {uid}")
