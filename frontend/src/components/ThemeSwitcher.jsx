/**
 * Theme Switcher Component
 * 
 * Allows users to preview and switch between different theme options
 * Shows color previews for each theme
 */
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { SwatchIcon, CheckIcon } from '@heroicons/react/24/outline';

const ThemeSwitcher = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedTheme, setSelectedTheme] = useState('oceanBreeze');

  const themes = {
    modernDark: {
      name: 'Modern Dark',
      description: 'Sophisticated dark mode with purple accents',
      colors: {
        primary: '#7C5CF8',
        secondary: '#334155',
        accent: '#EC4899',
        background: '#0F172A'
      }
    },
    oceanBreeze: {
      name: 'Ocean Breeze',
      description: 'Fresh blue and teal combination',
      colors: {
        primary: '#06B6D4',
        secondary: '#0EA5E9',
        accent: '#22C55E',
        background: '#FFFFFF'
      }
    },
    sunsetGlow: {
      name: 'Sunset Glow',
      description: 'Warm oranges and reds',
      colors: {
        primary: '#F97316',
        secondary: '#EF4444',
        accent: '#F59E0B',
        background: '#FFFFFF'
      }
    },
    forestGreen: {
      name: '<PERSON> Green',
      description: 'Natural and eco-friendly',
      colors: {
        primary: '#22C55E',
        secondary: '#84CC16',
        accent: '#EAB308',
        background: '#FFFFFF'
      }
    },
    royalPurple: {
      name: 'Royal Purple',
      description: 'Elegant and luxurious',
      colors: {
        primary: '#A855F7',
        secondary: '#64748B',
        accent: '#D946EF',
        background: '#FFFFFF'
      }
    }
  };

  const handleThemeChange = (themeKey) => {
    setSelectedTheme(themeKey);
    // In a real implementation, this would update the Tailwind config
    // For now, we'll show instructions to the user
    alert(`To apply the ${themes[themeKey].name} theme:
    
1. Open frontend/tailwind.config.js
2. Change line 286: const selectedTheme = themes.${themeKey};
3. Save the file and restart your development server
4. The new theme will be applied!`);
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Theme Switcher Button */}
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-center w-12 h-12 bg-primary-600 text-white rounded-full shadow-lg hover:bg-primary-700 transition-colors"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <SwatchIcon className="w-6 h-6" />
      </motion.button>

      {/* Theme Options Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.9 }}
            className="absolute bottom-16 right-0 w-80 bg-white rounded-lg shadow-xl border border-gray-200 p-4"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Choose Theme</h3>
            
            <div className="space-y-3">
              {Object.entries(themes).map(([key, theme]) => (
                <motion.div
                  key={key}
                  onClick={() => handleThemeChange(key)}
                  className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
                    selectedTheme === key
                      ? 'border-primary-500 bg-primary-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900">{theme.name}</h4>
                    {selectedTheme === key && (
                      <CheckIcon className="w-5 h-5 text-primary-600" />
                    )}
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-3">{theme.description}</p>
                  
                  {/* Color Preview */}
                  <div className="flex space-x-2">
                    <div
                      className="w-6 h-6 rounded-full border border-gray-300"
                      style={{ backgroundColor: theme.colors.primary }}
                      title="Primary Color"
                    />
                    <div
                      className="w-6 h-6 rounded-full border border-gray-300"
                      style={{ backgroundColor: theme.colors.secondary }}
                      title="Secondary Color"
                    />
                    <div
                      className="w-6 h-6 rounded-full border border-gray-300"
                      style={{ backgroundColor: theme.colors.accent }}
                      title="Accent Color"
                    />
                    <div
                      className="w-6 h-6 rounded-full border border-gray-300"
                      style={{ backgroundColor: theme.colors.background }}
                      title="Background Color"
                    />
                  </div>
                </motion.div>
              ))}
            </div>

            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Note:</strong> After selecting a theme, follow the instructions to apply it to your application.
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ThemeSwitcher;
