{"openapi": "3.0.3", "info": {"title": "Ecommerce API", "version": "1.0.0", "description": "API documentation for the ecommerce platform", "contact": {"name": "Support", "email": ""}, "license": {"name": "Proprietary"}}, "paths": {"/": {"get": {"operationId": "root_retrieve", "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RootLinks"}}}, "description": ""}}}}, "/api/admin/orders/": {"get": {"operationId": "admin_orders_list", "parameters": [{"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}], "tags": ["admin"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedOrderList"}}}, "description": ""}}}, "post": {"operationId": "admin_orders_create", "tags": ["admin"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/OrderRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/OrderRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Order"}}}, "description": ""}}}}, "/api/admin/orders/{id}/": {"get": {"operationId": "admin_orders_retrieve", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this order.", "required": true}], "tags": ["admin"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Order"}}}, "description": ""}}}, "put": {"operationId": "admin_orders_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this order.", "required": true}], "tags": ["admin"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/OrderRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/OrderRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Order"}}}, "description": ""}}}, "patch": {"operationId": "admin_orders_partial_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this order.", "required": true}], "tags": ["admin"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedOrderRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PatchedOrderRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PatchedOrderRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Order"}}}, "description": ""}}}, "delete": {"operationId": "admin_orders_destroy", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this order.", "required": true}], "tags": ["admin"], "security": [{"jwtAuth": []}], "responses": {"204": {"description": "No response body"}}}}, "/api/admin/orders/{id}/update_status/": {"post": {"operationId": "admin_orders_update_status_create", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this order.", "required": true}], "tags": ["admin"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/OrderRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/OrderRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Order"}}}, "description": ""}}}}, "/api/analytics/general/": {"get": {"operationId": "analytics_general_retrieve", "description": "API view for general analytics data (admin/manager only)", "parameters": [{"in": "query", "name": "range", "schema": {"type": "string"}, "description": "Time range: day|week|month|year"}], "tags": ["analytics"], "security": [{"jwtAuth": []}], "responses": {"200": {"description": "General analytics data"}}}}, "/api/analytics/user/": {"get": {"operationId": "analytics_user_retrieve", "description": "API view for individual user analytics", "parameters": [{"in": "query", "name": "range", "schema": {"type": "string"}, "description": "Time range: day|week|month|year"}], "tags": ["analytics"], "security": [{"jwtAuth": []}], "responses": {"200": {"description": "User analytics data"}}}}, "/api/analytics/user/{user_id}/": {"get": {"operationId": "analytics_user_retrieve_2", "description": "API view for individual user analytics", "parameters": [{"in": "query", "name": "range", "schema": {"type": "string"}, "description": "Time range: day|week|month|year"}, {"in": "path", "name": "user_id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["analytics"], "security": [{"jwtAuth": []}], "responses": {"200": {"description": "User analytics data"}}}}, "/api/apply_promo/": {"post": {"operationId": "apply_promo_create", "tags": ["apply_promo"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromoCodeApplyRequestRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PromoCodeApplyRequestRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PromoCodeApplyRequestRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromoCodeApplyResponse"}}}, "description": ""}}}}, "/api/auth/csrf-token/": {"get": {"operationId": "auth_csrf_token_retrieve", "description": "View to get CSRF token for frontend applications.", "tags": ["auth"], "security": [{"jwtAuth": []}, {}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CSRFTok"}}}, "description": ""}}}}, "/api/auth/forgot-password/": {"post": {"operationId": "auth_forgot_password_create", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequestRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequestRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequestRequest"}}}, "required": true}, "security": [{"jwtAuth": []}, {}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordResponse"}}}, "description": ""}}}}, "/api/auth/google/": {"post": {"operationId": "auth_google_create", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoogleAuthRequestRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/GoogleAuthRequestRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/GoogleAuthRequestRequest"}}}, "required": true}, "security": [{"jwtAuth": []}, {}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoogleAuthResponse"}}}, "description": ""}, "400": {"description": "Invalid token or misconfiguration"}}}}, "/api/auth/login/": {"post": {"operationId": "auth_login_create", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequestRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/LoginRequestRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/LoginRequestRequest"}}}, "required": true}, "security": [{"jwtAuth": []}, {}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}, "description": ""}, "401": {"description": "Invalid credentials"}}}}, "/api/auth/refresh/": {"post": {"operationId": "auth_refresh_create", "description": "Takes a refresh type JSON web token and returns an access type JSON web\ntoken if the refresh token is valid.", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenRefreshRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/TokenRefreshRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/TokenRefreshRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenRefresh"}}}, "description": ""}}}}, "/api/auth/register/": {"post": {"operationId": "auth_register_create", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRegistrationRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/UserRegistrationRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/UserRegistrationRequest"}}}, "required": true}, "security": [{"jwtAuth": []}, {}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterResponse"}}}, "description": ""}, "400": {"description": "Validation error"}}}}, "/api/auth/reset-password/": {"post": {"operationId": "auth_reset_password_create", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequestRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequestRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequestRequest"}}}, "required": true}, "security": [{"jwtAuth": []}, {}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordResponse"}}}, "description": ""}, "400": {"description": "Invalid token or weak password"}}}}, "/api/carts/": {"get": {"operationId": "carts_list", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}, {"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}], "tags": ["carts"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedCartList"}}}, "description": ""}}}, "post": {"operationId": "carts_create", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["carts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CartRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/CartRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/CartRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cart"}}}, "description": ""}}}}, "/api/carts/{id}/": {"get": {"operationId": "carts_retrieve", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["carts"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cart"}}}, "description": ""}}}, "put": {"operationId": "carts_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["carts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CartRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/CartRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/CartRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cart"}}}, "description": ""}}}, "patch": {"operationId": "carts_partial_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["carts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedCartRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PatchedCartRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PatchedCartRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cart"}}}, "description": ""}}}, "delete": {"operationId": "carts_destroy", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["carts"], "security": [{"jwtAuth": []}], "responses": {"204": {"description": "No response body"}}}}, "/api/carts/{id}/add_item/": {"post": {"operationId": "carts_add_item_create", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["carts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CartRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/CartRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/CartRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cart"}}}, "description": ""}}}}, "/api/carts/{id}/checkout/": {"post": {"operationId": "carts_checkout_create", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["carts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CartRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/CartRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/CartRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cart"}}}, "description": ""}}}}, "/api/carts/{id}/remove_item/": {"post": {"operationId": "carts_remove_item_create", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["carts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CartRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/CartRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/CartRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cart"}}}, "description": ""}}}}, "/api/carts/{id}/update_item/": {"post": {"operationId": "carts_update_item_create", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["carts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CartRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/CartRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/CartRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cart"}}}, "description": ""}}}}, "/api/categories/": {"get": {"operationId": "categories_list", "parameters": [{"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}, {"name": "search", "required": false, "in": "query", "description": "A search term.", "schema": {"type": "string"}}], "tags": ["categories"], "security": [{"jwtAuth": []}, {}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedCategoryList"}}}, "description": ""}}}, "post": {"operationId": "categories_create", "tags": ["categories"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/CategoryRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/CategoryRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Category"}}}, "description": ""}}}}, "/api/categories/{id}/": {"get": {"operationId": "categories_retrieve", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this category.", "required": true}], "tags": ["categories"], "security": [{"jwtAuth": []}, {}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Category"}}}, "description": ""}}}, "put": {"operationId": "categories_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this category.", "required": true}], "tags": ["categories"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/CategoryRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/CategoryRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Category"}}}, "description": ""}}}, "patch": {"operationId": "categories_partial_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this category.", "required": true}], "tags": ["categories"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedCategoryRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PatchedCategoryRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PatchedCategoryRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Category"}}}, "description": ""}}}, "delete": {"operationId": "categories_destroy", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this category.", "required": true}], "tags": ["categories"], "security": [{"jwtAuth": []}], "responses": {"204": {"description": "No response body"}}}}, "/api/categories/{id}/products/": {"get": {"operationId": "categories_products_retrieve", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this category.", "required": true}], "tags": ["categories"], "security": [{"jwtAuth": []}, {}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Category"}}}, "description": ""}}}}, "/api/home/": {"get": {"operationId": "home_retrieve", "tags": ["home"], "security": [{"jwtAuth": []}, {}], "responses": {"200": {"description": "Home page data"}}}}, "/api/inventory/": {"get": {"operationId": "inventory_list", "description": "ViewSet for managing product inventory.\n\nThis ViewSet provides CRUD operations for inventory management:\n- List all inventory changes with filtering and search\n- Create new inventory changes (add/subtract stock)\n- Retrieve details of specific inventory changes\n- Generate inventory reports and statistics\n\nOnly managers and admins can access these endpoints.", "parameters": [{"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}, {"name": "search", "required": false, "in": "query", "description": "A search term.", "schema": {"type": "string"}}], "tags": ["inventory"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedInventoryChangeList"}}}, "description": ""}}}, "post": {"operationId": "inventory_create", "description": "ViewSet for managing product inventory.\n\nThis ViewSet provides CRUD operations for inventory management:\n- List all inventory changes with filtering and search\n- Create new inventory changes (add/subtract stock)\n- Retrieve details of specific inventory changes\n- Generate inventory reports and statistics\n\nOnly managers and admins can access these endpoints.", "tags": ["inventory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryChangeRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/InventoryChangeRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/InventoryChangeRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryChange"}}}, "description": ""}}}}, "/api/inventory-alerts/": {"get": {"operationId": "inventory_alerts_list", "description": "ViewSet for managing inventory alerts and notifications.\n\nProvides CRUD operations for inventory alerts with filtering and actions.\nOnly managers and admins can access these endpoints.", "parameters": [{"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}, {"name": "search", "required": false, "in": "query", "description": "A search term.", "schema": {"type": "string"}}], "tags": ["inventory-alerts"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedInventoryAlertList"}}}, "description": ""}}}, "post": {"operationId": "inventory_alerts_create", "description": "ViewSet for managing inventory alerts and notifications.\n\nProvides CRUD operations for inventory alerts with filtering and actions.\nOnly managers and admins can access these endpoints.", "tags": ["inventory-alerts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryAlertRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/InventoryAlertRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/InventoryAlertRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryAlert"}}}, "description": ""}}}}, "/api/inventory-alerts/{id}/": {"get": {"operationId": "inventory_alerts_retrieve", "description": "ViewSet for managing inventory alerts and notifications.\n\nProvides CRUD operations for inventory alerts with filtering and actions.\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this inventory alert.", "required": true}], "tags": ["inventory-alerts"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryAlert"}}}, "description": ""}}}, "put": {"operationId": "inventory_alerts_update", "description": "ViewSet for managing inventory alerts and notifications.\n\nProvides CRUD operations for inventory alerts with filtering and actions.\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this inventory alert.", "required": true}], "tags": ["inventory-alerts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryAlertRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/InventoryAlertRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/InventoryAlertRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryAlert"}}}, "description": ""}}}, "patch": {"operationId": "inventory_alerts_partial_update", "description": "ViewSet for managing inventory alerts and notifications.\n\nProvides CRUD operations for inventory alerts with filtering and actions.\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this inventory alert.", "required": true}], "tags": ["inventory-alerts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedInventoryAlertRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PatchedInventoryAlertRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PatchedInventoryAlertRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryAlert"}}}, "description": ""}}}, "delete": {"operationId": "inventory_alerts_destroy", "description": "ViewSet for managing inventory alerts and notifications.\n\nProvides CRUD operations for inventory alerts with filtering and actions.\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this inventory alert.", "required": true}], "tags": ["inventory-alerts"], "security": [{"jwtAuth": []}], "responses": {"204": {"description": "No response body"}}}}, "/api/inventory-alerts/{id}/acknowledge/": {"post": {"operationId": "inventory_alerts_acknowledge_create", "description": "Acknowledge an alert", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this inventory alert.", "required": true}], "tags": ["inventory-alerts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryAlertRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/InventoryAlertRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/InventoryAlertRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryAlert"}}}, "description": ""}}}}, "/api/inventory-alerts/{id}/dismiss/": {"post": {"operationId": "inventory_alerts_dismiss_create", "description": "Dismiss/deactivate an alert", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this inventory alert.", "required": true}], "tags": ["inventory-alerts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryAlertRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/InventoryAlertRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/InventoryAlertRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryAlert"}}}, "description": ""}}}}, "/api/inventory-alerts/active_alerts/": {"get": {"operationId": "inventory_alerts_active_alerts_retrieve", "description": "Get all active, unacknowledged alerts", "tags": ["inventory-alerts"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryAlert"}}}, "description": ""}}}}, "/api/inventory-alerts/bulk_acknowledge/": {"post": {"operationId": "inventory_alerts_bulk_acknowledge_create", "description": "Acknowledge multiple alerts", "tags": ["inventory-alerts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryAlertRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/InventoryAlertRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/InventoryAlertRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryAlert"}}}, "description": ""}}}}, "/api/inventory-alerts/bulk_dismiss/": {"post": {"operationId": "inventory_alerts_bulk_dismiss_create", "description": "Dismiss multiple alerts", "tags": ["inventory-alerts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryAlertRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/InventoryAlertRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/InventoryAlertRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryAlert"}}}, "description": ""}}}}, "/api/inventory-alerts/critical_alerts/": {"get": {"operationId": "inventory_alerts_critical_alerts_retrieve", "description": "Get all critical priority alerts", "tags": ["inventory-alerts"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryAlert"}}}, "description": ""}}}}, "/api/inventory-alerts/dashboard_summary/": {"get": {"operationId": "inventory_alerts_dashboard_summary_retrieve", "description": "Get alert dashboard summary with counts by type and priority", "tags": ["inventory-alerts"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryAlert"}}}, "description": ""}}}}, "/api/inventory-batches/": {"get": {"operationId": "inventory_batches_list", "description": "ViewSet for managing inventory batches.\n\nProvides CRUD operations for batch-level inventory management.\nOnly managers and admins can access these endpoints.", "parameters": [{"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}, {"name": "search", "required": false, "in": "query", "description": "A search term.", "schema": {"type": "string"}}], "tags": ["inventory-batches"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedInventoryBatchList"}}}, "description": ""}}}, "post": {"operationId": "inventory_batches_create", "description": "ViewSet for managing inventory batches.\n\nProvides CRUD operations for batch-level inventory management.\nOnly managers and admins can access these endpoints.", "tags": ["inventory-batches"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryBatchRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/InventoryBatchRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/InventoryBatchRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryBatch"}}}, "description": ""}}}}, "/api/inventory-batches/{id}/": {"get": {"operationId": "inventory_batches_retrieve", "description": "ViewSet for managing inventory batches.\n\nProvides CRUD operations for batch-level inventory management.\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this inventory batch.", "required": true}], "tags": ["inventory-batches"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryBatch"}}}, "description": ""}}}, "put": {"operationId": "inventory_batches_update", "description": "ViewSet for managing inventory batches.\n\nProvides CRUD operations for batch-level inventory management.\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this inventory batch.", "required": true}], "tags": ["inventory-batches"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryBatchRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/InventoryBatchRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/InventoryBatchRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryBatch"}}}, "description": ""}}}, "patch": {"operationId": "inventory_batches_partial_update", "description": "ViewSet for managing inventory batches.\n\nProvides CRUD operations for batch-level inventory management.\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this inventory batch.", "required": true}], "tags": ["inventory-batches"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedInventoryBatchRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PatchedInventoryBatchRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PatchedInventoryBatchRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryBatch"}}}, "description": ""}}}, "delete": {"operationId": "inventory_batches_destroy", "description": "ViewSet for managing inventory batches.\n\nProvides CRUD operations for batch-level inventory management.\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this inventory batch.", "required": true}], "tags": ["inventory-batches"], "security": [{"jwtAuth": []}], "responses": {"204": {"description": "No response body"}}}}, "/api/inventory-batches/expired/": {"get": {"operationId": "inventory_batches_expired_retrieve", "description": "Get all expired batches with remaining stock", "tags": ["inventory-batches"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryBatch"}}}, "description": ""}}}}, "/api/inventory-batches/expiring_soon/": {"get": {"operationId": "inventory_batches_expiring_soon_retrieve", "description": "Get batches expiring within specified days", "tags": ["inventory-batches"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryBatch"}}}, "description": ""}}}}, "/api/inventory/{id}/": {"get": {"operationId": "inventory_retrieve", "description": "ViewSet for managing product inventory.\n\nThis ViewSet provides CRUD operations for inventory management:\n- List all inventory changes with filtering and search\n- Create new inventory changes (add/subtract stock)\n- Retrieve details of specific inventory changes\n- Generate inventory reports and statistics\n\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this inventory change.", "required": true}], "tags": ["inventory"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryChange"}}}, "description": ""}}}, "put": {"operationId": "inventory_update", "description": "ViewSet for managing product inventory.\n\nThis ViewSet provides CRUD operations for inventory management:\n- List all inventory changes with filtering and search\n- Create new inventory changes (add/subtract stock)\n- Retrieve details of specific inventory changes\n- Generate inventory reports and statistics\n\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this inventory change.", "required": true}], "tags": ["inventory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryChangeRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/InventoryChangeRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/InventoryChangeRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryChange"}}}, "description": ""}}}, "patch": {"operationId": "inventory_partial_update", "description": "ViewSet for managing product inventory.\n\nThis ViewSet provides CRUD operations for inventory management:\n- List all inventory changes with filtering and search\n- Create new inventory changes (add/subtract stock)\n- Retrieve details of specific inventory changes\n- Generate inventory reports and statistics\n\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this inventory change.", "required": true}], "tags": ["inventory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedInventoryChangeRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PatchedInventoryChangeRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PatchedInventoryChangeRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryChange"}}}, "description": ""}}}, "delete": {"operationId": "inventory_destroy", "description": "ViewSet for managing product inventory.\n\nThis ViewSet provides CRUD operations for inventory management:\n- List all inventory changes with filtering and search\n- Create new inventory changes (add/subtract stock)\n- Retrieve details of specific inventory changes\n- Generate inventory reports and statistics\n\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this inventory change.", "required": true}], "tags": ["inventory"], "security": [{"jwtAuth": []}], "responses": {"204": {"description": "No response body"}}}}, "/api/inventory/summary/": {"get": {"operationId": "inventory_summary_retrieve", "description": "Get inventory summary statistics.\n\nThis endpoint provides a dashboard overview of inventory status:\n- Count of products with low stock (less than 10 items)\n- Total value of all inventory in stock\n- Recent inventory changes for activity monitoring\n\nReturns:\n    Response with summary statistics in JSON format", "tags": ["inventory"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryChange"}}}, "description": ""}}}}, "/api/moderation/": {"get": {"operationId": "moderation_list", "parameters": [{"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}], "tags": ["moderation"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedProductModerationLogList"}}}, "description": ""}}}, "post": {"operationId": "moderation_create", "tags": ["moderation"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductModerationLogRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ProductModerationLogRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/ProductModerationLogRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductModerationLog"}}}, "description": ""}}}}, "/api/moderation/{id}/": {"get": {"operationId": "moderation_retrieve", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this product moderation log.", "required": true}], "tags": ["moderation"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductModerationLog"}}}, "description": ""}}}, "put": {"operationId": "moderation_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this product moderation log.", "required": true}], "tags": ["moderation"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductModerationLogRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ProductModerationLogRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/ProductModerationLogRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductModerationLog"}}}, "description": ""}}}, "patch": {"operationId": "moderation_partial_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this product moderation log.", "required": true}], "tags": ["moderation"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedProductModerationLogRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PatchedProductModerationLogRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PatchedProductModerationLogRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductModerationLog"}}}, "description": ""}}}, "delete": {"operationId": "moderation_destroy", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this product moderation log.", "required": true}], "tags": ["moderation"], "security": [{"jwtAuth": []}], "responses": {"204": {"description": "No response body"}}}}, "/api/moderation/moderate/": {"post": {"operationId": "moderation_moderate_create", "tags": ["moderation"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductModerationLogRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ProductModerationLogRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/ProductModerationLogRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductModerationLog"}}}, "description": ""}}}}, "/api/offers/": {"get": {"operationId": "offers_list", "parameters": [{"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}], "tags": ["offers"], "security": [{"jwtAuth": []}, {}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedOfferList"}}}, "description": ""}}}, "post": {"operationId": "offers_create", "tags": ["offers"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OfferRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/OfferRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/OfferRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Offer"}}}, "description": ""}}}}, "/api/offers/{id}/": {"get": {"operationId": "offers_retrieve", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this offer.", "required": true}], "tags": ["offers"], "security": [{"jwtAuth": []}, {}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Offer"}}}, "description": ""}}}, "put": {"operationId": "offers_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this offer.", "required": true}], "tags": ["offers"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OfferRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/OfferRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/OfferRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Offer"}}}, "description": ""}}}, "patch": {"operationId": "offers_partial_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this offer.", "required": true}], "tags": ["offers"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedOfferRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PatchedOfferRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PatchedOfferRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Offer"}}}, "description": ""}}}, "delete": {"operationId": "offers_destroy", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this offer.", "required": true}], "tags": ["offers"], "security": [{"jwtAuth": []}], "responses": {"204": {"description": "No response body"}}}}, "/api/offers/active/": {"get": {"operationId": "offers_active_retrieve", "tags": ["offers"], "security": [{"jwtAuth": []}, {}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Offer"}}}, "description": ""}}}}, "/api/orders/": {"get": {"operationId": "orders_list", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}, {"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}], "tags": ["orders"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedOrderList"}}}, "description": ""}}}}, "/api/orders/{id}/": {"get": {"operationId": "orders_retrieve", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["orders"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Order"}}}, "description": ""}}}}, "/api/orders/{id}/cancel/": {"post": {"operationId": "orders_cancel_create", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/OrderRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/OrderRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Order"}}}, "description": ""}}}}, "/api/payment/": {"post": {"operationId": "payment_create", "tags": ["payment"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentProcessingRequestRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PaymentProcessingRequestRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PaymentProcessingRequestRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentProcessingResponse"}}}, "description": ""}, "400": {"description": "Bad request"}, "404": {"description": "Order not found"}}}}, "/api/payment/enhanced/": {"post": {"operationId": "payment_enhanced_create", "description": "Enhanced payment processing view with customer and payment method management.\n\nSupports:\n- Creating payment intents with saved customers\n- Using saved payment methods\n- Saving payment methods for future use", "tags": ["payment"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnhancedPaymentProcessingRequestRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/EnhancedPaymentProcessingRequestRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/EnhancedPaymentProcessingRequestRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnhancedPaymentProcessingResponse"}}}, "description": ""}, "400": {"description": "Bad request"}, "404": {"description": "Order not found"}}}}, "/api/payments/analytics/": {"get": {"operationId": "payments_analytics_retrieve", "description": "Get payment analytics based on query parameters.", "tags": ["payments"], "security": [{"jwtAuth": []}], "responses": {"200": {"description": "Payment analytics overview"}}}}, "/api/payments/history/": {"get": {"operationId": "payments_history_retrieve", "description": "Get payment history for the current user or all payments for managers.", "tags": ["payments"], "security": [{"jwtAuth": []}], "responses": {"200": {"description": "Payment history list"}}}}, "/api/product-images/": {"get": {"operationId": "product_images_list", "description": "ViewSet for managing product images", "parameters": [{"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}], "tags": ["product-images"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedProductImageList"}}}, "description": ""}}}, "post": {"operationId": "product_images_create", "description": "ViewSet for managing product images", "tags": ["product-images"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductImageRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ProductImageRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/ProductImageRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductImage"}}}, "description": ""}}}}, "/api/product-images/{id}/": {"get": {"operationId": "product_images_retrieve", "description": "ViewSet for managing product images", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this product image.", "required": true}], "tags": ["product-images"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductImage"}}}, "description": ""}}}, "put": {"operationId": "product_images_update", "description": "ViewSet for managing product images", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this product image.", "required": true}], "tags": ["product-images"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductImageRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ProductImageRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/ProductImageRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductImage"}}}, "description": ""}}}, "patch": {"operationId": "product_images_partial_update", "description": "ViewSet for managing product images", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this product image.", "required": true}], "tags": ["product-images"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedProductImageRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PatchedProductImageRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PatchedProductImageRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductImage"}}}, "description": ""}}}, "delete": {"operationId": "product_images_destroy", "description": "ViewSet for managing product images", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this product image.", "required": true}], "tags": ["product-images"], "security": [{"jwtAuth": []}], "responses": {"204": {"description": "No response body"}}}}, "/api/products/": {"get": {"operationId": "products_list", "parameters": [{"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}, {"name": "search", "required": false, "in": "query", "description": "A search term.", "schema": {"type": "string"}}], "tags": ["products"], "security": [{"jwtAuth": []}, {}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedProductList"}}}, "description": ""}}}, "post": {"operationId": "products_create", "tags": ["products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ProductRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/ProductRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}, "description": ""}}}}, "/api/products/{id}/": {"get": {"operationId": "products_retrieve", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this product.", "required": true}], "tags": ["products"], "security": [{"jwtAuth": []}, {}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}, "description": ""}}}, "put": {"operationId": "products_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this product.", "required": true}], "tags": ["products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ProductRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/ProductRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}, "description": ""}}}, "patch": {"operationId": "products_partial_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this product.", "required": true}], "tags": ["products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedProductRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PatchedProductRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PatchedProductRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}, "description": ""}}}, "delete": {"operationId": "products_destroy", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this product.", "required": true}], "tags": ["products"], "security": [{"jwtAuth": []}], "responses": {"204": {"description": "No response body"}}}}, "/api/products/{id}/rating/": {"get": {"operationId": "products_rating_retrieve", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this product.", "required": true}], "tags": ["products"], "security": [{"jwtAuth": []}, {}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}, "description": ""}}}}, "/api/products/{id}/reviews/": {"get": {"operationId": "products_reviews_retrieve", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this product.", "required": true}], "tags": ["products"], "security": [{"jwtAuth": []}, {}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}, "description": ""}}}}, "/api/promocode-usage/": {"get": {"operationId": "promocode_usage_list", "description": "ViewSet for viewing promocode usage history.\n\nProvides read-only access to promocode usage records.", "parameters": [{"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}], "tags": ["promocode-usage"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedPromoCodeUsageList"}}}, "description": ""}}}}, "/api/promocode-usage/{id}/": {"get": {"operationId": "promocode_usage_retrieve", "description": "ViewSet for viewing promocode usage history.\n\nProvides read-only access to promocode usage records.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this promo code usage.", "required": true}], "tags": ["promocode-usage"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromoCodeUsage"}}}, "description": ""}}}}, "/api/promocodes/": {"get": {"operationId": "promocodes_list", "description": "ViewSet for managing promocodes.\n\nProvides CRUD operations for promocodes with proper permissions.", "parameters": [{"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}], "tags": ["promocodes"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedPromocodeList"}}}, "description": ""}}}, "post": {"operationId": "promocodes_create", "description": "ViewSet for managing promocodes.\n\nProvides CRUD operations for promocodes with proper permissions.", "tags": ["promocodes"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromocodeRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PromocodeRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PromocodeRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Promocode"}}}, "description": ""}}}}, "/api/promocodes/{id}/": {"get": {"operationId": "promocodes_retrieve", "description": "ViewSet for managing promocodes.\n\nProvides CRUD operations for promocodes with proper permissions.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this promocode.", "required": true}], "tags": ["promocodes"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Promocode"}}}, "description": ""}}}, "put": {"operationId": "promocodes_update", "description": "ViewSet for managing promocodes.\n\nProvides CRUD operations for promocodes with proper permissions.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this promocode.", "required": true}], "tags": ["promocodes"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromocodeRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PromocodeRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PromocodeRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Promocode"}}}, "description": ""}}}, "patch": {"operationId": "promocodes_partial_update", "description": "ViewSet for managing promocodes.\n\nProvides CRUD operations for promocodes with proper permissions.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this promocode.", "required": true}], "tags": ["promocodes"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedPromocodeRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PatchedPromocodeRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PatchedPromocodeRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Promocode"}}}, "description": ""}}}, "delete": {"operationId": "promocodes_destroy", "description": "ViewSet for managing promocodes.\n\nProvides CRUD operations for promocodes with proper permissions.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this promocode.", "required": true}], "tags": ["promocodes"], "security": [{"jwtAuth": []}], "responses": {"204": {"description": "No response body"}}}}, "/api/promocodes/{id}/validate_code/": {"post": {"operationId": "promocodes_validate_code_create", "description": "Validate a promocode for use.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this promocode.", "required": true}], "tags": ["promocodes"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromocodeRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PromocodeRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PromocodeRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Promocode"}}}, "description": ""}}}}, "/api/recommendations/": {"get": {"operationId": "recommendations_list", "description": "Return personalized recommendations for the current user.\nOptional query param: limit (default 12)", "parameters": [{"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Max items to return (default 12)"}], "tags": ["recommendations"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PersonalizedRecommendation"}}}}, "description": ""}}}, "post": {"operationId": "recommendations_create", "description": "Trigger rebuild of recommendations for current user (async if Celery available).", "tags": ["recommendations"], "security": [{"jwtAuth": []}], "responses": {"202": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecoRebuildResponse"}}}, "description": ""}}}}, "/api/reviews/": {"get": {"operationId": "reviews_list", "parameters": [{"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}], "tags": ["reviews"], "security": [{"jwtAuth": []}, {}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedReviewList"}}}, "description": ""}}}, "post": {"operationId": "reviews_create", "tags": ["reviews"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReviewRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ReviewRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/ReviewRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Review"}}}, "description": ""}}}}, "/api/reviews/{id}/": {"get": {"operationId": "reviews_retrieve", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this review.", "required": true}], "tags": ["reviews"], "security": [{"jwtAuth": []}, {}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Review"}}}, "description": ""}}}, "put": {"operationId": "reviews_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this review.", "required": true}], "tags": ["reviews"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReviewRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/ReviewRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/ReviewRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Review"}}}, "description": ""}}}, "patch": {"operationId": "reviews_partial_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this review.", "required": true}], "tags": ["reviews"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedReviewRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PatchedReviewRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PatchedReviewRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Review"}}}, "description": ""}}}, "delete": {"operationId": "reviews_destroy", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this review.", "required": true}], "tags": ["reviews"], "security": [{"jwtAuth": []}], "responses": {"204": {"description": "No response body"}}}}, "/api/roles/": {"get": {"operationId": "roles_list", "parameters": [{"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}], "tags": ["roles"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedRoleList"}}}, "description": ""}}}, "post": {"operationId": "roles_create", "tags": ["roles"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/RoleRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/RoleRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Role"}}}, "description": ""}}}}, "/api/roles/{id}/": {"get": {"operationId": "roles_retrieve", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this role.", "required": true}], "tags": ["roles"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Role"}}}, "description": ""}}}, "put": {"operationId": "roles_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this role.", "required": true}], "tags": ["roles"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/RoleRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/RoleRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Role"}}}, "description": ""}}}, "patch": {"operationId": "roles_partial_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this role.", "required": true}], "tags": ["roles"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedRoleRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PatchedRoleRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PatchedRoleRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Role"}}}, "description": ""}}}, "delete": {"operationId": "roles_destroy", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this role.", "required": true}], "tags": ["roles"], "security": [{"jwtAuth": []}], "responses": {"204": {"description": "No response body"}}}}, "/api/sales-reports/": {"get": {"operationId": "sales_reports_list", "description": "ViewSet for managing sales reports.\n\nProvides CRUD operations for sales reporting and analytics.\nOnly managers and admins can access these endpoints.", "parameters": [{"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}, {"name": "search", "required": false, "in": "query", "description": "A search term.", "schema": {"type": "string"}}], "tags": ["sales-reports"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedSalesReportList"}}}, "description": ""}}}, "post": {"operationId": "sales_reports_create", "description": "ViewSet for managing sales reports.\n\nProvides CRUD operations for sales reporting and analytics.\nOnly managers and admins can access these endpoints.", "tags": ["sales-reports"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SalesReportRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/SalesReportRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/SalesReportRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SalesReport"}}}, "description": ""}}}}, "/api/sales-reports/{id}/": {"get": {"operationId": "sales_reports_retrieve", "description": "ViewSet for managing sales reports.\n\nProvides CRUD operations for sales reporting and analytics.\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this sales report.", "required": true}], "tags": ["sales-reports"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SalesReport"}}}, "description": ""}}}, "put": {"operationId": "sales_reports_update", "description": "ViewSet for managing sales reports.\n\nProvides CRUD operations for sales reporting and analytics.\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this sales report.", "required": true}], "tags": ["sales-reports"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SalesReportRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/SalesReportRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/SalesReportRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SalesReport"}}}, "description": ""}}}, "patch": {"operationId": "sales_reports_partial_update", "description": "ViewSet for managing sales reports.\n\nProvides CRUD operations for sales reporting and analytics.\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this sales report.", "required": true}], "tags": ["sales-reports"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedSalesReportRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PatchedSalesReportRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PatchedSalesReportRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SalesReport"}}}, "description": ""}}}, "delete": {"operationId": "sales_reports_destroy", "description": "ViewSet for managing sales reports.\n\nProvides CRUD operations for sales reporting and analytics.\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this sales report.", "required": true}], "tags": ["sales-reports"], "security": [{"jwtAuth": []}], "responses": {"204": {"description": "No response body"}}}}, "/api/sales-reports/dashboard_summary/": {"get": {"operationId": "sales_reports_dashboard_summary_retrieve", "description": "Get sales dashboard summary with key metrics", "tags": ["sales-reports"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SalesReport"}}}, "description": ""}}}}, "/api/sales-reports/generate_report/": {"post": {"operationId": "sales_reports_generate_report_create", "description": "Generate a new sales report for specified date and type", "tags": ["sales-reports"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SalesReportRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/SalesReportRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/SalesReportRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SalesReport"}}}, "description": ""}}}}, "/api/search/": {"get": {"operationId": "search_retrieve", "description": "Handle GET requests for product search.\n\nQuery parameters:\n- q: Search query string\n- category: Category ID to filter by\n- min_price: Minimum price filter\n- max_price: Maximum price filter\n- sort_by: Field to sort by (e.g., price:asc, price:desc)\n- page: Page number for pagination\n- per_page: Number of results per page\n\nReturns:\n    Response with search results and metadata", "parameters": [{"in": "query", "name": "category", "schema": {"type": "integer"}}, {"in": "query", "name": "max_price", "schema": {"type": "number"}}, {"in": "query", "name": "min_price", "schema": {"type": "number"}}, {"in": "query", "name": "page", "schema": {"type": "integer"}}, {"in": "query", "name": "per_page", "schema": {"type": "integer"}}, {"in": "query", "name": "q", "schema": {"type": "string"}, "description": "Search query"}, {"in": "query", "name": "sort_by", "schema": {"type": "string"}}], "tags": ["search"], "security": [{"jwtAuth": []}, {}], "responses": {"200": {"description": "Search results"}}}}, "/api/shipping_options/": {"get": {"operationId": "shipping_options_retrieve", "tags": ["shipping_options"], "security": [{"jwtAuth": []}], "responses": {"200": {"description": "List of shipping options"}}}}, "/api/stripe/customers/": {"get": {"operationId": "stripe_customers_list", "description": "ViewSet for managing Stripe customers.\n\nProvides CRUD operations for Stripe customers including:\n- Creating customers with automatic Stripe integration\n- Updating customer information\n- Retrieving customer details\n- Managing customer payment methods", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}, {"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}], "tags": ["stripe"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedStripeCustomerList"}}}, "description": ""}}}, "post": {"operationId": "stripe_customers_create", "description": "ViewSet for managing Stripe customers.\n\nProvides CRUD operations for Stripe customers including:\n- Creating customers with automatic Stripe integration\n- Updating customer information\n- Retrieving customer details\n- Managing customer payment methods", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["stripe"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripeCustomerRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/StripeCustomerRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/StripeCustomerRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripeCustomer"}}}, "description": ""}}}}, "/api/stripe/customers/{id}/": {"get": {"operationId": "stripe_customers_retrieve", "description": "ViewSet for managing Stripe customers.\n\nProvides CRUD operations for Stripe customers including:\n- Creating customers with automatic Stripe integration\n- Updating customer information\n- Retrieving customer details\n- Managing customer payment methods", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["stripe"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripeCustomer"}}}, "description": ""}}}, "put": {"operationId": "stripe_customers_update", "description": "ViewSet for managing Stripe customers.\n\nProvides CRUD operations for Stripe customers including:\n- Creating customers with automatic Stripe integration\n- Updating customer information\n- Retrieving customer details\n- Managing customer payment methods", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["stripe"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripeCustomerRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/StripeCustomerRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/StripeCustomerRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripeCustomer"}}}, "description": ""}}}, "patch": {"operationId": "stripe_customers_partial_update", "description": "ViewSet for managing Stripe customers.\n\nProvides CRUD operations for Stripe customers including:\n- Creating customers with automatic Stripe integration\n- Updating customer information\n- Retrieving customer details\n- Managing customer payment methods", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["stripe"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedStripeCustomerRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PatchedStripeCustomerRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PatchedStripeCustomerRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripeCustomer"}}}, "description": ""}}}, "delete": {"operationId": "stripe_customers_destroy", "description": "ViewSet for managing Stripe customers.\n\nProvides CRUD operations for Stripe customers including:\n- Creating customers with automatic Stripe integration\n- Updating customer information\n- Retrieving customer details\n- Managing customer payment methods", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["stripe"], "security": [{"jwtAuth": []}], "responses": {"204": {"description": "No response body"}}}}, "/api/stripe/customers/{id}/attach_payment_method/": {"post": {"operationId": "stripe_customers_attach_payment_method_create", "description": "Attach a payment method to the customer.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["stripe"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripeCustomerRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/StripeCustomerRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/StripeCustomerRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripeCustomer"}}}, "description": ""}}}}, "/api/stripe/customers/{id}/payment_methods/": {"get": {"operationId": "stripe_customers_payment_methods_retrieve", "description": "Get all payment methods for a customer.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["stripe"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripeCustomer"}}}, "description": ""}}}}, "/api/stripe/payment-methods/": {"get": {"operationId": "stripe_payment_methods_list", "description": "ViewSet for managing Stripe payment methods.\n\nProvides operations for:\n- Viewing payment methods\n- Setting default payment methods\n- Detaching payment methods", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}, {"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}], "tags": ["stripe"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedStripePaymentMethodList"}}}, "description": ""}}}, "post": {"operationId": "stripe_payment_methods_create", "description": "ViewSet for managing Stripe payment methods.\n\nProvides operations for:\n- Viewing payment methods\n- Setting default payment methods\n- Detaching payment methods", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["stripe"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripePaymentMethodRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/StripePaymentMethodRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/StripePaymentMethodRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripePaymentMethod"}}}, "description": ""}}}}, "/api/stripe/payment-methods/{id}/": {"get": {"operationId": "stripe_payment_methods_retrieve", "description": "ViewSet for managing Stripe payment methods.\n\nProvides operations for:\n- Viewing payment methods\n- Setting default payment methods\n- Detaching payment methods", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["stripe"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripePaymentMethod"}}}, "description": ""}}}, "put": {"operationId": "stripe_payment_methods_update", "description": "ViewSet for managing Stripe payment methods.\n\nProvides operations for:\n- Viewing payment methods\n- Setting default payment methods\n- Detaching payment methods", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["stripe"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripePaymentMethodRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/StripePaymentMethodRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/StripePaymentMethodRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripePaymentMethod"}}}, "description": ""}}}, "patch": {"operationId": "stripe_payment_methods_partial_update", "description": "ViewSet for managing Stripe payment methods.\n\nProvides operations for:\n- Viewing payment methods\n- Setting default payment methods\n- Detaching payment methods", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["stripe"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedStripePaymentMethodRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PatchedStripePaymentMethodRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PatchedStripePaymentMethodRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripePaymentMethod"}}}, "description": ""}}}, "delete": {"operationId": "stripe_payment_methods_destroy", "description": "Detach a payment method.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["stripe"], "security": [{"jwtAuth": []}], "responses": {"204": {"description": "No response body"}}}}, "/api/stripe/payment-methods/{id}/set_default/": {"post": {"operationId": "stripe_payment_methods_set_default_create", "description": "Set a payment method as the default.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["stripe"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripePaymentMethodRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/StripePaymentMethodRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/StripePaymentMethodRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripePaymentMethod"}}}, "description": ""}}}}, "/api/stripe/refunds/": {"get": {"operationId": "stripe_refunds_list", "description": "ViewSet for managing Stripe refunds.\n\nProvides operations for:\n- Creating refunds\n- Viewing refund history\n- Tracking refund status", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}, {"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}], "tags": ["stripe"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedStripeRefundList"}}}, "description": ""}}}, "post": {"operationId": "stripe_refunds_create", "description": "ViewSet for managing Stripe refunds.\n\nProvides operations for:\n- Creating refunds\n- Viewing refund history\n- Tracking refund status", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["stripe"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripeRefundRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/StripeRefundRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/StripeRefundRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripeRefund"}}}, "description": ""}}}}, "/api/stripe/refunds/{id}/": {"get": {"operationId": "stripe_refunds_retrieve", "description": "ViewSet for managing Stripe refunds.\n\nProvides operations for:\n- Creating refunds\n- Viewing refund history\n- Tracking refund status", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["stripe"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripeRefund"}}}, "description": ""}}}, "put": {"operationId": "stripe_refunds_update", "description": "ViewSet for managing Stripe refunds.\n\nProvides operations for:\n- Creating refunds\n- Viewing refund history\n- Tracking refund status", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["stripe"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripeRefundRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/StripeRefundRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/StripeRefundRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripeRefund"}}}, "description": ""}}}, "patch": {"operationId": "stripe_refunds_partial_update", "description": "ViewSet for managing Stripe refunds.\n\nProvides operations for:\n- Creating refunds\n- Viewing refund history\n- Tracking refund status", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["stripe"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedStripeRefundRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PatchedStripeRefundRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PatchedStripeRefundRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripeRefund"}}}, "description": ""}}}, "delete": {"operationId": "stripe_refunds_destroy", "description": "ViewSet for managing Stripe refunds.\n\nProvides operations for:\n- Creating refunds\n- Viewing refund history\n- Tracking refund status", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "required": true}], "tags": ["stripe"], "security": [{"jwtAuth": []}], "responses": {"204": {"description": "No response body"}}}}, "/api/suppliers/": {"get": {"operationId": "suppliers_list", "description": "ViewSet for managing suppliers.\n\nProvides CRUD operations for supplier management.\nOnly managers and admins can access these endpoints.", "parameters": [{"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}, {"name": "search", "required": false, "in": "query", "description": "A search term.", "schema": {"type": "string"}}], "tags": ["suppliers"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedSupplierList"}}}, "description": ""}}}, "post": {"operationId": "suppliers_create", "description": "ViewSet for managing suppliers.\n\nProvides CRUD operations for supplier management.\nOnly managers and admins can access these endpoints.", "tags": ["suppliers"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SupplierRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/SupplierRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/SupplierRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Supplier"}}}, "description": ""}}}}, "/api/suppliers/{id}/": {"get": {"operationId": "suppliers_retrieve", "description": "ViewSet for managing suppliers.\n\nProvides CRUD operations for supplier management.\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this Supplier.", "required": true}], "tags": ["suppliers"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Supplier"}}}, "description": ""}}}, "put": {"operationId": "suppliers_update", "description": "ViewSet for managing suppliers.\n\nProvides CRUD operations for supplier management.\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this Supplier.", "required": true}], "tags": ["suppliers"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SupplierRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/SupplierRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/SupplierRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Supplier"}}}, "description": ""}}}, "patch": {"operationId": "suppliers_partial_update", "description": "ViewSet for managing suppliers.\n\nProvides CRUD operations for supplier management.\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this Supplier.", "required": true}], "tags": ["suppliers"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedSupplierRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PatchedSupplierRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PatchedSupplierRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Supplier"}}}, "description": ""}}}, "delete": {"operationId": "suppliers_destroy", "description": "ViewSet for managing suppliers.\n\nProvides CRUD operations for supplier management.\nOnly managers and admins can access these endpoints.", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this Supplier.", "required": true}], "tags": ["suppliers"], "security": [{"jwtAuth": []}], "responses": {"204": {"description": "No response body"}}}}, "/api/track/": {"get": {"operationId": "track_retrieve", "description": "Get user behavior data for the current user or a specific user (admin only)", "tags": ["track"], "security": [{"jwtAuth": []}], "responses": {"200": {"description": "List user behavior entries for current user or specified user (admin only)"}}}, "post": {"operationId": "track_create", "tags": ["track"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserBehaviorRequestRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/UserBehaviorRequestRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/UserBehaviorRequestRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserBehavior"}}}, "description": ""}}}}, "/api/users/": {"get": {"operationId": "users_list", "parameters": [{"name": "page", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}, {"name": "search", "required": false, "in": "query", "description": "A search term.", "schema": {"type": "string"}}], "tags": ["users"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedUserList"}}}, "description": ""}}}, "post": {"operationId": "users_create", "tags": ["users"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/UserRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/UserRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "description": ""}}}}, "/api/users/{id}/": {"get": {"operationId": "users_retrieve", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this user.", "required": true}], "tags": ["users"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "description": ""}}}, "put": {"operationId": "users_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this user.", "required": true}], "tags": ["users"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/UserRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/UserRequest"}}}, "required": true}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "description": ""}}}, "patch": {"operationId": "users_partial_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this user.", "required": true}], "tags": ["users"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedUserRequest"}}, "application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/PatchedUserRequest"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/PatchedUserRequest"}}}}, "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "description": ""}}}, "delete": {"operationId": "users_destroy", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string", "format": "uuid"}, "description": "A UUID string identifying this user.", "required": true}], "tags": ["users"], "security": [{"jwtAuth": []}], "responses": {"204": {"description": "No response body"}}}}, "/api/users/me/": {"get": {"operationId": "users_me_retrieve", "tags": ["users"], "security": [{"jwtAuth": []}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "description": ""}}}}, "/api/webhook/stripe/": {"post": {"operationId": "webhook_stripe_create", "description": "Handle Stripe webhook events.\n\nThis view receives webhook events from Stripe and processes them accordingly.\nIt handles payment intents, refunds, and other payment-related events.", "tags": ["webhook"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {}}}, "application/x-www-form-urlencoded": {"schema": {"type": "object", "additionalProperties": {}}}, "multipart/form-data": {"schema": {"type": "object", "additionalProperties": {}}}}}, "security": [{"jwtAuth": []}, {}], "responses": {"200": {"description": "Stripe webhook received"}}}}}, "components": {"schemas": {"ActionEnum": {"enum": ["search", "view", "click", "add_to_cart", "purchase", "page_view", "login", "logout", "register", "wishlist_add", "review", "share"], "type": "string", "description": "* `search` - Search\n* `view` - View\n* `click` - Click\n* `add_to_cart` - Add to Cart\n* `purchase` - Purchase\n* `page_view` - Page View\n* `login` - Login\n* `logout` - Logout\n* `register` - Register\n* `wishlist_add` - Add to Wishlist\n* `review` - Write Review\n* `share` - Share Product"}, "AlertTypeEnum": {"enum": ["low_stock", "out_of_stock", "expiring_soon", "expired", "reorder_suggestion"], "type": "string", "description": "* `low_stock` - Low Stock\n* `out_of_stock` - Out of Stock\n* `expiring_soon` - Expiring Soon\n* `expired` - Expired\n* `reorder_suggestion` - Reorder Suggestion"}, "ApplicableToEnum": {"enum": ["all", "category", "product"], "type": "string", "description": "* `all` - All Products\n* `category` - Specific Categories\n* `product` - Specific Products"}, "BlankEnum": {"enum": [""]}, "CSRFTok": {"type": "object", "properties": {"csrfToken": {"type": "string"}}, "required": ["csrfToken"]}, "Cart": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "created_by": {"type": "string", "format": "uuid"}, "status": {"$ref": "#/components/schemas/CartStatusEnum"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/CartItem"}, "readOnly": true}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"type": "string", "format": "date-time", "readOnly": true}}, "required": ["created_at", "created_by", "id", "items", "updated_at"]}, "CartItem": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "cart": {"type": "string", "format": "uuid"}, "product": {"allOf": [{"$ref": "#/components/schemas/Product"}], "readOnly": true}, "price": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "quantity": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}}, "required": ["cart", "created_at", "id", "price", "product"]}, "CartItemRequest": {"type": "object", "properties": {"cart": {"type": "string", "format": "uuid"}, "product_id": {"type": "string", "format": "uuid", "writeOnly": true}, "price": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "quantity": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}}, "required": ["cart", "price", "product_id"]}, "CartRequest": {"type": "object", "properties": {"created_by": {"type": "string", "format": "uuid"}, "status": {"$ref": "#/components/schemas/CartStatusEnum"}}, "required": ["created_by"]}, "CartStatusEnum": {"enum": ["active", "abandoned", "converted"], "type": "string", "description": "* `active` - Active\n* `abandoned` - Abandoned\n* `converted` - Converted to Order"}, "Category": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "parent_category": {"type": "string", "format": "uuid", "nullable": true}, "slug": {"type": "string", "readOnly": true, "pattern": "^[-a-zA-Z0-9_]+$"}, "name": {"type": "string", "maxLength": 255}, "description": {"type": "string", "nullable": true}, "tags": {}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"type": "string", "format": "date-time", "readOnly": true}}, "required": ["created_at", "id", "name", "slug", "updated_at"]}, "CategoryRequest": {"type": "object", "properties": {"parent_category": {"type": "string", "format": "uuid", "nullable": true}, "name": {"type": "string", "minLength": 1, "maxLength": 255}, "description": {"type": "string", "nullable": true}, "tags": {}}, "required": ["name"]}, "ChangeTypeEnum": {"enum": ["add", "subtract", "order", "return", "adjustment", "initial", "batch_add", "batch_subtract", "expiry"], "type": "string", "description": "* `add` - Addition\n* `subtract` - Subtraction\n* `order` - Order Placement\n* `return` - Order Return\n* `adjustment` - Manual Adjustment\n* `initial` - Initial Stock\n* `batch_add` - Batch Addition\n* `batch_subtract` - Batch Subtraction\n* `expiry` - Expired Stock Removal"}, "DeliveryStatusEnum": {"enum": ["processing", "shipped", "delivered", "cancelled"], "type": "string", "description": "* `processing` - Processing\n* `shipped` - Shipped\n* `delivered` - Delivered\n* `cancelled` - Cancelled"}, "DiscountTypeEnum": {"enum": ["percentage", "fixed"], "type": "string", "description": "* `percentage` - Percentage\n* `fixed` - Fixed Amount"}, "EnhancedPaymentProcessingRequestRequest": {"type": "object", "properties": {"order_id": {"type": "integer"}, "payment_method_id": {"type": "string", "minLength": 1}, "save_payment_method": {"type": "boolean"}, "use_saved_method": {"type": "boolean"}}, "required": ["order_id"]}, "EnhancedPaymentProcessingResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "payment_intent_client_secret": {"type": "string"}, "payment_intent_id": {"type": "string"}, "requires_action": {"type": "boolean"}, "status": {"type": "string"}, "message": {"type": "string"}, "error": {"type": "string"}}, "required": ["success"]}, "ForgotPasswordRequestRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "minLength": 1}}, "required": ["email"]}, "ForgotPasswordResponse": {"type": "object", "properties": {"message": {"type": "string"}}, "required": ["message"]}, "GoogleAuthRequestRequest": {"type": "object", "properties": {"id_token": {"type": "string", "minLength": 1}}, "required": ["id_token"]}, "GoogleAuthResponse": {"type": "object", "properties": {"refresh": {"type": "string"}, "access": {"type": "string"}, "user": {"$ref": "#/components/schemas/User"}}, "required": ["access", "refresh", "user"]}, "InventoryAlert": {"type": "object", "description": "Serializer for InventoryAlert model", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "alert_type": {"$ref": "#/components/schemas/AlertTypeEnum"}, "alert_type_display": {"type": "string", "readOnly": true}, "priority": {"$ref": "#/components/schemas/PriorityEnum"}, "priority_display": {"type": "string", "readOnly": true}, "title": {"type": "string", "maxLength": 255}, "message": {"type": "string"}, "product": {"type": "string", "format": "uuid", "nullable": true}, "product_title": {"type": "string", "readOnly": true}, "batch": {"type": "string", "format": "uuid", "nullable": true}, "batch_number": {"type": "string", "readOnly": true}, "is_active": {"type": "boolean"}, "is_acknowledged": {"type": "boolean"}, "acknowledged_by": {"type": "string", "format": "uuid", "nullable": true}, "acknowledged_by_username": {"type": "string", "readOnly": true}, "acknowledged_at": {"type": "string", "format": "date-time", "readOnly": true, "nullable": true}, "threshold_value": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648, "nullable": true, "description": "Threshold value that triggered this alert"}, "expiry_date": {"type": "string", "format": "date", "nullable": true, "description": "Expiry date for expiry-related alerts"}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"type": "string", "format": "date-time", "readOnly": true}, "days_since_created": {"type": "integer", "description": "Calculate days since alert was created", "readOnly": true}, "is_overdue": {"type": "boolean", "description": "Check if alert is overdue (older than 7 days and not acknowledged)", "readOnly": true}}, "required": ["acknowledged_at", "acknowledged_by_username", "alert_type", "alert_type_display", "batch_number", "created_at", "days_since_created", "id", "is_overdue", "message", "priority_display", "product_title", "title", "updated_at"]}, "InventoryAlertRequest": {"type": "object", "description": "Serializer for InventoryAlert model", "properties": {"alert_type": {"$ref": "#/components/schemas/AlertTypeEnum"}, "priority": {"$ref": "#/components/schemas/PriorityEnum"}, "title": {"type": "string", "minLength": 1, "maxLength": 255}, "message": {"type": "string", "minLength": 1}, "product": {"type": "string", "format": "uuid", "nullable": true}, "batch": {"type": "string", "format": "uuid", "nullable": true}, "is_active": {"type": "boolean"}, "is_acknowledged": {"type": "boolean"}, "acknowledged_by": {"type": "string", "format": "uuid", "nullable": true}, "threshold_value": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648, "nullable": true, "description": "Threshold value that triggered this alert"}, "expiry_date": {"type": "string", "format": "date", "nullable": true, "description": "Expiry date for expiry-related alerts"}}, "required": ["alert_type", "message", "title"]}, "InventoryBatch": {"type": "object", "description": "Serializer for the InventoryBatch model", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "product_title": {"type": "string", "readOnly": true}, "supplier_name": {"type": "string", "readOnly": true}, "is_expired": {"type": "boolean", "readOnly": true}, "days_until_expiry": {"type": "integer", "readOnly": true}, "is_low_stock": {"type": "boolean", "readOnly": true}, "batch_number": {"type": "string", "maxLength": 100}, "quantity_received": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "quantity_remaining": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "cost_per_unit": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "manufacturing_date": {"type": "string", "format": "date", "nullable": true}, "expiry_date": {"type": "string", "format": "date", "nullable": true}, "received_date": {"type": "string", "format": "date-time", "readOnly": true}, "notes": {"type": "string", "nullable": true}, "is_active": {"type": "boolean"}, "product": {"type": "string", "format": "uuid"}, "supplier": {"type": "string", "format": "uuid"}}, "required": ["batch_number", "cost_per_unit", "days_until_expiry", "id", "is_expired", "is_low_stock", "product", "product_title", "quantity_received", "quantity_remaining", "received_date", "supplier", "supplier_name"]}, "InventoryBatchRequest": {"type": "object", "description": "Serializer for the InventoryBatch model", "properties": {"batch_number": {"type": "string", "minLength": 1, "maxLength": 100}, "quantity_received": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "quantity_remaining": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "cost_per_unit": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "manufacturing_date": {"type": "string", "format": "date", "nullable": true}, "expiry_date": {"type": "string", "format": "date", "nullable": true}, "notes": {"type": "string", "nullable": true}, "is_active": {"type": "boolean"}, "product": {"type": "string", "format": "uuid"}, "supplier": {"type": "string", "format": "uuid"}}, "required": ["batch_number", "cost_per_unit", "product", "quantity_received", "quantity_remaining", "supplier"]}, "InventoryChange": {"type": "object", "description": "Serializer for the InventoryChange model.\n\nProvides a complete representation of inventory changes including:\n- Nested product data\n- User who made the change\n- Reference to related order (if applicable)\n- Detailed change information (quantities, timestamps, etc.)\n\nUses write-only ID fields for creating records while providing\nfull nested objects in responses.", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "product": {"allOf": [{"$ref": "#/components/schemas/Product"}], "readOnly": true}, "batch": {"allOf": [{"$ref": "#/components/schemas/InventoryBatch"}], "readOnly": true}, "quantity_change": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "change_type": {"$ref": "#/components/schemas/ChangeTypeEnum"}, "previous_quantity": {"type": "integer", "readOnly": true}, "new_quantity": {"type": "integer", "readOnly": true}, "changed_by": {"allOf": [{"$ref": "#/components/schemas/User"}], "readOnly": true}, "reference_order": {"allOf": [{"$ref": "#/components/schemas/Order"}], "readOnly": true}, "notes": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time", "readOnly": true}}, "required": ["batch", "change_type", "changed_by", "id", "new_quantity", "previous_quantity", "product", "quantity_change", "reference_order", "timestamp"]}, "InventoryChangeRequest": {"type": "object", "description": "Serializer for the InventoryChange model.\n\nProvides a complete representation of inventory changes including:\n- Nested product data\n- User who made the change\n- Reference to related order (if applicable)\n- Detailed change information (quantities, timestamps, etc.)\n\nUses write-only ID fields for creating records while providing\nfull nested objects in responses.", "properties": {"product_id": {"type": "string", "format": "uuid", "writeOnly": true}, "batch_id": {"type": "string", "format": "uuid", "writeOnly": true, "nullable": true}, "quantity_change": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "change_type": {"$ref": "#/components/schemas/ChangeTypeEnum"}, "reference_order_id": {"type": "string", "format": "uuid", "writeOnly": true, "nullable": true}, "notes": {"type": "string", "nullable": true}}, "required": ["change_type", "product_id", "quantity_change"]}, "LoginRequestRequest": {"type": "object", "properties": {"username": {"type": "string", "minLength": 1}, "password": {"type": "string", "writeOnly": true, "minLength": 1}}, "required": ["password", "username"]}, "LoginResponse": {"type": "object", "properties": {"refresh": {"type": "string"}, "access": {"type": "string"}, "user": {"$ref": "#/components/schemas/User"}}, "required": ["access", "refresh", "user"]}, "NullEnum": {"enum": [null]}, "Offer": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "product": {"allOf": [{"$ref": "#/components/schemas/Product"}], "readOnly": true}, "name": {"type": "string", "maxLength": 255}, "offer_type": {"type": "string", "maxLength": 20}, "discount_value": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "start_date": {"type": "string", "format": "date-time"}, "end_date": {"type": "string", "format": "date-time"}, "is_festival": {"type": "boolean"}, "is_active": {"type": "boolean"}}, "required": ["discount_value", "end_date", "id", "name", "offer_type", "product", "start_date"]}, "OfferRequest": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uuid", "writeOnly": true}, "name": {"type": "string", "minLength": 1, "maxLength": 255}, "offer_type": {"type": "string", "minLength": 1, "maxLength": 20}, "discount_value": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "start_date": {"type": "string", "format": "date-time"}, "end_date": {"type": "string", "format": "date-time"}, "is_festival": {"type": "boolean"}, "is_active": {"type": "boolean"}}, "required": ["discount_value", "end_date", "name", "offer_type", "product_id", "start_date"]}, "Order": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "user": {"type": "string", "format": "uuid"}, "total_price": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$", "description": "Final order total after all discounts"}, "payment_status": {"$ref": "#/components/schemas/PaymentStatusEnum"}, "delivery_status": {"$ref": "#/components/schemas/DeliveryStatusEnum"}, "stripe_payment_intent_id": {"type": "string", "nullable": true, "maxLength": 255}, "stripe_payment_method_id": {"type": "string", "nullable": true, "maxLength": 255}, "order_lines": {"type": "array", "items": {"$ref": "#/components/schemas/OrderLine"}, "readOnly": true}, "stripe_payments": {"type": "array", "items": {"type": "object", "additionalProperties": {}}, "readOnly": true}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}}, "required": ["created_at", "id", "order_lines", "stripe_payments", "total_price", "user"]}, "OrderLine": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "order": {"type": "string", "format": "uuid"}, "product": {"allOf": [{"$ref": "#/components/schemas/Product"}], "readOnly": true}, "price": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "quantity": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}}, "required": ["id", "order", "price", "product"]}, "OrderLineRequest": {"type": "object", "properties": {"order": {"type": "string", "format": "uuid"}, "price": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "quantity": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}}, "required": ["order", "price"]}, "OrderRequest": {"type": "object", "properties": {"user": {"type": "string", "format": "uuid"}, "total_price": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$", "description": "Final order total after all discounts"}, "payment_status": {"$ref": "#/components/schemas/PaymentStatusEnum"}, "delivery_status": {"$ref": "#/components/schemas/DeliveryStatusEnum"}, "stripe_payment_intent_id": {"type": "string", "nullable": true, "maxLength": 255}, "stripe_payment_method_id": {"type": "string", "nullable": true, "maxLength": 255}}, "required": ["total_price", "user"]}, "PaginatedCartList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/Cart"}}}}, "PaginatedCategoryList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/Category"}}}}, "PaginatedInventoryAlertList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/InventoryAlert"}}}}, "PaginatedInventoryBatchList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/InventoryBatch"}}}}, "PaginatedInventoryChangeList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/InventoryChange"}}}}, "PaginatedOfferList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/Offer"}}}}, "PaginatedOrderList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}}}, "PaginatedProductImageList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/ProductImage"}}}}, "PaginatedProductList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}}}, "PaginatedProductModerationLogList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/ProductModerationLog"}}}}, "PaginatedPromoCodeUsageList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/PromoCodeUsage"}}}}, "PaginatedPromocodeList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/Promocode"}}}}, "PaginatedReviewList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/Review"}}}}, "PaginatedRoleList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}}}}, "PaginatedSalesReportList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/SalesReport"}}}}, "PaginatedStripeCustomerList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/StripeCustomer"}}}}, "PaginatedStripePaymentMethodList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/StripePaymentMethod"}}}}, "PaginatedStripeRefundList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/StripeRefund"}}}}, "PaginatedSupplierList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/Supplier"}}}}, "PaginatedUserList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?page=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}, "PatchedCartRequest": {"type": "object", "properties": {"created_by": {"type": "string", "format": "uuid"}, "status": {"$ref": "#/components/schemas/CartStatusEnum"}}}, "PatchedCategoryRequest": {"type": "object", "properties": {"parent_category": {"type": "string", "format": "uuid", "nullable": true}, "name": {"type": "string", "minLength": 1, "maxLength": 255}, "description": {"type": "string", "nullable": true}, "tags": {}}}, "PatchedInventoryAlertRequest": {"type": "object", "description": "Serializer for InventoryAlert model", "properties": {"alert_type": {"$ref": "#/components/schemas/AlertTypeEnum"}, "priority": {"$ref": "#/components/schemas/PriorityEnum"}, "title": {"type": "string", "minLength": 1, "maxLength": 255}, "message": {"type": "string", "minLength": 1}, "product": {"type": "string", "format": "uuid", "nullable": true}, "batch": {"type": "string", "format": "uuid", "nullable": true}, "is_active": {"type": "boolean"}, "is_acknowledged": {"type": "boolean"}, "acknowledged_by": {"type": "string", "format": "uuid", "nullable": true}, "threshold_value": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648, "nullable": true, "description": "Threshold value that triggered this alert"}, "expiry_date": {"type": "string", "format": "date", "nullable": true, "description": "Expiry date for expiry-related alerts"}}}, "PatchedInventoryBatchRequest": {"type": "object", "description": "Serializer for the InventoryBatch model", "properties": {"batch_number": {"type": "string", "minLength": 1, "maxLength": 100}, "quantity_received": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "quantity_remaining": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "cost_per_unit": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "manufacturing_date": {"type": "string", "format": "date", "nullable": true}, "expiry_date": {"type": "string", "format": "date", "nullable": true}, "notes": {"type": "string", "nullable": true}, "is_active": {"type": "boolean"}, "product": {"type": "string", "format": "uuid"}, "supplier": {"type": "string", "format": "uuid"}}}, "PatchedInventoryChangeRequest": {"type": "object", "description": "Serializer for the InventoryChange model.\n\nProvides a complete representation of inventory changes including:\n- Nested product data\n- User who made the change\n- Reference to related order (if applicable)\n- Detailed change information (quantities, timestamps, etc.)\n\nUses write-only ID fields for creating records while providing\nfull nested objects in responses.", "properties": {"product_id": {"type": "string", "format": "uuid", "writeOnly": true}, "batch_id": {"type": "string", "format": "uuid", "writeOnly": true, "nullable": true}, "quantity_change": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "change_type": {"$ref": "#/components/schemas/ChangeTypeEnum"}, "reference_order_id": {"type": "string", "format": "uuid", "writeOnly": true, "nullable": true}, "notes": {"type": "string", "nullable": true}}}, "PatchedOfferRequest": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uuid", "writeOnly": true}, "name": {"type": "string", "minLength": 1, "maxLength": 255}, "offer_type": {"type": "string", "minLength": 1, "maxLength": 20}, "discount_value": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "start_date": {"type": "string", "format": "date-time"}, "end_date": {"type": "string", "format": "date-time"}, "is_festival": {"type": "boolean"}, "is_active": {"type": "boolean"}}}, "PatchedOrderRequest": {"type": "object", "properties": {"user": {"type": "string", "format": "uuid"}, "total_price": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$", "description": "Final order total after all discounts"}, "payment_status": {"$ref": "#/components/schemas/PaymentStatusEnum"}, "delivery_status": {"$ref": "#/components/schemas/DeliveryStatusEnum"}, "stripe_payment_intent_id": {"type": "string", "nullable": true, "maxLength": 255}, "stripe_payment_method_id": {"type": "string", "nullable": true, "maxLength": 255}}}, "PatchedProductImageRequest": {"type": "object", "properties": {"image": {"type": "string", "format": "binary", "nullable": true}, "order": {"type": "integer", "maximum": 65535, "minimum": 0}, "alt_text": {"type": "string", "nullable": true, "maxLength": 255}}}, "PatchedProductModerationLogRequest": {"type": "object", "properties": {"action": {"type": "string", "minLength": 1, "maxLength": 100}, "comments": {"type": "string", "nullable": true}}}, "PatchedProductRequest": {"type": "object", "description": "Serializer for the Product model.\n\nProvides a complete representation of product data including:\n- Nested category data\n- All product images (main + additional)\n- Inventory and pricing information\n\nUses a write-only category_id field for creating/updating products\nwhile providing the full category object in responses.", "properties": {"category_id": {"type": "string", "format": "uuid", "writeOnly": true}, "title": {"type": "string", "minLength": 1, "maxLength": 255}, "summary": {"type": "string", "nullable": true, "maxLength": 255}, "description": {"type": "string", "nullable": true}, "picture": {"type": "string", "format": "binary", "nullable": true}, "price": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "discount_type": {"type": "string", "nullable": true, "maxLength": 20}, "discount_value": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "tags": {}, "stock_quantity": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "is_active": {"type": "boolean"}}}, "PatchedPromocodeRequest": {"type": "object", "description": "Serializer for the Promocode model.\n\nProvides a complete representation of promocode data for API responses.", "properties": {"code": {"type": "string", "minLength": 1, "description": "Unique promocode", "maxLength": 50}, "description": {"type": "string", "nullable": true, "description": "Description of the promocode"}, "discount_type": {"$ref": "#/components/schemas/DiscountTypeEnum"}, "discount_value": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$", "description": "Discount amount or percentage"}, "valid_from": {"type": "string", "format": "date-time", "description": "Start date and time for promocode validity"}, "valid_until": {"type": "string", "format": "date-time", "description": "End date and time for promocode validity"}, "usage_limit": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648, "description": "Maximum number of times this code can be used (0 for unlimited)"}, "applicable_to": {"$ref": "#/components/schemas/ApplicableToEnum"}, "minimum_order_amount": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$", "description": "Minimum order amount to apply this promocode"}, "is_active": {"type": "boolean", "description": "Whether this promocode is active"}}}, "PatchedReviewRequest": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uuid", "writeOnly": true}, "rating": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,2}(?:\\.\\d{0,1})?$"}, "comment": {"type": "string", "nullable": true}}}, "PatchedRoleRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1, "maxLength": 100}, "description": {"type": "string", "nullable": true}}}, "PatchedSalesReportRequest": {"type": "object", "description": "Serializer for the SalesReport model", "properties": {"report_date": {"type": "string", "format": "date"}, "report_type": {"$ref": "#/components/schemas/ReportTypeEnum"}, "total_orders": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "total_revenue": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,10}(?:\\.\\d{0,2})?$"}, "total_units_sold": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "total_discount_given": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,10}(?:\\.\\d{0,2})?$"}, "new_customers": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "returning_customers": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "top_selling_product_units": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "average_order_value": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "top_selling_product": {"type": "string", "format": "uuid", "nullable": true}}}, "PatchedStripeCustomerRequest": {"type": "object", "description": "Serializer for the StripeCustomer model.", "properties": {"user_id": {"type": "string", "format": "uuid", "writeOnly": true}, "email": {"type": "string", "format": "email", "minLength": 1, "maxLength": 254}, "name": {"type": "string", "nullable": true, "maxLength": 255}, "phone": {"type": "string", "nullable": true, "maxLength": 20}, "address": {}, "metadata": {}}}, "PatchedStripePaymentMethodRequest": {"type": "object", "description": "Serializer for the StripePaymentMethod model.", "properties": {"customer_id": {"type": "string", "format": "uuid", "writeOnly": true}, "type": {"$ref": "#/components/schemas/TypeEnum"}, "card_brand": {"type": "string", "nullable": true, "maxLength": 20}, "card_last4": {"type": "string", "nullable": true, "maxLength": 4}, "card_exp_month": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648, "nullable": true}, "card_exp_year": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648, "nullable": true}, "is_default": {"type": "boolean"}, "is_active": {"type": "boolean"}}}, "PatchedStripeRefundRequest": {"type": "object", "description": "Serializer for the StripeRefund model.", "properties": {"payment_id": {"type": "string", "format": "uuid", "writeOnly": true}, "amount": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "currency": {"type": "string", "minLength": 1, "maxLength": 3}, "status": {"$ref": "#/components/schemas/StripeRefundStatusEnum"}, "reason": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ReasonEnum"}, {"$ref": "#/components/schemas/BlankEnum"}, {"$ref": "#/components/schemas/NullEnum"}]}, "receipt_number": {"type": "string", "nullable": true, "maxLength": 255}}}, "PatchedSupplierRequest": {"type": "object", "description": "Serializer for the Supplier model", "properties": {"name": {"type": "string", "minLength": 1, "maxLength": 255}, "contact_person": {"type": "string", "nullable": true, "maxLength": 255}, "email": {"type": "string", "format": "email", "nullable": true, "maxLength": 254}, "phone": {"type": "string", "nullable": true, "maxLength": 20}, "address": {"type": "string", "nullable": true}, "is_active": {"type": "boolean"}}}, "PatchedUserRequest": {"type": "object", "description": "Serializer for the User model.\n\nProvides a complete representation of user data for API responses,\nwith sensitive fields marked as read-only.", "properties": {"username": {"type": "string", "minLength": 1, "description": "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.", "pattern": "^[\\w.@+-]+$", "maxLength": 150}, "email": {"type": "string", "format": "email", "minLength": 1, "maxLength": 254}, "first_name": {"type": "string", "maxLength": 150}, "last_name": {"type": "string", "maxLength": 150}, "phone": {"type": "string", "nullable": true, "maxLength": 20}, "avatar": {"type": "string", "format": "binary", "nullable": true}, "locale": {"type": "string", "minLength": 1, "maxLength": 10}, "bio": {"type": "string", "nullable": true}, "company": {"type": "string", "nullable": true, "maxLength": 255}, "email_validated": {"type": "boolean"}, "phone_validated": {"type": "boolean"}}}, "PaymentProcessingRequestRequest": {"type": "object", "properties": {"payment_method_id": {"type": "string", "minLength": 1}, "order_id": {"type": "integer"}}, "required": ["order_id"]}, "PaymentProcessingResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "payment_intent_client_secret": {"type": "string"}, "payment_intent_id": {"type": "string"}, "payment_intent": {"type": "string"}, "requires_action": {"type": "boolean"}, "message": {"type": "string"}, "error": {"type": "string"}}, "required": ["success"]}, "PaymentStatusEnum": {"enum": ["pending", "paid", "failed", "refunded"], "type": "string", "description": "* `pending` - Pending\n* `paid` - Paid\n* `failed` - Failed\n* `refunded` - Refunded"}, "PersonalizedRecommendation": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "user": {"allOf": [{"$ref": "#/components/schemas/User"}], "readOnly": true}, "product": {"allOf": [{"$ref": "#/components/schemas/Product"}], "readOnly": true}, "recommendation_score": {"type": "number", "format": "double"}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}}, "required": ["created_at", "id", "product", "recommendation_score", "user"]}, "PriorityEnum": {"enum": ["low", "medium", "high", "critical"], "type": "string", "description": "* `low` - Low\n* `medium` - Medium\n* `high` - High\n* `critical` - Critical"}, "Product": {"type": "object", "description": "Serializer for the Product model.\n\nProvides a complete representation of product data including:\n- Nested category data\n- All product images (main + additional)\n- Inventory and pricing information\n\nUses a write-only category_id field for creating/updating products\nwhile providing the full category object in responses.", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "category": {"allOf": [{"$ref": "#/components/schemas/Category"}], "readOnly": true}, "title": {"type": "string", "maxLength": 255}, "slug": {"type": "string", "readOnly": true, "pattern": "^[-a-zA-Z0-9_]+$"}, "summary": {"type": "string", "nullable": true, "maxLength": 255}, "description": {"type": "string", "nullable": true}, "picture": {"type": "string", "format": "uri", "nullable": true}, "price": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "discount_type": {"type": "string", "nullable": true, "maxLength": 20}, "discount_value": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "tags": {}, "stock_quantity": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "is_active": {"type": "boolean"}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"type": "string", "format": "date-time", "readOnly": true}, "additional_images": {"type": "array", "items": {"$ref": "#/components/schemas/ProductImage"}, "readOnly": true}, "images": {"type": "array", "items": {"type": "string"}, "readOnly": true}}, "required": ["additional_images", "category", "created_at", "id", "images", "price", "slug", "title", "updated_at"]}, "ProductImage": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "image": {"type": "string", "format": "uri", "nullable": true}, "order": {"type": "integer", "maximum": 65535, "minimum": 0}, "alt_text": {"type": "string", "nullable": true, "maxLength": 255}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"type": "string", "format": "date-time", "readOnly": true}}, "required": ["created_at", "id", "updated_at"]}, "ProductImageRequest": {"type": "object", "properties": {"image": {"type": "string", "format": "binary", "nullable": true}, "order": {"type": "integer", "maximum": 65535, "minimum": 0}, "alt_text": {"type": "string", "nullable": true, "maxLength": 255}}}, "ProductModerationLog": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "product": {"allOf": [{"$ref": "#/components/schemas/Product"}], "readOnly": true}, "moderator": {"allOf": [{"$ref": "#/components/schemas/User"}], "readOnly": true}, "action": {"type": "string", "maxLength": 100}, "comments": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time", "readOnly": true}}, "required": ["action", "id", "moderator", "product", "timestamp"]}, "ProductModerationLogRequest": {"type": "object", "properties": {"action": {"type": "string", "minLength": 1, "maxLength": 100}, "comments": {"type": "string", "nullable": true}}, "required": ["action"]}, "ProductRequest": {"type": "object", "description": "Serializer for the Product model.\n\nProvides a complete representation of product data including:\n- Nested category data\n- All product images (main + additional)\n- Inventory and pricing information\n\nUses a write-only category_id field for creating/updating products\nwhile providing the full category object in responses.", "properties": {"category_id": {"type": "string", "format": "uuid", "writeOnly": true}, "title": {"type": "string", "minLength": 1, "maxLength": 255}, "summary": {"type": "string", "nullable": true, "maxLength": 255}, "description": {"type": "string", "nullable": true}, "picture": {"type": "string", "format": "binary", "nullable": true}, "price": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "discount_type": {"type": "string", "nullable": true, "maxLength": 20}, "discount_value": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "tags": {}, "stock_quantity": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "is_active": {"type": "boolean"}}, "required": ["category_id", "price", "title"]}, "PromoCodeApplyRequestRequest": {"type": "object", "properties": {"code": {"type": "string", "minLength": 1}, "cart_id": {"type": "integer"}}, "required": ["code"]}, "PromoCodeApplyResponse": {"type": "object", "properties": {"valid": {"type": "boolean"}, "discount": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "message": {"type": "string"}}, "required": ["valid"]}, "PromoCodeUsage": {"type": "object", "description": "Serializer for the PromoCodeUsage model.\n\nProvides a complete representation of promocode usage data for API responses.", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "promocode": {"allOf": [{"$ref": "#/components/schemas/Promocode"}], "readOnly": true}, "user": {"allOf": [{"$ref": "#/components/schemas/User"}], "readOnly": true}, "order": {"allOf": [{"$ref": "#/components/schemas/Order"}], "readOnly": true}, "discount_amount": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$", "description": "Actual discount amount applied"}, "order_amount_before_discount": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$", "description": "Order total before promocode discount"}, "order_amount_after_discount": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$", "description": "Order total after promocode discount"}, "used_at": {"type": "string", "format": "date-time", "readOnly": true}, "ip_address": {"type": "string", "nullable": true, "description": "IP address from which promocode was used"}, "user_agent": {"type": "string", "nullable": true, "description": "User agent string"}}, "required": ["discount_amount", "id", "order", "order_amount_after_discount", "order_amount_before_discount", "promocode", "used_at", "user"]}, "Promocode": {"type": "object", "description": "Serializer for the Promocode model.\n\nProvides a complete representation of promocode data for API responses.", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "code": {"type": "string", "description": "Unique promocode", "maxLength": 50}, "description": {"type": "string", "nullable": true, "description": "Description of the promocode"}, "discount_type": {"$ref": "#/components/schemas/DiscountTypeEnum"}, "discount_value": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$", "description": "Discount amount or percentage"}, "valid_from": {"type": "string", "format": "date-time", "description": "Start date and time for promocode validity"}, "valid_until": {"type": "string", "format": "date-time", "description": "End date and time for promocode validity"}, "usage_limit": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648, "description": "Maximum number of times this code can be used (0 for unlimited)"}, "times_used": {"type": "integer", "readOnly": true, "description": "Number of times this code has been used"}, "applicable_to": {"$ref": "#/components/schemas/ApplicableToEnum"}, "applicable_categories": {"type": "array", "items": {"type": "string"}, "readOnly": true}, "applicable_products": {"type": "array", "items": {"type": "string"}, "readOnly": true}, "minimum_order_amount": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$", "description": "Minimum order amount to apply this promocode"}, "is_active": {"type": "boolean", "description": "Whether this promocode is active"}, "created_by": {"type": "string", "readOnly": true}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"type": "string", "format": "date-time", "readOnly": true}}, "required": ["applicable_categories", "applicable_products", "code", "created_at", "created_by", "discount_value", "id", "times_used", "updated_at", "valid_from", "valid_until"]}, "PromocodeRequest": {"type": "object", "description": "Serializer for the Promocode model.\n\nProvides a complete representation of promocode data for API responses.", "properties": {"code": {"type": "string", "minLength": 1, "description": "Unique promocode", "maxLength": 50}, "description": {"type": "string", "nullable": true, "description": "Description of the promocode"}, "discount_type": {"$ref": "#/components/schemas/DiscountTypeEnum"}, "discount_value": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$", "description": "Discount amount or percentage"}, "valid_from": {"type": "string", "format": "date-time", "description": "Start date and time for promocode validity"}, "valid_until": {"type": "string", "format": "date-time", "description": "End date and time for promocode validity"}, "usage_limit": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648, "description": "Maximum number of times this code can be used (0 for unlimited)"}, "applicable_to": {"$ref": "#/components/schemas/ApplicableToEnum"}, "minimum_order_amount": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$", "description": "Minimum order amount to apply this promocode"}, "is_active": {"type": "boolean", "description": "Whether this promocode is active"}}, "required": ["code", "discount_value", "valid_from", "valid_until"]}, "ReasonEnum": {"enum": ["duplicate", "fraudulent", "requested_by_customer", "expired_uncaptured_charge", "other"], "type": "string", "description": "* `duplicate` - Duplicate\n* `fraudulent` - Fraudulent\n* `requested_by_customer` - Requested by Customer\n* `expired_uncaptured_charge` - Expired Uncaptured Charge\n* `other` - Other"}, "RecoRebuildResponse": {"type": "object", "properties": {"status": {"type": "string"}}, "required": ["status"]}, "RegisterResponse": {"type": "object", "properties": {"refresh": {"type": "string"}, "access": {"type": "string"}, "user": {"$ref": "#/components/schemas/User"}}, "required": ["access", "refresh", "user"]}, "ReportTypeEnum": {"enum": ["daily", "weekly", "monthly", "yearly"], "type": "string", "description": "* `daily` - Daily\n* `weekly` - Weekly\n* `monthly` - Monthly\n* `yearly` - Yearly"}, "ResetPasswordRequestRequest": {"type": "object", "properties": {"token": {"type": "string", "minLength": 1}, "new_password": {"type": "string", "minLength": 1}}, "required": ["new_password", "token"]}, "ResetPasswordResponse": {"type": "object", "properties": {"message": {"type": "string"}}, "required": ["message"]}, "Review": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "user": {"allOf": [{"$ref": "#/components/schemas/User"}], "readOnly": true}, "product": {"allOf": [{"$ref": "#/components/schemas/Product"}], "readOnly": true}, "rating": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,2}(?:\\.\\d{0,1})?$"}, "comment": {"type": "string", "nullable": true}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}}, "required": ["created_at", "id", "product", "rating", "user"]}, "ReviewRequest": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uuid", "writeOnly": true}, "rating": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,2}(?:\\.\\d{0,1})?$"}, "comment": {"type": "string", "nullable": true}}, "required": ["product_id", "rating"]}, "Role": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "name": {"type": "string", "maxLength": 100}, "description": {"type": "string", "nullable": true}}, "required": ["id", "name"]}, "RoleRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1, "maxLength": 100}, "description": {"type": "string", "nullable": true}}, "required": ["name"]}, "RootLinks": {"type": "object", "properties": {"api": {"type": "string", "format": "uri"}, "admin": {"type": "string", "format": "uri"}}, "required": ["admin", "api"]}, "SalesReport": {"type": "object", "description": "Serializer for the SalesReport model", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "top_selling_product_title": {"type": "string", "readOnly": true}, "report_date": {"type": "string", "format": "date"}, "report_type": {"$ref": "#/components/schemas/ReportTypeEnum"}, "total_orders": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "total_revenue": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,10}(?:\\.\\d{0,2})?$"}, "total_units_sold": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "total_discount_given": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,10}(?:\\.\\d{0,2})?$"}, "new_customers": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "returning_customers": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "top_selling_product_units": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "average_order_value": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"type": "string", "format": "date-time", "readOnly": true}, "top_selling_product": {"type": "string", "format": "uuid", "nullable": true}}, "required": ["created_at", "id", "report_date", "report_type", "top_selling_product_title", "updated_at"]}, "SalesReportRequest": {"type": "object", "description": "Serializer for the SalesReport model", "properties": {"report_date": {"type": "string", "format": "date"}, "report_type": {"$ref": "#/components/schemas/ReportTypeEnum"}, "total_orders": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "total_revenue": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,10}(?:\\.\\d{0,2})?$"}, "total_units_sold": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "total_discount_given": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,10}(?:\\.\\d{0,2})?$"}, "new_customers": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "returning_customers": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "top_selling_product_units": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648}, "average_order_value": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "top_selling_product": {"type": "string", "format": "uuid", "nullable": true}}, "required": ["report_date", "report_type"]}, "StripeCustomer": {"type": "object", "description": "Serializer for the StripeCustomer model.", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "user": {"allOf": [{"$ref": "#/components/schemas/User"}], "readOnly": true}, "stripe_customer_id": {"type": "string", "readOnly": true}, "email": {"type": "string", "format": "email", "maxLength": 254}, "name": {"type": "string", "nullable": true, "maxLength": 255}, "phone": {"type": "string", "nullable": true, "maxLength": 20}, "address": {}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"type": "string", "format": "date-time", "readOnly": true}, "metadata": {}}, "required": ["created_at", "email", "id", "stripe_customer_id", "updated_at", "user"]}, "StripeCustomerRequest": {"type": "object", "description": "Serializer for the StripeCustomer model.", "properties": {"user_id": {"type": "string", "format": "uuid", "writeOnly": true}, "email": {"type": "string", "format": "email", "minLength": 1, "maxLength": 254}, "name": {"type": "string", "nullable": true, "maxLength": 255}, "phone": {"type": "string", "nullable": true, "maxLength": 20}, "address": {}, "metadata": {}}, "required": ["email", "user_id"]}, "StripePaymentMethod": {"type": "object", "description": "Serializer for the StripePaymentMethod model.", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "customer": {"allOf": [{"$ref": "#/components/schemas/StripeCustomer"}], "readOnly": true}, "stripe_payment_method_id": {"type": "string", "readOnly": true}, "type": {"$ref": "#/components/schemas/TypeEnum"}, "card_brand": {"type": "string", "nullable": true, "maxLength": 20}, "card_last4": {"type": "string", "nullable": true, "maxLength": 4}, "card_exp_month": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648, "nullable": true}, "card_exp_year": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648, "nullable": true}, "is_default": {"type": "boolean"}, "is_active": {"type": "boolean"}, "display_name": {"type": "string", "description": "Get a user-friendly display name for the payment method.", "readOnly": true}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"type": "string", "format": "date-time", "readOnly": true}}, "required": ["created_at", "customer", "display_name", "id", "stripe_payment_method_id", "type", "updated_at"]}, "StripePaymentMethodRequest": {"type": "object", "description": "Serializer for the StripePaymentMethod model.", "properties": {"customer_id": {"type": "string", "format": "uuid", "writeOnly": true}, "type": {"$ref": "#/components/schemas/TypeEnum"}, "card_brand": {"type": "string", "nullable": true, "maxLength": 20}, "card_last4": {"type": "string", "nullable": true, "maxLength": 4}, "card_exp_month": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648, "nullable": true}, "card_exp_year": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648, "nullable": true}, "is_default": {"type": "boolean"}, "is_active": {"type": "boolean"}}, "required": ["customer_id", "type"]}, "StripeRefund": {"type": "object", "description": "Serializer for the StripeRefund model.", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "payment": {"type": "string", "readOnly": true}, "stripe_refund_id": {"type": "string", "readOnly": true}, "amount": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "currency": {"type": "string", "maxLength": 3}, "status": {"$ref": "#/components/schemas/StripeRefundStatusEnum"}, "reason": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ReasonEnum"}, {"$ref": "#/components/schemas/BlankEnum"}, {"$ref": "#/components/schemas/NullEnum"}]}, "receipt_number": {"type": "string", "nullable": true, "maxLength": 255}, "created_by": {"allOf": [{"$ref": "#/components/schemas/User"}], "readOnly": true}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"type": "string", "format": "date-time", "readOnly": true}}, "required": ["amount", "created_at", "created_by", "id", "payment", "stripe_refund_id", "updated_at"]}, "StripeRefundRequest": {"type": "object", "description": "Serializer for the StripeRefund model.", "properties": {"payment_id": {"type": "string", "format": "uuid", "writeOnly": true}, "amount": {"type": "string", "format": "decimal", "pattern": "^-?\\d{0,8}(?:\\.\\d{0,2})?$"}, "currency": {"type": "string", "minLength": 1, "maxLength": 3}, "status": {"$ref": "#/components/schemas/StripeRefundStatusEnum"}, "reason": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ReasonEnum"}, {"$ref": "#/components/schemas/BlankEnum"}, {"$ref": "#/components/schemas/NullEnum"}]}, "receipt_number": {"type": "string", "nullable": true, "maxLength": 255}}, "required": ["amount", "payment_id"]}, "StripeRefundStatusEnum": {"enum": ["pending", "succeeded", "failed", "canceled"], "type": "string", "description": "* `pending` - Pending\n* `succeeded` - Succeeded\n* `failed` - Failed\n* `canceled` - Canceled"}, "Supplier": {"type": "object", "description": "Serializer for the Supplier model", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "name": {"type": "string", "maxLength": 255}, "contact_person": {"type": "string", "nullable": true, "maxLength": 255}, "email": {"type": "string", "format": "email", "nullable": true, "maxLength": 254}, "phone": {"type": "string", "nullable": true, "maxLength": 20}, "address": {"type": "string", "nullable": true}, "is_active": {"type": "boolean"}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"type": "string", "format": "date-time", "readOnly": true}}, "required": ["created_at", "id", "name", "updated_at"]}, "SupplierRequest": {"type": "object", "description": "Serializer for the Supplier model", "properties": {"name": {"type": "string", "minLength": 1, "maxLength": 255}, "contact_person": {"type": "string", "nullable": true, "maxLength": 255}, "email": {"type": "string", "format": "email", "nullable": true, "maxLength": 254}, "phone": {"type": "string", "nullable": true, "maxLength": 20}, "address": {"type": "string", "nullable": true}, "is_active": {"type": "boolean"}}, "required": ["name"]}, "TokenRefresh": {"type": "object", "properties": {"access": {"type": "string", "readOnly": true}, "refresh": {"type": "string"}}, "required": ["access", "refresh"]}, "TokenRefreshRequest": {"type": "object", "properties": {"refresh": {"type": "string", "minLength": 1}}, "required": ["refresh"]}, "TypeEnum": {"enum": ["card", "bank_account", "apple_pay", "google_pay", "paypal"], "type": "string", "description": "* `card` - Card\n* `bank_account` - Bank Account\n* `apple_pay` - Apple Pay\n* `google_pay` - Google Pay\n* `paypal` - PayPal"}, "User": {"type": "object", "description": "Serializer for the User model.\n\nProvides a complete representation of user data for API responses,\nwith sensitive fields marked as read-only.", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "username": {"type": "string", "description": "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.", "pattern": "^[\\w.@+-]+$", "maxLength": 150}, "email": {"type": "string", "format": "email", "maxLength": 254}, "first_name": {"type": "string", "maxLength": 150}, "last_name": {"type": "string", "maxLength": 150}, "phone": {"type": "string", "nullable": true, "maxLength": 20}, "avatar": {"type": "string", "format": "uri", "nullable": true}, "locale": {"type": "string", "maxLength": 10}, "bio": {"type": "string", "nullable": true}, "company": {"type": "string", "nullable": true, "maxLength": 255}, "email_validated": {"type": "boolean"}, "phone_validated": {"type": "boolean"}, "date_joined": {"type": "string", "format": "date-time", "readOnly": true}, "last_login": {"type": "string", "format": "date-time", "readOnly": true, "nullable": true}}, "required": ["date_joined", "email", "id", "last_login", "username"]}, "UserBehavior": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "readOnly": true}, "user": {"allOf": [{"$ref": "#/components/schemas/User"}], "readOnly": true}, "action": {"$ref": "#/components/schemas/ActionEnum"}, "product": {"allOf": [{"$ref": "#/components/schemas/Product"}], "readOnly": true}, "metadata": {}, "action_time": {"type": "string", "format": "date-time", "readOnly": true}}, "required": ["action", "action_time", "id", "product", "user"]}, "UserBehaviorRequestRequest": {"type": "object", "properties": {"action": {"type": "string", "minLength": 1}, "product_id": {"type": "integer"}, "metadata": {"type": "object", "additionalProperties": {}}, "session_id": {"type": "string", "minLength": 1}}, "required": ["action"]}, "UserRegistrationRequest": {"type": "object", "properties": {"username": {"type": "string", "minLength": 1, "description": "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.", "pattern": "^[\\w.@+-]+$", "maxLength": 150}, "email": {"type": "string", "format": "email", "minLength": 1, "maxLength": 254}, "password": {"type": "string", "writeOnly": true, "minLength": 1}, "password_confirm": {"type": "string", "writeOnly": true, "minLength": 1}, "first_name": {"type": "string", "maxLength": 150}, "last_name": {"type": "string", "maxLength": 150}}, "required": ["email", "password", "password_confirm", "username"]}, "UserRequest": {"type": "object", "description": "Serializer for the User model.\n\nProvides a complete representation of user data for API responses,\nwith sensitive fields marked as read-only.", "properties": {"username": {"type": "string", "minLength": 1, "description": "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.", "pattern": "^[\\w.@+-]+$", "maxLength": 150}, "email": {"type": "string", "format": "email", "minLength": 1, "maxLength": 254}, "first_name": {"type": "string", "maxLength": 150}, "last_name": {"type": "string", "maxLength": 150}, "phone": {"type": "string", "nullable": true, "maxLength": 20}, "avatar": {"type": "string", "format": "binary", "nullable": true}, "locale": {"type": "string", "minLength": 1, "maxLength": 10}, "bio": {"type": "string", "nullable": true}, "company": {"type": "string", "nullable": true, "maxLength": 255}, "email_validated": {"type": "boolean"}, "phone_validated": {"type": "boolean"}}, "required": ["email", "username"]}}, "securitySchemes": {"jwtAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}