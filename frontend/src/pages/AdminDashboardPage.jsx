import React from 'react';
import { Link } from 'react-router-dom';
import Layout from '../components/Layout.jsx';

const Card = ({ title, description, to }) => (
  <Link to={to} className="block p-6 rounded-lg border border-gray-100 shadow bg-white hover:shadow-md transition">
    <div className="text-lg font-semibold mb-1">{title}</div>
    <div className="text-gray-600 text-sm">{description}</div>
  </Link>
);

const AdminDashboardPage = () => {
  return (
    <Layout>
      {/* Main Content */}
      <div className="container mx-auto py-12 px-4 space-y-8">
        <h1 className="text-3xl font-bold">Admin Dashboard</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card title="Product Moderation" description="Review and approve product changes" to="/admin/moderation" />
          <Card title="Order Management" description="Manage orders and refunds" to="/admin/orders" />
          <Card title="Inventory Dashboard" description="Overview of stock, changes, and KPIs" to="/admin/inventory" color="emerald" />
          <Card title="Inventory Adjustments" description="Add or subtract stock for products" to="/admin/inventory/changes" color="blue" />
          <Card title="Inventory Alerts" description="Acknowledge or dismiss low stock/expiry alerts" to="/admin/inventory/alerts" color="amber" />
          <Card title="Batches" description="View expiring and expired batches" to="/admin/inventory/batches" color="teal" />
          <Card title="Sales Dashboard" description="Revenue, orders, and AOV overview" to="/admin/sales" />
          <Card title="Sales Reports" description="Daily/Monthly/Yearly reports and trends" to="/admin/sales/reports" />

        </div>
      </div>
    </Layout>
  );
};

export default AdminDashboardPage;
