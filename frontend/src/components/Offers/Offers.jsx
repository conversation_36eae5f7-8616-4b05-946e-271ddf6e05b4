import React, { useEffect, useState } from 'react';
import { fetchOffers } from '../../services/api'; // Fetch from API

const Offers = () => {
  const [offers, setOffers] = useState([]);

  useEffect(() => {
    const getOffers = async () => {
      const response = await fetchOffers();
      setOffers(response.data);
    };
    getOffers();
  }, []);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
      {offers.map((offer) => (
        <div key={offer.id} className="card bg-yellow-100 p-4 shadow-lg rounded-lg">
          <h2 className="text-xl font-semibold">{offer.title}</h2>
          <p>{offer.description}</p>
        </div>
      ))}
    </div>
  );
};

export default Offers;
