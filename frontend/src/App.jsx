/**
 * Main App component for the eCommerce platform
 *
 * This component sets up the routing structure and provides:
 * - Analytics tracking via Google Analytics
 * - Toast notifications for user feedback
 * - Context providers for shared state
 */
import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import toast from 'react-hot-toast';
import GoogleAnalytics from './components/Analytics/GoogleAnalytics';
import { AnalyticsProvider } from './contexts/AnalyticsContext';
import { AuthProvider } from './contexts/AuthContext.jsx';
import { CartProvider } from './contexts/CartContext.jsx';
import FloatingCartButton from './components/FloatingCartButton/FloatingCartButton.jsx';
import ThemeSwitcher from './components/ThemeSwitcher.jsx';

// Page components
import HomePage from './pages/HomePage.jsx';
import ProductListingPage from './pages/ProductListingPage.jsx';
import ProductDetailPage from './pages/ProductDetailPage.jsx';
import CartPage from './pages/CartPage.jsx';
import CheckoutPage from './pages/CheckoutPage.jsx';
import OrderConfirmationPage from './pages/OrderConfirmationPage.jsx';
import LoginPage from './pages/LoginPage.jsx';
import RegistrationPage from './pages/RegistrationPage.jsx';
import UserProfilePage from './pages/UserProfilePage.jsx';
import ContactUsPage from './pages/ContactUsPage.jsx';
import FAQPage from './pages/FAQPage.jsx';
import AdminDashboardPage from './pages/AdminDashboardPage.jsx';
import ProductModerationPage from './pages/ProductModerationPage.jsx';
import ForgotPasswordPage from './pages/ForgotPasswordPage.jsx';
import ResetPasswordPage from './pages/ResetPasswordPage.jsx';
import OrderManagementPage from './pages/OrderManagementPage.jsx';
import AnalyticsDashboardPage from './pages/AnalyticsDashboardPage.jsx';
import UserAnalyticsPage from './pages/UserAnalyticsPage.jsx';
import InventoryDashboardPage from './pages/InventoryDashboardPage.jsx';
import InventoryAdjustmentsPage from './pages/InventoryAdjustmentsPage.jsx';
import InventoryAlertsPage from './pages/InventoryAlertsPage.jsx';
import InventoryBatchesPage from './pages/InventoryBatchesPage.jsx';
import ThemePreview from './components/ThemePreview.jsx';
import PrivateRoute from './components/PrivateRoute.jsx';
import SalesDashboardPage from './pages/SalesDashboardPage.jsx';
import SalesReportsPage from './pages/SalesReportsPage.jsx';



// Import API test functions
import { runAllTests } from './test-api-connection';

// Import CSS for animations
import './assets/styles/animations.css';

function App() {
  const [isLoading, setIsLoading] = useState(true);

  // Initialize app and test API connection
  useEffect(() => {
    // Simulate app initialization
    const timer = setTimeout(() => {
      setIsLoading(false);

      // Run API tests to verify backend connection
      runAllTests()
        .then(success => {
          if (success) {
            toast.success('Backend API connection successful!');
          } else {
            toast.error('Failed to connect to backend API. Check console for details.');
          }
        })
        .catch(error => {
          console.error('Error running API tests:', error);
          toast.error('Error testing backend connection. Check console for details.');
        });
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Loading screen
  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mb-4 h-16 w-16 animate-spin rounded-full border-b-2 border-t-2 border-primary-600"></div>
          <h2 className="text-xl font-semibold text-gray-700">Loading...</h2>
        </div>
      </div>
    );
  }

  return (
    <Router>
      {/* Global toast notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 3000,
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            duration: 3000,
            style: {
              background: '#059669',
              color: '#fff',
            },
          },
          error: {
            duration: 4000,
            style: {
              background: '#DC2626',
              color: '#fff',
            },
          },
        }}
      />

      {/* Google Analytics tracking */}
      <GoogleAnalytics />

      {/* Auth Provider for authentication state */}
      <AuthProvider>
        {/* Cart Provider for shopping cart state */}
        <CartProvider>
          {/* Analytics Provider for tracking user behavior */}
          <AnalyticsProvider>
              <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/products" element={<ProductListingPage />} />
            <Route path="/products/:productId" element={<ProductDetailPage />} />
            <Route path="/cart" element={<CartPage />} />
            <Route path="/checkout" element={<CheckoutPage />} />
            <Route path="/order-confirmation" element={<OrderConfirmationPage />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegistrationPage />} />
            <Route path="/forgot-password" element={<ForgotPasswordPage />} />
            <Route path="/reset-password" element={<ResetPasswordPage />} />
            <Route path="/profile" element={<UserProfilePage />} />
            <Route path="/contact" element={<ContactUsPage />} />
            <Route path="/faq" element={<FAQPage />} />
            <Route path="/admin" element={<PrivateRoute adminOnly><AdminDashboardPage /></PrivateRoute>} />
            <Route path="/admin/moderation" element={<PrivateRoute adminOnly><ProductModerationPage /></PrivateRoute>} />
            <Route path="/admin/orders" element={<PrivateRoute adminOnly><OrderManagementPage /></PrivateRoute>} />

            {/* Inventory Management (manager/admin only) */}
            <Route path="/admin/inventory" element={
              <PrivateRoute adminOnly>
                <InventoryDashboardPage />
              </PrivateRoute>
            } />
            <Route path="/admin/inventory/changes" element={
              <PrivateRoute adminOnly>
                <InventoryAdjustmentsPage />
              </PrivateRoute>
            } />
            <Route path="/admin/inventory/alerts" element={
              <PrivateRoute adminOnly>
                <InventoryAlertsPage />
              </PrivateRoute>
            } />
            <Route path="/admin/inventory/batches" element={
              <PrivateRoute adminOnly>
                <InventoryBatchesPage />



              </PrivateRoute>
            } />


            {/* Sales Management (manager/admin only) */}
            <Route path="/admin/sales" element={
              <PrivateRoute adminOnly>
                <SalesDashboardPage />
              </PrivateRoute>
            } />
            <Route path="/admin/sales/reports" element={
              <PrivateRoute adminOnly>
                <SalesReportsPage />
              </PrivateRoute>
            } />

            <Route path="/analytics" element={<AnalyticsDashboardPage />} />
            <Route path="/analytics/user/:userId" element={<UserAnalyticsPage />} />
            <Route path="/analytics/user" element={<UserAnalyticsPage />} />
            <Route path="/theme-preview" element={<ThemePreview />} />
              </Routes>

              {/* Floating Cart Button for interactive shopping */}
              <FloatingCartButton />

              {/* Theme Switcher for changing UI themes */}
              <ThemeSwitcher />
            </AnalyticsProvider>
          </CartProvider>
        </AuthProvider>
    </Router>
  );
}

export default App;
