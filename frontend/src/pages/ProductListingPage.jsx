import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

import toast from 'react-hot-toast';

// Import components
import Layout from '../components/Layout.jsx';

import InteractiveProductGrid from '../components/InteractiveProductGrid/InteractiveProductGrid.jsx';


// Import API services
import { productService } from '../services/api';
import { useAnalytics } from '../contexts/AnalyticsContext.jsx';

const ProductListingPage = ({ userId }) => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const category = queryParams.get('category');
  const discount = queryParams.get('discount');
  const searchQuery = queryParams.get('search');

  // State for products and categories
  const [products, setProducts] = useState([]);
  const [categoryList, setCategoryList] = useState([]);
  const [loading, setLoading] = useState(true);

  const { trackProductImpressions, trackSearchAction } = useAnalytics();

  // Fetch products and categories from API
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch products


        const productsResponse = await productService.fetchProducts();

        // Map products to the format expected by the component
        const mappedProducts = productsResponse.data.results.map(product => ({
          id: product.id,
          name: product.title,
          price: parseFloat(product.price),
          category: product.category.slug,
          discount: product.discount_type !== null,
          discountValue: product.discount_value > 0 ? parseFloat(product.discount_value) : 0,
          rating: 4.5, // Default rating since we don't have this in the API yet
          imageSrc: product.picture,
          images: product.images || (product.picture ? [product.picture] : [])
        }));

        setProducts(mappedProducts);

        // Fetch categories
        const categoriesResponse = await productService.fetchCategories();
        setCategoryList(categoriesResponse.data.results);

        setLoading(false);

        // Track search if there's a search query
        if (searchQuery) {
          trackSearchAction(searchQuery, productsResponse.data.results.length);
        }

        // Track product impressions for the listing
        const listName = category ? `category_${category}` :
                        discount ? 'discounted_products' :
                        searchQuery ? 'search_results' : 'all_products';
        trackProductImpressions(productsResponse.data.results, listName);

      } catch (err) {
        console.error('Error fetching data:', err);

        setLoading(false);
        toast.error('Failed to load products. Please try again later.');
      }
    };

    fetchData();
  }, [category, discount, searchQuery, trackSearchAction, trackProductImpressions]);

  // Filter products based on query parameters
  const filteredProducts = products.filter(product => {
    if (category && product.category !== category) return false;
    if (discount === 'true' && !product.discount) return false;
    return true;
  });

  // State for sorting and filtering
  const [sortBy] = useState('featured');
  const [priceRange] = useState('all');

  // Apply sorting
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    if (sortBy === 'price-low') return a.price - b.price;
    if (sortBy === 'price-high') return b.price - a.price;
    if (sortBy === 'rating') return b.rating - a.rating;
    return 0; // default: featured
  });

  // Apply price range filtering
  const displayedProducts = sortedProducts.filter(product => {
    if (priceRange === 'under-50' && product.price >= 50) return false;
    if (priceRange === '50-100' && (product.price < 50 || product.price > 100)) return false;
    if (priceRange === 'over-100' && product.price <= 100) return false;
    return true;
  });

  return (
    <Layout userId={userId}>

      {/* Main Content */}
      <div className="container mx-auto py-12 px-4">
        <h1 className="text-3xl font-bold mb-8">
          {category
            ? `${category.charAt(0).toUpperCase() + category.slice(1)} Products`
            : discount
              ? 'Discounted Products'
              : 'All Products'}
        </h1>

        {/* Enhanced Products Grid - Full Width */}
        <div className="w-full">
          <InteractiveProductGrid
            products={displayedProducts}
            loading={loading}
            categories={categoryList}
            hasMore={false}
          />
        </div>
      </div>
    </Layout>
  );
};

export default ProductListingPage;
