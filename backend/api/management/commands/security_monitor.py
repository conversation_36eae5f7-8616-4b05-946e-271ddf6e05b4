"""
Django management command for security monitoring and management.
"""
from django.core.management.base import BaseCommand
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
from datetime import datetime, timedelta
import json


class Command(BaseCommand):
    help = 'Monitor and manage security-related activities'

    def add_arguments(self, parser):
        parser.add_argument(
            '--action',
            type=str,
            choices=['monitor', 'clear_blocks', 'stats', 'cleanup'],
            default='monitor',
            help='Action to perform'
        )
        parser.add_argument(
            '--hours',
            type=int,
            default=24,
            help='Number of hours to look back for monitoring'
        )

    def handle(self, *args, **options):
        action = options['action']
        hours = options['hours']

        if action == 'monitor':
            self.monitor_security(hours)
        elif action == 'clear_blocks':
            self.clear_blocked_ips()
        elif action == 'stats':
            self.show_security_stats(hours)
        elif action == 'cleanup':
            self.cleanup_old_logs()

    def monitor_security(self, hours):
        """
        Monitor security activities and generate alerts.
        """
        self.stdout.write(f"Monitoring security activities for the last {hours} hours...")
        
        # Get all API logs from cache
        api_logs = self.get_api_logs(hours)
        
        # Analyze for suspicious patterns
        suspicious_ips = self.analyze_suspicious_activity(api_logs)
        
        if suspicious_ips:
            self.stdout.write(
                self.style.WARNING(f"Found {len(suspicious_ips)} suspicious IP addresses:")
            )
            for ip, count in suspicious_ips.items():
                self.stdout.write(f"  - {ip}: {count} requests")
        else:
            self.stdout.write(self.style.SUCCESS("No suspicious activity detected"))

    def clear_blocked_ips(self):
        """
        Clear all blocked IP addresses.
        """
        self.stdout.write("Clearing all blocked IP addresses...")
        
        # Get all cache keys that start with 'blocked_ip_'
        # Note: This is a simplified approach. In production, you'd want a more efficient method
        cleared_count = 0
        
        # Since we can't easily iterate over all cache keys, we'll clear known patterns
        # In a real implementation, you'd store blocked IPs in a database or use Redis SCAN
        self.stdout.write(
            self.style.WARNING(
                "Note: This command clears the cache. All blocked IPs will be unblocked."
            )
        )
        
        # Clear the entire cache (this is aggressive but effective for demo purposes)
        cache.clear()
        
        self.stdout.write(self.style.SUCCESS("Cache cleared. All IP blocks removed."))

    def show_security_stats(self, hours):
        """
        Show security statistics.
        """
        self.stdout.write(f"Security statistics for the last {hours} hours:")
        
        # Get API logs
        api_logs = self.get_api_logs(hours)
        
        if not api_logs:
            self.stdout.write("No API activity recorded")
            return
        
        # Calculate statistics
        total_requests = len(api_logs)
        unique_ips = len(set(log.get('ip', 'unknown') for log in api_logs))
        
        # Count requests by method
        method_counts = {}
        for log in api_logs:
            method = log.get('method', 'unknown')
            method_counts[method] = method_counts.get(method, 0) + 1
        
        # Count requests by path
        path_counts = {}
        for log in api_logs:
            path = log.get('path', 'unknown')
            path_counts[path] = path_counts.get(path, 0) + 1
        
        # Display statistics
        self.stdout.write(f"Total requests: {total_requests}")
        self.stdout.write(f"Unique IP addresses: {unique_ips}")
        
        self.stdout.write("\nRequests by method:")
        for method, count in sorted(method_counts.items()):
            self.stdout.write(f"  {method}: {count}")
        
        self.stdout.write("\nTop 10 requested paths:")
        for path, count in sorted(path_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
            self.stdout.write(f"  {path}: {count}")

    def cleanup_old_logs(self):
        """
        Clean up old security logs from cache.
        """
        self.stdout.write("Cleaning up old security logs...")
        
        # This is a simplified cleanup. In production, you'd implement proper log rotation
        cutoff_time = timezone.now() - timedelta(days=7)
        cutoff_timestamp = cutoff_time.timestamp()
        
        # Note: This is a demonstration. In practice, you'd need a more sophisticated
        # approach to clean up specific cache entries
        self.stdout.write(
            f"Would clean up logs older than {cutoff_time.strftime('%Y-%m-%d %H:%M:%S')}"
        )
        
        self.stdout.write(self.style.SUCCESS("Cleanup completed"))

    def get_api_logs(self, hours):
        """
        Retrieve API logs from cache for the specified time period.
        """
        logs = []
        cutoff_time = timezone.now() - timedelta(hours=hours)
        cutoff_timestamp = cutoff_time.timestamp()
        
        # Note: This is a simplified approach. In production, you'd store logs
        # in a database or use a proper logging system
        
        # For demonstration, we'll return empty logs since we can't easily
        # iterate over cache keys in Django's cache framework
        return logs

    def analyze_suspicious_activity(self, api_logs):
        """
        Analyze API logs for suspicious activity patterns.
        """
        ip_counts = {}
        
        for log in api_logs:
            ip = log.get('ip', 'unknown')
            if ip != 'unknown':
                ip_counts[ip] = ip_counts.get(ip, 0) + 1
        
        # Flag IPs with more than 100 requests per hour as suspicious
        suspicious_threshold = 100
        suspicious_ips = {
            ip: count for ip, count in ip_counts.items() 
            if count > suspicious_threshold
        }
        
        return suspicious_ips

    def block_suspicious_ips(self, suspicious_ips):
        """
        Block suspicious IP addresses.
        """
        for ip in suspicious_ips:
            block_key = f"blocked_ip_{ip}"
            cache.set(block_key, True, 3600)  # Block for 1 hour
            self.stdout.write(f"Blocked IP: {ip}")
