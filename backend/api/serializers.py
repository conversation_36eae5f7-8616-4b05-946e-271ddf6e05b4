"""
Serializers for the eCommerce API.

This module contains serializer classes that convert model instances
to JSON representations and validate incoming data for API operations.
"""
from rest_framework import serializers

# Optional drf-yasg import with safe fallbacks
try:
    from drf_yasg.utils import swagger_serializer_method
    from drf_yasg import openapi

    # Use swagger_serializer_method directly as extend_schema_field
    extend_schema_field = swagger_serializer_method

    class _Types:
        INT = openapi.TYPE_INTEGER
        STR = openapi.TYPE_STRING
        NUMBER = openapi.TYPE_NUMBER
        UUID = openapi.TYPE_STRING
        BOOL = openapi.TYPE_BOOLEAN
    OpenApiTypes = _Types()
except Exception:  # drf-yasg not installed; provide no-op fallbacks
    def extend_schema_field(*args, **kwargs):
        def _decorator(fn):
            return fn
        return _decorator
    class _Types:
        INT = int
        STR = str
        NUMBER = float
        UUID = str
        BOOL = bool
    OpenApiTypes = _Types()

from .models import (
    User, Role, UserRole, Credential, SocialProfile,
    Category, Product, ProductImage, ProductModerationLog,
    Cart, CartItem, Order, OrderLine,
    Review, PersonalizedRecommendation, Offer, UserBehavior,
    InventoryChange, InventoryBatch, Supplier, SalesReport, InventoryAlert,
    StripePayment, StripeCustomer, StripePaymentMethod, StripeRefund, StripeWebhookEvent,
    PaymentAnalytics, Promocode, PromoCodeUsage
)

class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for the User model.

    Provides a complete representation of user data for API responses,
    with sensitive fields marked as read-only.
    """
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'phone',
                  'avatar', 'locale', 'bio', 'company', 'email_validated',
                  'phone_validated', 'date_joined', 'last_login']
        read_only_fields = ['id', 'date_joined', 'last_login']

class RoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = ['id', 'name', 'description']

class UserRoleSerializer(serializers.ModelSerializer):
    role = RoleSerializer(read_only=True)

    class Meta:
        model = UserRole
        fields = ['id', 'user', 'role', 'assigned_at']
        read_only_fields = ['id', 'assigned_at']

class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['id', 'parent_category', 'slug', 'name', 'description', 'tags', 'created_at', 'updated_at']
        read_only_fields = ['id', 'slug', 'created_at', 'updated_at']

class ProductImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductImage
        fields = ['id', 'image', 'order', 'alt_text', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

class ProductSerializer(serializers.ModelSerializer):
    """
    Serializer for the Product model.

    Provides a complete representation of product data including:
    - Nested category data
    - All product images (main + additional)
    - Inventory and pricing information

    Uses a write-only category_id field for creating/updating products
    while providing the full category object in responses.
    """
    category = CategorySerializer(read_only=True)
    category_id = serializers.UUIDField(write_only=True)
    additional_images = ProductImageSerializer(many=True, read_only=True)
    images = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = ['id', 'category', 'category_id', 'title', 'slug', 'summary', 'description',
                  'picture', 'price', 'discount_type', 'discount_value', 'tags',
                  'stock_quantity', 'is_active', 'created_at', 'updated_at',
                  'additional_images', 'images']
        read_only_fields = ['id', 'slug', 'created_at', 'updated_at', 'images']

    @extend_schema_field(serializers.ListField(child=serializers.CharField()))
    def get_images(self, obj):
        """Return a list of all image URLs for the product"""
        return obj.all_images

class ProductModerationLogSerializer(serializers.ModelSerializer):
    moderator = UserSerializer(read_only=True)
    product = ProductSerializer(read_only=True)

    class Meta:
        model = ProductModerationLog
        fields = ['id', 'product', 'moderator', 'action', 'comments', 'timestamp']
        read_only_fields = ['id', 'timestamp']

class CartItemSerializer(serializers.ModelSerializer):
    product = ProductSerializer(read_only=True)
    product_id = serializers.UUIDField(write_only=True)

    class Meta:
        model = CartItem
        fields = ['id', 'cart', 'product', 'product_id', 'price', 'quantity', 'created_at']
        read_only_fields = ['id', 'created_at']

class CartSerializer(serializers.ModelSerializer):
    items = CartItemSerializer(many=True, read_only=True)

    class Meta:
        model = Cart
        fields = ['id', 'created_by', 'status', 'items', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

class OrderLineSerializer(serializers.ModelSerializer):
    product = ProductSerializer(read_only=True)

    class Meta:
        model = OrderLine
        fields = ['id', 'order', 'product', 'price', 'quantity']
        read_only_fields = ['id']

class OrderSerializer(serializers.ModelSerializer):
    order_lines = OrderLineSerializer(many=True, read_only=True)
    stripe_payments = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Order
        fields = ['id', 'user', 'total_price', 'payment_status', 'delivery_status',
                 'stripe_payment_intent_id', 'stripe_payment_method_id',
                 'order_lines', 'stripe_payments', 'created_at']
        read_only_fields = ['id', 'created_at', 'stripe_payments']

    @extend_schema_field(serializers.ListField(child=serializers.DictField()))
    def get_stripe_payments(self, obj):
        from .serializers import StripePaymentSerializer
        payments = obj.stripe_payments.all()
        return StripePaymentSerializer(payments, many=True).data

class ReviewSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    product = ProductSerializer(read_only=True)
    product_id = serializers.UUIDField(write_only=True)

    class Meta:
        model = Review
        fields = ['id', 'user', 'product', 'product_id', 'rating', 'comment', 'created_at']
        read_only_fields = ['id', 'created_at']

class OfferSerializer(serializers.ModelSerializer):
    product = ProductSerializer(read_only=True)
    product_id = serializers.UUIDField(write_only=True)

    class Meta:
        model = Offer
        fields = ['id', 'product', 'product_id', 'name', 'offer_type', 'discount_value',
                  'start_date', 'end_date', 'is_festival', 'is_active']
        read_only_fields = ['id']

class UserBehaviorSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    product = ProductSerializer(read_only=True)

    class Meta:
        model = UserBehavior
        fields = ['id', 'user', 'action', 'product', 'metadata', 'action_time']
        read_only_fields = ['id', 'action_time']

class PersonalizedRecommendationSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    product = ProductSerializer(read_only=True)

    class Meta:
        model = PersonalizedRecommendation
        fields = ['id', 'user', 'product', 'recommendation_score', 'created_at']
        read_only_fields = ['id', 'created_at']

class UserRegistrationSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})
    password_confirm = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})

    class Meta:
        model = User
        fields = ['username', 'email', 'password', 'password_confirm', 'first_name', 'last_name']

    def validate(self, data):
        if data['password'] != data['password_confirm']:
            raise serializers.ValidationError({"password_confirm": "Passwords do not match."})
        return data

    def create(self, validated_data):
        validated_data.pop('password_confirm')
        user = User.objects.create_user(**validated_data)
        return user


class StripeCustomerSerializer(serializers.ModelSerializer):
    """Serializer for the StripeCustomer model."""
    user = UserSerializer(read_only=True)
    user_id = serializers.UUIDField(write_only=True)

    class Meta:
        model = StripeCustomer
        fields = ['id', 'user', 'user_id', 'stripe_customer_id', 'email', 'name',
                  'phone', 'address', 'created_at', 'updated_at', 'metadata']
        read_only_fields = ['id', 'stripe_customer_id', 'created_at', 'updated_at']


class StripePaymentMethodSerializer(serializers.ModelSerializer):
    """Serializer for the StripePaymentMethod model."""
    customer = StripeCustomerSerializer(read_only=True)
    customer_id = serializers.UUIDField(write_only=True)
    display_name = serializers.SerializerMethodField()

    class Meta:
        model = StripePaymentMethod
        fields = ['id', 'customer', 'customer_id', 'stripe_payment_method_id', 'type',
                  'card_brand', 'card_last4', 'card_exp_month', 'card_exp_year',
                  'is_default', 'is_active', 'display_name', 'created_at', 'updated_at']
        read_only_fields = ['id', 'stripe_payment_method_id', 'created_at', 'updated_at']

    @extend_schema_field(serializers.CharField())
    def get_display_name(self, obj):
        """Get a user-friendly display name for the payment method."""
        if obj.type == 'card' and obj.card_last4:
            return f"{obj.card_brand.title() if obj.card_brand else 'Card'} ending in {obj.card_last4}"
        return f"{obj.type.replace('_', ' ').title()}"


class StripeRefundSerializer(serializers.ModelSerializer):
    """Serializer for the StripeRefund model."""
    payment = serializers.StringRelatedField(read_only=True)
    payment_id = serializers.UUIDField(write_only=True)
    created_by = UserSerializer(read_only=True)

    class Meta:
        model = StripeRefund
        fields = ['id', 'payment', 'payment_id', 'stripe_refund_id', 'amount', 'currency',
                  'status', 'reason', 'receipt_number', 'created_by', 'created_at', 'updated_at']
        read_only_fields = ['id', 'stripe_refund_id', 'created_at', 'updated_at']


class StripePaymentSerializer(serializers.ModelSerializer):
    """
    Enhanced serializer for the StripePayment model.

    Provides a complete representation of Stripe payment data including:
    - Nested order, customer, and payment method data
    - Payment intent and method IDs
    - Payment status and timestamps
    - Refund information
    """
    order = OrderSerializer(read_only=True)
    order_id = serializers.UUIDField(write_only=True)
    customer = StripeCustomerSerializer(read_only=True)
    payment_method = StripePaymentMethodSerializer(read_only=True)
    refunds = StripeRefundSerializer(many=True, read_only=True)
    is_refundable = serializers.ReadOnlyField()
    refundable_amount = serializers.ReadOnlyField()

    class Meta:
        model = StripePayment
        fields = ['id', 'order', 'order_id', 'customer', 'payment_intent_id', 'payment_method',
                  'stripe_payment_method_id', 'charge_id', 'amount', 'amount_refunded', 'currency',
                  'status', 'payment_type', 'failure_code', 'failure_message', 'receipt_email',
                  'receipt_url', 'is_refundable', 'refundable_amount', 'refunds', 'created_at',
                  'updated_at', 'metadata']
        read_only_fields = ['id', 'payment_intent_id', 'charge_id', 'created_at', 'updated_at']


class StripeWebhookEventSerializer(serializers.ModelSerializer):
    """Serializer for the StripeWebhookEvent model."""

    class Meta:
        model = StripeWebhookEvent
        fields = ['id', 'stripe_event_id', 'event_type', 'processing_status', 'api_version',
                  'livemode', 'processed_at', 'error_message', 'retry_count', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']


class PaymentAnalyticsSerializer(serializers.ModelSerializer):
    """Serializer for the PaymentAnalytics model."""

    class Meta:
        model = PaymentAnalytics
        fields = ['id', 'period_type', 'period_start', 'period_end', 'total_payments',
                  'total_amount', 'successful_payments', 'failed_payments', 'refunded_payments',
                  'total_refunded_amount', 'average_payment_amount', 'unique_customers',
                  'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']


class SupplierSerializer(serializers.ModelSerializer):
    """Serializer for the Supplier model"""
    class Meta:
        model = Supplier
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')


class InventoryBatchSerializer(serializers.ModelSerializer):
    """Serializer for the InventoryBatch model"""
    product_title = serializers.CharField(source='product.title', read_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    is_expired = serializers.BooleanField(read_only=True)
    days_until_expiry = serializers.IntegerField(read_only=True)
    is_low_stock = serializers.BooleanField(read_only=True)

    class Meta:
        model = InventoryBatch
        fields = '__all__'
        read_only_fields = ('received_date',)


class InventoryChangeSerializer(serializers.ModelSerializer):
    """
    Serializer for the InventoryChange model.

    Provides a complete representation of inventory changes including:
    - Nested product data
    - User who made the change
    - Reference to related order (if applicable)
    - Detailed change information (quantities, timestamps, etc.)

    Uses write-only ID fields for creating records while providing
    full nested objects in responses.
    """
    product = ProductSerializer(read_only=True)
    product_id = serializers.UUIDField(write_only=True)
    batch = InventoryBatchSerializer(read_only=True)
    batch_id = serializers.UUIDField(write_only=True, required=False, allow_null=True)
    changed_by = UserSerializer(read_only=True)
    reference_order = OrderSerializer(read_only=True)
    reference_order_id = serializers.UUIDField(write_only=True, required=False, allow_null=True)

    class Meta:
        model = InventoryChange
        fields = ['id', 'product', 'product_id', 'batch', 'batch_id', 'quantity_change', 'change_type',
                  'previous_quantity', 'new_quantity', 'changed_by', 'reference_order',
                  'reference_order_id', 'notes', 'timestamp']
        read_only_fields = ['id', 'previous_quantity', 'new_quantity', 'timestamp']


class SalesReportSerializer(serializers.ModelSerializer):
    """Serializer for the SalesReport model"""
    top_selling_product_title = serializers.CharField(source='top_selling_product.title', read_only=True)

    class Meta:
        model = SalesReport
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')


class PromocodeSerializer(serializers.ModelSerializer):
    """
    Serializer for the Promocode model.

    Provides a complete representation of promocode data for API responses.
    """
    applicable_categories = serializers.StringRelatedField(many=True, read_only=True)
    applicable_products = serializers.StringRelatedField(many=True, read_only=True)
    created_by = serializers.StringRelatedField(read_only=True)

    class Meta:
        model = Promocode
        fields = ['id', 'code', 'description', 'discount_type', 'discount_value',
                  'valid_from', 'valid_until', 'usage_limit', 'times_used',
                  'applicable_to', 'applicable_categories', 'applicable_products',
                  'minimum_order_amount', 'is_active', 'created_by', 'created_at', 'updated_at']
        read_only_fields = ['id', 'times_used', 'created_at', 'updated_at']


class PromoCodeUsageSerializer(serializers.ModelSerializer):
    """
    Serializer for the PromoCodeUsage model.

    Provides a complete representation of promocode usage data for API responses.
    """
    promocode = PromocodeSerializer(read_only=True)
    user = UserSerializer(read_only=True)
    order = OrderSerializer(read_only=True)

    class Meta:
        model = PromoCodeUsage
        fields = ['id', 'promocode', 'user', 'order', 'discount_amount',
                  'order_amount_before_discount', 'order_amount_after_discount',
                  'used_at', 'ip_address', 'user_agent']
        read_only_fields = ['id', 'used_at']


class InventoryAlertSerializer(serializers.ModelSerializer):
    """Serializer for InventoryAlert model"""

    alert_type_display = serializers.CharField(source='get_alert_type_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    product_title = serializers.CharField(source='product.title', read_only=True)
    batch_number = serializers.CharField(source='batch.batch_number', read_only=True)
    acknowledged_by_username = serializers.CharField(source='acknowledged_by.username', read_only=True)

    # Computed fields
    days_since_created = serializers.SerializerMethodField()
    is_overdue = serializers.SerializerMethodField()

    class Meta:
        model = InventoryAlert
        fields = [
            'id', 'alert_type', 'alert_type_display', 'priority', 'priority_display',
            'title', 'message', 'product', 'product_title', 'batch', 'batch_number',
            'is_active', 'is_acknowledged', 'acknowledged_by', 'acknowledged_by_username',
            'acknowledged_at', 'threshold_value', 'expiry_date', 'created_at', 'updated_at',
            'days_since_created', 'is_overdue'
        ]
        read_only_fields = ['created_at', 'updated_at', 'acknowledged_at']

    @extend_schema_field(serializers.IntegerField())
    def get_days_since_created(self, obj):
        """Calculate days since alert was created"""
        from django.utils import timezone
        delta = timezone.now().date() - obj.created_at.date()
        return delta.days

    @extend_schema_field(serializers.BooleanField())
    def get_is_overdue(self, obj):
        """Check if alert is overdue (older than 7 days and not acknowledged)"""
        if obj.is_acknowledged:
            return False

        from django.utils import timezone
        delta = timezone.now().date() - obj.created_at.date()
        return delta.days > 7
