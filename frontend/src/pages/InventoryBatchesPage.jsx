import React, { useEffect, useState, useCallback } from 'react';
import Layout from '../components/Layout.jsx';
import toast from 'react-hot-toast';
import { inventoryService } from '../services/api';

const InventoryBatchesPage = () => {
  const [days, setDays] = useState(30);
  const [expiring, setExpiring] = useState([]);
  const [expired, setExpired] = useState([]);
  const [loading, setLoading] = useState(true);

  const load = useCallback(async () => {
    setLoading(true);
    try {
      const [expRes, exdRes] = await Promise.all([
        inventoryService.getBatchesExpiringSoon(days),
        inventoryService.getBatchesExpired(),
      ]);
      setExpiring(expRes.data?.results || expRes.data || []);
      setExpired(exdRes.data?.results || exdRes.data || []);
    } catch (e) {
      console.error(e);
      toast.error('Failed to load batches');
    } finally { setLoading(false); }
  }, [days]);

  useEffect(() => { load(); }, [load]);

  return (
    <Layout>
      <div className="container mx-auto py-10 px-4 space-y-8">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Inventory Batches</h1>
          <div className="flex items-center gap-2">
            <label className="text-sm text-gray-600">Expiring within</label>
            <input type="number" min="1" value={days} onChange={(e)=>setDays(Number(e.target.value)||1)} className="w-24 border rounded px-3 py-2" />
            <span className="text-sm text-gray-600">days</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow border border-gray-100">
          <div className="px-6 py-4 border-b flex items-center justify-between">
            <h2 className="text-lg font-semibold">Expiring Soon</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="text-left text-sm text-gray-500">
                  <th className="px-6 py-3">Batch</th>
                  <th className="px-6 py-3">Product</th>
                  <th className="px-6 py-3">Expiry</th>
                  <th className="px-6 py-3">Remaining</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr><td className="px-6 py-6" colSpan={4}>Loading...</td></tr>
                ) : expiring.length ? expiring.map(b => (
                  <tr key={b.id} className="border-t">
                    <td className="px-6 py-3">{b.batch_number}</td>
                    <td className="px-6 py-3">{b.product?.title || b.product_title || '\u2014'}</td>
                    <td className="px-6 py-3">{b.expiry_date}</td>
                    <td className="px-6 py-3">{b.quantity_remaining}</td>
                  </tr>
                )) : (
                  <tr><td className="px-6 py-6" colSpan={4}>No results</td></tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow border border-gray-100">
          <div className="px-6 py-4 border-b flex items-center justify-between">
            <h2 className="text-lg font-semibold">Expired Batches</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="text-left text-sm text-gray-500">
                  <th className="px-6 py-3">Batch</th>
                  <th className="px-6 py-3">Product</th>
                  <th className="px-6 py-3">Expiry</th>
                  <th className="px-6 py-3">Remaining</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr><td className="px-6 py-6" colSpan={4}>Loading...</td></tr>
                ) : expired.length ? expired.map(b => (
                  <tr key={b.id} className="border-t">
                    <td className="px-6 py-3">{b.batch_number}</td>
                    <td className="px-6 py-3">{b.product?.title || b.product_title || '\u2014'}</td>
                    <td className="px-6 py-3">{b.expiry_date}</td>
                    <td className="px-6 py-3">{b.quantity_remaining}</td>
                  </tr>
                )) : (
                  <tr><td className="px-6 py-6" colSpan={4}>No results</td></tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default InventoryBatchesPage;

