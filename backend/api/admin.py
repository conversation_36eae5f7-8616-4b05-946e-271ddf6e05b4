from django.contrib import admin
from .models import (
    User, Role, UserRole, Credential, SocialProfile,
    Category, Product, ProductImage, ProductModerationLog,
    Cart, CartItem, Order, OrderLine,
    Review, PersonalizedRecommendation, Offer, UserBehavior,
    InventoryChange, InventoryBatch, Supplier, SalesReport, InventoryAlert,
    StripePayment, Promocode, PromoCodeUsage
)

@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    list_display = ('username', 'email', 'first_name', 'last_name', 'is_staff', 'date_joined')
    search_fields = ('username', 'email', 'first_name', 'last_name')
    list_filter = ('is_staff', 'is_active', 'date_joined')

@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ('name', 'description')

@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    list_display = ('user', 'role', 'assigned_at')
    list_filter = ('role', 'assigned_at')

@admin.register(Credential)
class CredentialAdmin(admin.ModelAdmin):
    list_display = ('user', 'provider_id', 'created_at')
    list_filter = ('provider_id', 'created_at')

@admin.register(SocialProfile)
class SocialProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'platform', 'platform_user', 'created_at')
    list_filter = ('platform', 'created_at')

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug', 'parent_category', 'created_at')
    search_fields = ('name', 'description')
    list_filter = ('created_at',)
    prepopulated_fields = {'slug': ('name',)}

class ProductImageInline(admin.TabularInline):
    model = ProductImage
    extra = 1
    max_num = 4  # Maximum 4 additional images (5 total with main picture)

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('title', 'category', 'price', 'stock_quantity', 'is_active', 'created_at')
    search_fields = ('title', 'description')
    list_filter = ('category', 'is_active', 'created_at')
    prepopulated_fields = {'slug': ('title',)}
    inlines = [ProductImageInline]

@admin.register(ProductImage)
class ProductImageAdmin(admin.ModelAdmin):
    list_display = ('product', 'order', 'alt_text', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('product__title', 'alt_text')

@admin.register(ProductModerationLog)
class ProductModerationLogAdmin(admin.ModelAdmin):
    list_display = ('product', 'moderator', 'action', 'timestamp')
    list_filter = ('action', 'timestamp')

@admin.register(Cart)
class CartAdmin(admin.ModelAdmin):
    list_display = ('id', 'created_by', 'status', 'created_at', 'updated_at')
    list_filter = ('status', 'created_at')

@admin.register(CartItem)
class CartItemAdmin(admin.ModelAdmin):
    list_display = ('cart', 'product', 'price', 'quantity', 'created_at')

@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'total_price', 'payment_status', 'delivery_status', 'created_at')
    list_filter = ('payment_status', 'delivery_status', 'created_at')

@admin.register(OrderLine)
class OrderLineAdmin(admin.ModelAdmin):
    list_display = ('order', 'product', 'price', 'quantity')

@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ('user', 'product', 'rating', 'created_at')
    list_filter = ('rating', 'created_at')

@admin.register(PersonalizedRecommendation)
class PersonalizedRecommendationAdmin(admin.ModelAdmin):
    list_display = ('user', 'product', 'recommendation_score', 'created_at')
    list_filter = ('created_at',)

@admin.register(Offer)
class OfferAdmin(admin.ModelAdmin):
    list_display = ('name', 'product', 'offer_type', 'discount_value', 'start_date', 'end_date', 'is_active')
    list_filter = ('offer_type', 'is_festival', 'is_active', 'start_date', 'end_date')

@admin.register(UserBehavior)
class UserBehaviorAdmin(admin.ModelAdmin):
    list_display = ('user', 'action', 'product', 'action_time')
    list_filter = ('action', 'action_time')

@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    list_display = ('name', 'contact_person', 'email', 'phone', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'contact_person', 'email')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(InventoryBatch)
class InventoryBatchAdmin(admin.ModelAdmin):
    list_display = ('product', 'batch_number', 'supplier', 'quantity_remaining', 'quantity_received', 'expiry_date', 'is_active')
    list_filter = ('supplier', 'is_active', 'expiry_date', 'received_date')
    search_fields = ('product__title', 'batch_number', 'supplier__name')
    readonly_fields = ('received_date',)
    date_hierarchy = 'expiry_date'

@admin.register(InventoryChange)
class InventoryChangeAdmin(admin.ModelAdmin):
    list_display = ('product', 'batch', 'change_type', 'quantity_change', 'changed_by', 'timestamp')
    list_filter = ('change_type', 'timestamp')
    search_fields = ('product__title', 'batch__batch_number', 'notes')
    readonly_fields = ('previous_quantity', 'new_quantity', 'timestamp')

@admin.register(SalesReport)
class SalesReportAdmin(admin.ModelAdmin):
    list_display = ('report_date', 'report_type', 'total_orders', 'total_revenue', 'total_units_sold', 'average_order_value')
    list_filter = ('report_type', 'report_date')
    search_fields = ('report_date',)
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'report_date'

@admin.register(StripePayment)
class StripePaymentAdmin(admin.ModelAdmin):
    list_display = ('order', 'payment_intent_id', 'amount', 'status', 'created_at')
    list_filter = ('status', 'created_at')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(Promocode)
class PromocodeAdmin(admin.ModelAdmin):
    list_display = ('code', 'discount_type', 'discount_value', 'valid_from', 'valid_until', 'is_active', 'times_used')
    list_filter = ('discount_type', 'applicable_to', 'is_active', 'valid_from', 'valid_until')
    search_fields = ('code', 'description')
    readonly_fields = ('times_used', 'created_at', 'updated_at')

@admin.register(PromoCodeUsage)
class PromoCodeUsageAdmin(admin.ModelAdmin):
    list_display = ('promocode', 'user', 'order', 'discount_amount', 'used_at')
    list_filter = ('used_at',)
    readonly_fields = ('used_at',)


@admin.register(InventoryAlert)
class InventoryAlertAdmin(admin.ModelAdmin):
    list_display = ['title', 'alert_type', 'priority', 'product', 'batch', 'is_active', 'is_acknowledged', 'created_at']
    list_filter = ['alert_type', 'priority', 'is_active', 'is_acknowledged', 'created_at']
    search_fields = ['title', 'message', 'product__title', 'batch__batch_number']
    readonly_fields = ['created_at', 'updated_at', 'acknowledged_at']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Alert Information', {
            'fields': ('alert_type', 'priority', 'title', 'message')
        }),
        ('Related Objects', {
            'fields': ('product', 'batch')
        }),
        ('Status', {
            'fields': ('is_active', 'is_acknowledged', 'acknowledged_by', 'acknowledged_at')
        }),
        ('Metadata', {
            'fields': ('threshold_value', 'expiry_date')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_acknowledged', 'mark_dismissed']

    def mark_acknowledged(self, request, queryset):
        """Mark selected alerts as acknowledged"""
        count = 0
        for alert in queryset:
            if not alert.is_acknowledged:
                alert.acknowledge(request.user)
                count += 1

        self.message_user(request, f'{count} alerts marked as acknowledged.')
    mark_acknowledged.short_description = "Mark selected alerts as acknowledged"

    def mark_dismissed(self, request, queryset):
        """Dismiss selected alerts"""
        count = 0
        for alert in queryset:
            if alert.is_active:
                alert.dismiss()
                count += 1

        self.message_user(request, f'{count} alerts dismissed.')
    mark_dismissed.short_description = "Dismiss selected alerts"
