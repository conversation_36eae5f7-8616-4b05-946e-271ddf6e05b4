import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { EnvelopeIcon } from '@heroicons/react/24/outline';

// Import components
import Layout from '../components/Layout.jsx';

const ForgotPasswordPage = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email) {
      toast.error('Please enter your email address');
      return;
    }

    try {
      setLoading(true);

      // In a real application, this would call an API endpoint
      // For now, we'll just simulate a successful request
      await new Promise(resolve => setTimeout(resolve, 1000));

      setSubmitted(true);
      toast.success('Password reset instructions sent to your email');
    } catch (error) {
      toast.error('Failed to send password reset email');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="mx-auto max-w-md px-4 py-16 sm:px-6 lg:px-8">
        <div className="rounded-lg bg-white px-8 py-10 shadow-md">
          {submitted ? (
              <div className="text-center">
                <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                  <EnvelopeIcon className="h-6 w-6 text-green-600" aria-hidden="true" />
                </div>
                <h2 className="mt-4 text-2xl font-bold tracking-tight text-gray-900">Check your email</h2>
                <p className="mt-2 text-gray-600">
                  We've sent password reset instructions to {email}. Please check your inbox.
                </p>
                <div className="mt-6">
                  <Link
                    to="/login"
                    className="text-primary-600 hover:text-primary-500"
                  >
                    Back to login
                  </Link>
                </div>
              </div>
            ) : (
              <>
                <div className="text-center">
                  <h2 className="text-2xl font-bold tracking-tight text-gray-900">Forgot your password?</h2>
                  <p className="mt-2 text-gray-600">
                    Enter your email address and we'll send you instructions to reset your password.
                  </p>
                </div>
                <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                      Email address
                    </label>
                    <div className="mt-1">
                      <input
                        id="email"
                        name="email"
                        type="email"
                        autoComplete="email"
                        required
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                      />
                    </div>
                  </div>
                  <div>
                    <button
                      type="submit"
                      disabled={loading}
                      className="flex w-full justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50"
                    >
                      {loading ? 'Sending...' : 'Reset Password'}
                    </button>
                  </div>
                </form>
                <div className="mt-6 text-center">
                  <Link
                    to="/login"
                    className="text-sm text-primary-600 hover:text-primary-500"
                  >
                    Back to login
                  </Link>
                </div>
              </>
            )}
        </div>
      </div>
    </Layout>
  );
};

export default ForgotPasswordPage;
