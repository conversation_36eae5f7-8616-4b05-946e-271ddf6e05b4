import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import Layout from '../components/Layout.jsx';
import { useAnalytics } from '../contexts/AnalyticsContext.jsx';
import { useCart } from '../contexts/CartContext.jsx';

const CheckoutPage = () => {
  const { trackCheckoutStartAction, trackPurchaseAction } = useAnalytics();
  const { cart } = useCart();

  // Track checkout start when page loads
  useEffect(() => {
    if (cart && cart.items && cart.items.length > 0) {
      const totalValue = cart.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      trackCheckoutStartAction(cart.items, totalValue);
    }
  }, [cart, trackCheckoutStartAction]);

  const handlePlaceOrder = () => {
    // Simulate order completion
    const orderData = {
      id: 'ORDER_' + Date.now(),
      total_price: cart ? cart.items.reduce((sum, item) => sum + (item.price * item.quantity), 0) : 0,
      items: cart ? cart.items : [],
      payment_method: 'demo',
      shipping_cost: 0,
      tax_amount: 0
    };

    // Track purchase completion
    trackPurchaseAction(orderData);
  };

  return (
    <Layout>
      {/* Main Content */}
      <div className="container mx-auto py-12 px-4">
        <h1 className="text-3xl font-bold mb-8">Checkout</h1>
        <div className="bg-white rounded-lg shadow-md p-8">
          <h2 className="text-xl font-semibold mb-4">Checkout Page</h2>
          <p className="text-gray-600 mb-4">This is a placeholder for the checkout page.</p>

          {cart && cart.items && cart.items.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-4">Order Summary</h3>
              <div className="space-y-2">
                {cart.items.map((item, index) => (
                  <div key={index} className="flex justify-between">
                    <span>{item.product?.name || 'Product'} x {item.quantity}</span>
                    <span>${((item.price || 0) * item.quantity).toFixed(2)}</span>
                  </div>
                ))}
                <div className="border-t pt-2 font-semibold">
                  <div className="flex justify-between">
                    <span>Total:</span>
                    <span>${cart.items.reduce((sum, item) => sum + ((item.price || 0) * item.quantity), 0).toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          <Link
            to="/order-confirmation"
            onClick={handlePlaceOrder}
            className="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 inline-block"
          >
            Place Order (Demo)
          </Link>
        </div>
      </div>
    </Layout>
  );
};

export default CheckoutPage;
