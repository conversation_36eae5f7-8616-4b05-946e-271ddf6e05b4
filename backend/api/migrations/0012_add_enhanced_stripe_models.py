# Generated by Django 4.2.7 on 2025-10-09 20:09

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0011_optimize_models_and_add_indexes'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentAnalytics',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('period_type', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('yearly', 'Yearly')], db_index=True, max_length=10)),
                ('period_start', models.DateTimeField(db_index=True)),
                ('period_end', models.DateTimeField(db_index=True)),
                ('total_payments', models.IntegerField(default=0)),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('successful_payments', models.IntegerField(default=0)),
                ('failed_payments', models.IntegerField(default=0)),
                ('refunded_payments', models.IntegerField(default=0)),
                ('total_refunded_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('average_payment_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('unique_customers', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='StripeCustomer',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('stripe_customer_id', models.CharField(db_index=True, max_length=255, unique=True)),
                ('email', models.EmailField(db_index=True, max_length=254)),
                ('name', models.CharField(blank=True, max_length=255, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('address', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
            ],
        ),
        migrations.CreateModel(
            name='StripePaymentMethod',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('stripe_payment_method_id', models.CharField(db_index=True, max_length=255, unique=True)),
                ('type', models.CharField(choices=[('card', 'Card'), ('bank_account', 'Bank Account'), ('apple_pay', 'Apple Pay'), ('google_pay', 'Google Pay'), ('paypal', 'PayPal')], db_index=True, max_length=20)),
                ('card_brand', models.CharField(blank=True, max_length=20, null=True)),
                ('card_last4', models.CharField(blank=True, max_length=4, null=True)),
                ('card_exp_month', models.IntegerField(blank=True, null=True)),
                ('card_exp_year', models.IntegerField(blank=True, null=True)),
                ('is_default', models.BooleanField(db_index=True, default=False)),
                ('is_active', models.BooleanField(db_index=True, default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
            ],
        ),
        migrations.CreateModel(
            name='StripeRefund',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('stripe_refund_id', models.CharField(db_index=True, max_length=255, unique=True)),
                ('amount', models.DecimalField(db_index=True, decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('succeeded', 'Succeeded'), ('failed', 'Failed'), ('canceled', 'Canceled')], db_index=True, default='pending', max_length=20)),
                ('reason', models.CharField(blank=True, choices=[('duplicate', 'Duplicate'), ('fraudulent', 'Fraudulent'), ('requested_by_customer', 'Requested by Customer'), ('expired_uncaptured_charge', 'Expired Uncaptured Charge'), ('other', 'Other')], max_length=30, null=True)),
                ('receipt_number', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
            ],
        ),
        migrations.CreateModel(
            name='StripeWebhookEvent',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('stripe_event_id', models.CharField(db_index=True, max_length=255, unique=True)),
                ('event_type', models.CharField(choices=[('payment_intent.succeeded', 'Payment Intent Succeeded'), ('payment_intent.payment_failed', 'Payment Intent Failed'), ('payment_intent.canceled', 'Payment Intent Canceled'), ('charge.succeeded', 'Charge Succeeded'), ('charge.failed', 'Charge Failed'), ('charge.refunded', 'Charge Refunded'), ('charge.dispute.created', 'Dispute Created'), ('customer.created', 'Customer Created'), ('customer.updated', 'Customer Updated'), ('payment_method.attached', 'Payment Method Attached'), ('invoice.payment_succeeded', 'Invoice Payment Succeeded'), ('invoice.payment_failed', 'Invoice Payment Failed')], db_index=True, max_length=50)),
                ('processing_status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('processed', 'Processed'), ('failed', 'Failed'), ('ignored', 'Ignored')], db_index=True, default='pending', max_length=20)),
                ('api_version', models.CharField(blank=True, max_length=20, null=True)),
                ('livemode', models.BooleanField(db_index=True, default=False)),
                ('data', models.JSONField()),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('retry_count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.RemoveField(
            model_name='stripepayment',
            name='payment_method_id',
        ),
        migrations.AddField(
            model_name='stripepayment',
            name='amount_refunded',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AddField(
            model_name='stripepayment',
            name='charge_id',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='stripepayment',
            name='failure_code',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='stripepayment',
            name='failure_message',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='stripepayment',
            name='payment_type',
            field=models.CharField(choices=[('payment', 'Payment'), ('refund', 'Refund'), ('partial_refund', 'Partial Refund')], db_index=True, default='payment', max_length=20),
        ),
        migrations.AddField(
            model_name='stripepayment',
            name='receipt_email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='stripepayment',
            name='receipt_url',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='stripepayment',
            name='stripe_payment_method_id',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='stripepayment',
            name='amount',
            field=models.DecimalField(db_index=True, decimal_places=2, max_digits=10),
        ),
        migrations.AlterField(
            model_name='stripepayment',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name='stripepayment',
            name='currency',
            field=models.CharField(db_index=True, default='USD', max_length=3),
        ),
        migrations.AlterField(
            model_name='stripepayment',
            name='payment_intent_id',
            field=models.CharField(db_index=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='stripepayment',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('succeeded', 'Succeeded'), ('failed', 'Failed'), ('refunded', 'Refunded'), ('partially_refunded', 'Partially Refunded'), ('canceled', 'Canceled'), ('disputed', 'Disputed')], db_index=True, default='pending', max_length=20),
        ),
        migrations.AddIndex(
            model_name='stripepayment',
            index=models.Index(fields=['payment_intent_id'], name='api_stripep_payment_2a3174_idx'),
        ),
        migrations.AddIndex(
            model_name='stripepayment',
            index=models.Index(fields=['order', 'status'], name='api_stripep_order_i_5698ce_idx'),
        ),
        migrations.AddIndex(
            model_name='stripepayment',
            index=models.Index(fields=['customer', 'status'], name='api_stripep_custome_5e4a22_idx'),
        ),
        migrations.AddIndex(
            model_name='stripepayment',
            index=models.Index(fields=['status', 'created_at'], name='api_stripep_status_94bad3_idx'),
        ),
        migrations.AddIndex(
            model_name='stripepayment',
            index=models.Index(fields=['amount', 'currency'], name='api_stripep_amount_d24c52_idx'),
        ),
        migrations.AddIndex(
            model_name='stripepayment',
            index=models.Index(fields=['payment_type', 'status'], name='api_stripep_payment_3abb02_idx'),
        ),
        migrations.AddIndex(
            model_name='stripepayment',
            index=models.Index(fields=['created_at', 'status'], name='api_stripep_created_dcd99a_idx'),
        ),
        migrations.AddConstraint(
            model_name='stripepayment',
            constraint=models.CheckConstraint(check=models.Q(('amount__gte', 0)), name='stripe_payment_amount_non_negative'),
        ),
        migrations.AddConstraint(
            model_name='stripepayment',
            constraint=models.CheckConstraint(check=models.Q(('amount_refunded__gte', 0)), name='stripe_payment_amount_refunded_non_negative'),
        ),
        migrations.AddConstraint(
            model_name='stripepayment',
            constraint=models.CheckConstraint(check=models.Q(('amount_refunded__lte', models.F('amount'))), name='stripe_payment_refund_not_exceed_amount'),
        ),
        migrations.AddIndex(
            model_name='stripewebhookevent',
            index=models.Index(fields=['stripe_event_id'], name='api_stripew_stripe__bca299_idx'),
        ),
        migrations.AddIndex(
            model_name='stripewebhookevent',
            index=models.Index(fields=['event_type', 'processing_status'], name='api_stripew_event_t_157f44_idx'),
        ),
        migrations.AddIndex(
            model_name='stripewebhookevent',
            index=models.Index(fields=['processing_status', 'created_at'], name='api_stripew_process_1ec82f_idx'),
        ),
        migrations.AddIndex(
            model_name='stripewebhookevent',
            index=models.Index(fields=['livemode', 'event_type'], name='api_stripew_livemod_4c7413_idx'),
        ),
        migrations.AddIndex(
            model_name='stripewebhookevent',
            index=models.Index(fields=['created_at', 'processing_status'], name='api_stripew_created_371ae5_idx'),
        ),
        migrations.AddConstraint(
            model_name='stripewebhookevent',
            constraint=models.CheckConstraint(check=models.Q(('retry_count__gte', 0)), name='webhook_event_retry_count_non_negative'),
        ),
        migrations.AddField(
            model_name='striperefund',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_refunds', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='striperefund',
            name='payment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='refunds', to='api.stripepayment'),
        ),
        migrations.AddField(
            model_name='stripepaymentmethod',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_methods', to='api.stripecustomer'),
        ),
        migrations.AddField(
            model_name='stripecustomer',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='stripe_customer', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='paymentanalytics',
            index=models.Index(fields=['period_type', 'period_start'], name='api_payment_period__e28945_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentanalytics',
            index=models.Index(fields=['period_start', 'period_end'], name='api_payment_period__cb9c17_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentanalytics',
            index=models.Index(fields=['total_amount', 'period_type'], name='api_payment_total_a_c8139a_idx'),
        ),
        migrations.AddConstraint(
            model_name='paymentanalytics',
            constraint=models.UniqueConstraint(fields=('period_type', 'period_start'), name='unique_analytics_period'),
        ),
        migrations.AddConstraint(
            model_name='paymentanalytics',
            constraint=models.CheckConstraint(check=models.Q(('period_end__gt', models.F('period_start'))), name='analytics_period_end_after_start'),
        ),
        migrations.AddField(
            model_name='stripepayment',
            name='customer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payments', to='api.stripecustomer'),
        ),
        migrations.AddField(
            model_name='stripepayment',
            name='payment_method',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='api.stripepaymentmethod'),
        ),
        migrations.AddIndex(
            model_name='striperefund',
            index=models.Index(fields=['stripe_refund_id'], name='api_striper_stripe__e466aa_idx'),
        ),
        migrations.AddIndex(
            model_name='striperefund',
            index=models.Index(fields=['payment', 'status'], name='api_striper_payment_05c572_idx'),
        ),
        migrations.AddIndex(
            model_name='striperefund',
            index=models.Index(fields=['status', 'created_at'], name='api_striper_status_896847_idx'),
        ),
        migrations.AddIndex(
            model_name='striperefund',
            index=models.Index(fields=['amount', 'currency'], name='api_striper_amount_e5f5e0_idx'),
        ),
        migrations.AddIndex(
            model_name='striperefund',
            index=models.Index(fields=['created_by', 'created_at'], name='api_striper_created_2891c9_idx'),
        ),
        migrations.AddConstraint(
            model_name='striperefund',
            constraint=models.CheckConstraint(check=models.Q(('amount__gt', 0)), name='stripe_refund_amount_positive'),
        ),
        migrations.AddIndex(
            model_name='stripepaymentmethod',
            index=models.Index(fields=['customer', 'is_default'], name='api_stripep_custome_83f399_idx'),
        ),
        migrations.AddIndex(
            model_name='stripepaymentmethod',
            index=models.Index(fields=['customer', 'is_active'], name='api_stripep_custome_fbe990_idx'),
        ),
        migrations.AddIndex(
            model_name='stripepaymentmethod',
            index=models.Index(fields=['type', 'is_active'], name='api_stripep_type_9b37ac_idx'),
        ),
        migrations.AddIndex(
            model_name='stripepaymentmethod',
            index=models.Index(fields=['stripe_payment_method_id'], name='api_stripep_stripe__84febb_idx'),
        ),
        migrations.AddIndex(
            model_name='stripecustomer',
            index=models.Index(fields=['stripe_customer_id'], name='api_stripec_stripe__fa8bf9_idx'),
        ),
        migrations.AddIndex(
            model_name='stripecustomer',
            index=models.Index(fields=['email', 'created_at'], name='api_stripec_email_93a350_idx'),
        ),
        migrations.AddIndex(
            model_name='stripecustomer',
            index=models.Index(fields=['user', 'created_at'], name='api_stripec_user_id_fb21bc_idx'),
        ),
        migrations.AddConstraint(
            model_name='stripecustomer',
            constraint=models.CheckConstraint(check=models.Q(('email__isnull', False), models.Q(('email', ''), _negated=True)), name='stripe_customer_email_not_empty'),
        ),
    ]
