from __future__ import annotations
import os

# Set default Django settings module for 'celery' command-line program.
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "ecommerce_backend.settings")

try:
    from celery import Celery
except Exception:  # Celery may not be installed yet; avoid import-time crash
    Celery = None

app = None
if Celery is not None:
    app = Celery("ecommerce_backend")
    # Using a string here means the worker doesn't have to serialize
    # the configuration object to child processes.
    # - namespace='CELERY' means all celery-related config keys
    #   should have a `CELERY_` prefix in Django settings.
    app.config_from_object("django.conf:settings", namespace="CELERY")
    # Auto-discover tasks from all registered Django app configs.
    app.autodiscover_tasks()

__all__ = ("app",)

