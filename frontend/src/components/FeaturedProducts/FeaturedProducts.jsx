import React, { useEffect, useState } from 'react';
import { fetchFeaturedProducts } from '../../services/api'; // Fetch from API
import ProductCard from '../ProductCard.jsx';
import toast from 'react-hot-toast';

const FeaturedProducts = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const getFeaturedProducts = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fetchFeaturedProducts();
        setProducts(response.data.results || response.data);
      } catch (err) {
        console.error('Error fetching featured products:', err);
        setError('Failed to load featured products');
        toast.error('Failed to load featured products');
      } finally {
        setLoading(false);
      }
    };
    getFeaturedProducts();
  }, []);

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
        {[1, 2, 3].map((i) => (
          <div key={i} className="animate-pulse bg-gray-200 h-64 rounded-lg"></div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
      {products.map((product) => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  );
};

export default FeaturedProducts;
