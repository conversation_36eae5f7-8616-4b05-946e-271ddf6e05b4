"""
Management command to generate inventory alerts automatically.

This command can be run daily via cron job to:
1. Check for low stock products and create alerts
2. Check for expiring inventory batches and create alerts
3. Generate reorder suggestions based on sales patterns
4. Clean up old acknowledged alerts
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from api.models import Product, InventoryBatch, InventoryAlert, Order, OrderLine
from django.db.models import Sum


class Command(BaseCommand):
    help = 'Generate inventory alerts for low stock, expiring items, and reorder suggestions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--low-stock-threshold',
            type=int,
            default=10,
            help='Threshold for low stock alerts (default: 10)',
        )
        parser.add_argument(
            '--expiry-warning-days',
            type=int,
            default=30,
            help='Days before expiry to create warnings (default: 30)',
        )
        parser.add_argument(
            '--cleanup-days',
            type=int,
            default=30,
            help='Days after which to clean up acknowledged alerts (default: 30)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        self.stdout.write('Generating inventory alerts...')
        self.stdout.write('')

        # Generate low stock alerts
        self.generate_low_stock_alerts(options)
        
        # Generate expiry alerts
        self.generate_expiry_alerts(options)
        
        # Generate reorder suggestions
        self.generate_reorder_suggestions(options)
        
        # Clean up old alerts
        self.cleanup_old_alerts(options)
        
        self.stdout.write('')
        self.stdout.write(self.style.SUCCESS('Inventory alert generation completed!'))

    def generate_low_stock_alerts(self, options):
        """Generate alerts for low stock products"""
        threshold = options['low_stock_threshold']
        
        self.stdout.write(f'Checking for products with stock below {threshold} units...')
        
        low_stock_products = Product.objects.filter(
            stock_quantity__lte=threshold,
            is_active=True
        )
        
        alert_count = 0
        for product in low_stock_products:
            if not options['dry_run']:
                alert = InventoryAlert.create_low_stock_alert(
                    product=product,
                    current_stock=product.stock_quantity,
                    threshold=threshold
                )
                self.stdout.write(f'  Created/updated alert: {alert.title}')
                alert_count += 1
            else:
                self.stdout.write(f'  DRY RUN: Would create low stock alert for {product.title} (stock: {product.stock_quantity})')
        
        if alert_count > 0:
            self.stdout.write(self.style.WARNING(f'Generated {alert_count} low stock alerts'))
        else:
            self.stdout.write(self.style.SUCCESS('No low stock alerts needed'))
        self.stdout.write('')

    def generate_expiry_alerts(self, options):
        """Generate alerts for expiring inventory batches"""
        warning_days = options['expiry_warning_days']
        today = timezone.now().date()
        warning_date = today + timedelta(days=warning_days)
        
        self.stdout.write(f'Checking for batches expiring within {warning_days} days...')
        
        # Find batches expiring soon or already expired
        expiring_batches = InventoryBatch.objects.filter(
            expiry_date__lte=warning_date,
            quantity_remaining__gt=0,
            is_active=True
        )
        
        alert_count = 0
        for batch in expiring_batches:
            days_until_expiry = (batch.expiry_date - today).days
            
            if not options['dry_run']:
                alert = InventoryAlert.create_expiry_alert(batch, days_until_expiry)
                status = "expired" if days_until_expiry < 0 else f"expires in {days_until_expiry} days"
                self.stdout.write(f'  Created/updated alert: {batch.product.title} - Batch {batch.batch_number} ({status})')
                alert_count += 1
            else:
                status = "expired" if days_until_expiry < 0 else f"expires in {days_until_expiry} days"
                self.stdout.write(f'  DRY RUN: Would create expiry alert for {batch.product.title} - Batch {batch.batch_number} ({status})')
        
        if alert_count > 0:
            self.stdout.write(self.style.WARNING(f'Generated {alert_count} expiry alerts'))
        else:
            self.stdout.write(self.style.SUCCESS('No expiry alerts needed'))
        self.stdout.write('')

    def generate_reorder_suggestions(self, options):
        """Generate reorder suggestions based on sales patterns"""
        self.stdout.write('Analyzing sales patterns for reorder suggestions...')
        
        # Look at sales from the last 30 days
        thirty_days_ago = timezone.now().date() - timedelta(days=30)
        
        # Get products with recent sales but low stock
        recent_orders = OrderLine.objects.filter(
            order__created_at__gte=thirty_days_ago,
            order__payment_status='paid'
        ).values('product').annotate(
            total_sold=Sum('quantity')
        )
        
        suggestion_count = 0
        for order_data in recent_orders:
            try:
                product = Product.objects.get(id=order_data['product'])
                total_sold = order_data['total_sold']
                
                # Calculate average daily sales
                avg_daily_sales = total_sold / 30
                
                # Suggest reorder if current stock will last less than 14 days
                days_of_stock = product.stock_quantity / avg_daily_sales if avg_daily_sales > 0 else float('inf')
                
                if days_of_stock < 14 and product.stock_quantity > 0:  # Only suggest if not already out of stock
                    suggested_quantity = int(avg_daily_sales * 30)  # 30 days worth of stock
                    
                    if not options['dry_run']:
                        # Check if reorder suggestion already exists
                        existing_alert = InventoryAlert.objects.filter(
                            product=product,
                            alert_type='reorder_suggestion',
                            is_active=True,
                            is_acknowledged=False
                        ).first()
                        
                        if not existing_alert:
                            alert = InventoryAlert.objects.create(
                                alert_type='reorder_suggestion',
                                priority='medium',
                                title=f"Reorder Suggestion: {product.title}",
                                message=f"Based on recent sales ({total_sold} units in 30 days), current stock ({product.stock_quantity} units) will last approximately {days_of_stock:.1f} days. Suggested reorder quantity: {suggested_quantity} units.",
                                product=product,
                                threshold_value=suggested_quantity
                            )
                            self.stdout.write(f'  Created reorder suggestion: {product.title} (suggest {suggested_quantity} units)')
                            suggestion_count += 1
                    else:
                        self.stdout.write(f'  DRY RUN: Would suggest reordering {suggested_quantity} units of {product.title}')
                        
            except Product.DoesNotExist:
                continue
        
        if suggestion_count > 0:
            self.stdout.write(self.style.SUCCESS(f'Generated {suggestion_count} reorder suggestions'))
        else:
            self.stdout.write(self.style.SUCCESS('No reorder suggestions needed'))
        self.stdout.write('')

    def cleanup_old_alerts(self, options):
        """Clean up old acknowledged alerts"""
        cleanup_days = options['cleanup_days']
        cutoff_date = timezone.now() - timedelta(days=cleanup_days)
        
        self.stdout.write(f'Cleaning up acknowledged alerts older than {cleanup_days} days...')
        
        old_alerts = InventoryAlert.objects.filter(
            is_acknowledged=True,
            acknowledged_at__lt=cutoff_date
        )
        
        count = old_alerts.count()
        if count > 0:
            if not options['dry_run']:
                old_alerts.delete()
                self.stdout.write(self.style.SUCCESS(f'Cleaned up {count} old acknowledged alerts'))
            else:
                self.stdout.write(f'DRY RUN: Would clean up {count} old acknowledged alerts')
        else:
            self.stdout.write(self.style.SUCCESS('No old alerts to clean up'))
        self.stdout.write('')
