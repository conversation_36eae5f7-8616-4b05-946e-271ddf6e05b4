"""
Management command to generate sales reports automatically.

This command can be run daily via cron job to generate sales reports
for different time periods (daily, weekly, monthly, yearly).
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from api.models import SalesReport


class Command(BaseCommand):
    help = 'Generate sales reports for specified date and type'

    def add_arguments(self, parser):
        parser.add_argument(
            '--date',
            type=str,
            help='Date for the report (YYYY-MM-DD format). Defaults to today.',
        )
        parser.add_argument(
            '--type',
            type=str,
            choices=['daily', 'weekly', 'monthly', 'yearly'],
            default='daily',
            help='Type of report to generate (default: daily)',
        )
        parser.add_argument(
            '--all-types',
            action='store_true',
            help='Generate reports for all types (daily, weekly, monthly, yearly)',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force regeneration of existing reports',
        )

    def handle(self, *args, **options):
        # Parse the date
        if options['date']:
            try:
                report_date = datetime.strptime(options['date'], '%Y-%m-%d').date()
            except ValueError:
                self.stdout.write(
                    self.style.ERROR('Invalid date format. Use YYYY-MM-DD.')
                )
                return
        else:
            report_date = timezone.now().date()

        # Determine which report types to generate
        if options['all_types']:
            report_types = ['daily', 'weekly', 'monthly', 'yearly']
        else:
            report_types = [options['type']]

        self.stdout.write(f'Generating sales reports for {report_date}...')

        for report_type in report_types:
            try:
                # Check if report already exists
                existing_report = SalesReport.objects.filter(
                    report_date=report_date,
                    report_type=report_type
                ).first()

                if existing_report and not options['force']:
                    self.stdout.write(
                        self.style.WARNING(
                            f'{report_type.title()} report for {report_date} already exists. '
                            'Use --force to regenerate.'
                        )
                    )
                    continue

                if existing_report:
                    # Update existing report
                    existing_report.calculate_metrics()
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'Updated {report_type} report for {report_date}'
                        )
                    )
                else:
                    # Create new report
                    report = SalesReport.objects.create(
                        report_date=report_date,
                        report_type=report_type
                    )
                    report.calculate_metrics()
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'Generated {report_type} report for {report_date}'
                        )
                    )

                # Display report summary
                if existing_report:
                    report = existing_report
                
                self.stdout.write(f'  - Total Orders: {report.total_orders}')
                self.stdout.write(f'  - Total Revenue: ${report.total_revenue}')
                self.stdout.write(f'  - Units Sold: {report.total_units_sold}')
                self.stdout.write(f'  - Average Order Value: ${report.average_order_value}')
                self.stdout.write('')

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'Error generating {report_type} report for {report_date}: {str(e)}'
                    )
                )

        self.stdout.write(
            self.style.SUCCESS('Sales report generation completed!')
        )
