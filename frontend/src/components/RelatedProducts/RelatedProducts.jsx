import React, { useEffect, useState } from 'react';
import { fetchRelatedProducts } from '../../services/api';
import ProductCard from '../components/ProductCard/ProductCard';

const RelatedProducts = ({ productId }) => {
  const [relatedProducts, setRelatedProducts] = useState([]);

  useEffect(() => {
    const getRelatedProducts = async () => {
      const response = await fetchRelatedProducts(productId);
      setRelatedProducts(response.data);
    };
    getRelatedProducts();
  }, [productId]);

  return (
    <div className="bg-white p-6 shadow-md rounded-lg mt-8">
      <h2 className="text-2xl font-semibold mb-4">Related Products</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {relatedProducts.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>
    </div>
  );
};

export default RelatedProducts;
