import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';

const ShippingStep = ({ data, onDataChange, checkout, onCheckoutUpdate }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [countries] = useState([
    { code: 'US', name: 'United States' },
    { code: 'CA', name: 'Canada' },
    { code: 'GB', name: 'United Kingdom' },
    { code: 'DE', name: 'Germany' },
    { code: 'FR', name: 'France' },
    // Add more countries as needed
  ]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    trigger
  } = useForm({
    defaultValues: data,
    mode: 'onChange'
  });

  const watchedValues = watch();

  useEffect(() => {
    // Update parent component with form data
    onDataChange(watchedValues);
  }, [watchedValues, onDataChange]);

  useEffect(() => {
    // Pre-fill form if checkout has shipping address
    if (checkout?.shippingAddress) {
      const address = checkout.shippingAddress;
      setValue('firstName', address.firstName);
      setValue('lastName', address.lastName);
      setValue('streetAddress1', address.streetAddress1);
      setValue('streetAddress2', address.streetAddress2);
      setValue('city', address.city);
      setValue('postalCode', address.postalCode);
      setValue('country', address.country.code);
      setValue('countryArea', address.countryArea);
    }
  }, [checkout, setValue]);

  const onSubmit = async (formData) => {
    try {
      setIsLoading(true);
      // TODO: Implement shipping address update with Django API
      onDataChange(formData);
      toast.success('Shipping address updated');
    } catch (error) {
      console.error('Error updating shipping address:', error);
      toast.error('Failed to update shipping address');
    } finally {
      setIsLoading(false);
    }
  };

  // Real-time validation on blur
  const handleFieldBlur = async (fieldName) => {
    await trigger(fieldName);
  };

  return (
    <motion.form
      onSubmit={handleSubmit(onSubmit)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      {/* Name Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            First Name *
          </label>
          <input
            type="text"
            {...register('firstName', { 
              required: 'First name is required',
              minLength: { value: 2, message: 'First name must be at least 2 characters' }
            })}
            onBlur={() => handleFieldBlur('firstName')}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors ${
              errors.firstName ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter your first name"
          />
          {errors.firstName && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-red-500 text-sm mt-1"
            >
              {errors.firstName.message}
            </motion.p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Last Name *
          </label>
          <input
            type="text"
            {...register('lastName', { 
              required: 'Last name is required',
              minLength: { value: 2, message: 'Last name must be at least 2 characters' }
            })}
            onBlur={() => handleFieldBlur('lastName')}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors ${
              errors.lastName ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter your last name"
          />
          {errors.lastName && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-red-500 text-sm mt-1"
            >
              {errors.lastName.message}
            </motion.p>
          )}
        </div>
      </div>

      {/* Address Fields */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Street Address *
        </label>
        <input
          type="text"
          {...register('streetAddress1', { 
            required: 'Street address is required',
            minLength: { value: 5, message: 'Please enter a complete address' }
          })}
          onBlur={() => handleFieldBlur('streetAddress1')}
          className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors ${
            errors.streetAddress1 ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder="Enter your street address"
        />
        {errors.streetAddress1 && (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-red-500 text-sm mt-1"
          >
            {errors.streetAddress1.message}
          </motion.p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Apartment, suite, etc. (optional)
        </label>
        <input
          type="text"
          {...register('streetAddress2')}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors"
          placeholder="Apartment, suite, unit, etc."
        />
      </div>

      {/* City, Postal Code, Country */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            City *
          </label>
          <input
            type="text"
            {...register('city', { 
              required: 'City is required',
              minLength: { value: 2, message: 'Please enter a valid city' }
            })}
            onBlur={() => handleFieldBlur('city')}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors ${
              errors.city ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter your city"
          />
          {errors.city && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-red-500 text-sm mt-1"
            >
              {errors.city.message}
            </motion.p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Postal Code *
          </label>
          <input
            type="text"
            {...register('postalCode', { 
              required: 'Postal code is required',
              pattern: {
                value: /^[A-Za-z0-9\s-]{3,10}$/,
                message: 'Please enter a valid postal code'
              }
            })}
            onBlur={() => handleFieldBlur('postalCode')}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors ${
              errors.postalCode ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter postal code"
          />
          {errors.postalCode && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-red-500 text-sm mt-1"
            >
              {errors.postalCode.message}
            </motion.p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Country *
          </label>
          <select
            {...register('country', { required: 'Country is required' })}
            onBlur={() => handleFieldBlur('country')}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors ${
              errors.country ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <option value="">Select country</option>
            {countries.map(country => (
              <option key={country.code} value={country.code}>
                {country.name}
              </option>
            ))}
          </select>
          {errors.country && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-red-500 text-sm mt-1"
            >
              {errors.country.message}
            </motion.p>
          )}
        </div>
      </div>

      {/* State/Province (optional) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          State/Province (optional)
        </label>
        <input
          type="text"
          {...register('countryArea')}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors"
          placeholder="Enter state or province"
        />
      </div>

      {/* Save Button */}
      <motion.button
        type="submit"
        disabled={isLoading}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className="w-full bg-primary-600 text-white py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors disabled:opacity-50"
      >
        {isLoading ? (
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" />
            Saving...
          </div>
        ) : (
          'Save Shipping Address'
        )}
      </motion.button>
    </motion.form>
  );
};

export default ShippingStep;
