# Generated by Django 4.2.7 on 2025-10-09 17:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0009_alter_supplier_options'),
    ]

    operations = [
        migrations.CreateModel(
            name='InventoryAlert',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('alert_type', models.CharField(choices=[('low_stock', 'Low Stock'), ('out_of_stock', 'Out of Stock'), ('expiring_soon', 'Expiring Soon'), ('expired', 'Expired'), ('reorder_suggestion', 'Reorder Suggestion')], max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='medium', max_length=10)),
                ('title', models.CharField(max_length=255)),
                ('message', models.TextField()),
                ('is_active', models.BooleanField(default=True)),
                ('is_acknowledged', models.BooleanField(default=False)),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True)),
                ('threshold_value', models.IntegerField(blank=True, help_text='Threshold value that triggered this alert', null=True)),
                ('expiry_date', models.DateField(blank=True, help_text='Expiry date for expiry-related alerts', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('acknowledged_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='acknowledged_alerts', to=settings.AUTH_USER_MODEL)),
                ('batch', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='alerts', to='api.inventorybatch')),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='inventory_alerts', to='api.product')),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['alert_type', 'is_active'], name='api_invento_alert_t_d119bb_idx'), models.Index(fields=['priority', 'is_active'], name='api_invento_priorit_af5968_idx'), models.Index(fields=['product', 'is_active'], name='api_invento_product_eed360_idx'), models.Index(fields=['is_acknowledged', 'is_active'], name='api_invento_is_ackn_ccbb64_idx')],
            },
        ),
    ]
