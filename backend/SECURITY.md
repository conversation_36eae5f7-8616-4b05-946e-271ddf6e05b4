# Security Configuration Guide

This document outlines the security measures implemented in the eCommerce backend and provides guidance for deployment and maintenance.

## Security Features Implemented

### 1. CSRF Protection
- **Django CSRF Middleware**: Enabled with proper token handling
- **CSRF Token Endpoint**: `/api/auth/csrf-token/` for frontend applications
- **Exempted Endpoints**: Login, register, password reset, and webhooks
- **Configuration**: 
  - `CSRF_COOKIE_SECURE`: True in production
  - `CSRF_COOKIE_HTTPONLY`: True
  - `CSRF_COOKIE_SAMESITE`: 'Lax'

### 2. CORS Configuration
- **Allowed Origins**: Configurable via environment variables
- **Credentials Support**: Enabled for authenticated requests
- **Headers**: Properly configured for API access
- **Methods**: GET, POST, PUT, PATCH, DELETE, OPTIONS

### 3. Rate Limiting
- **Global Rate Limits**: 
  - Anonymous users: 100 requests/hour (50 in production)
  - Authenticated users: 1000 requests/hour (500 in production)
- **Endpoint-Specific Limits**:
  - Login: 5 requests/minute (3 in production)
  - Registration: 3 requests/minute (2 in production)
  - Password reset: 3 requests/hour (2 in production)
  - Orders: 10 requests/minute (5 in production)
  - Payments: 5 requests/minute (3 in production)

### 4. Security Middleware
- **SecurityMiddleware**: IP blocking and suspicious activity detection
- **RateLimitMiddleware**: Global rate limiting
- **APILoggingMiddleware**: Request logging for monitoring
- **RequestSizeMiddleware**: Limits request size to 10MB

### 5. Authentication Security
- **JWT Tokens**: Secure token-based authentication
- **Password Hashing**: Django's built-in PBKDF2 algorithm
- **Password Reset**: Secure token-based reset mechanism
- **Throttling**: Rate limiting on authentication endpoints

### 6. HTTP Security Headers
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`
- `Strict-Transport-Security` (HSTS) in production

## Environment Variables

### Required for Production
```bash
# Security
SECRET_KEY=your-secret-key-here
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com

# SSL/HTTPS
SECURE_SSL_REDIRECT=True
CSRF_COOKIE_SECURE=True
SESSION_COOKIE_SECURE=True

# CORS
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
CSRF_TRUSTED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Database
DB_NAME=ecommerce_db
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_HOST=localhost
DB_PORT=3306

# Redis (for caching and rate limiting)
REDIS_URL=redis://127.0.0.1:6379/1

# Email
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_app_password
DEFAULT_FROM_EMAIL=<EMAIL>

# Stripe
STRIPE_PUBLISHABLE_KEY_LIVE=pk_live_...
STRIPE_SECRET_KEY_LIVE=sk_live_...
STRIPE_WEBHOOK_SECRET_LIVE=whsec_...
```

## Deployment Checklist

### Before Deployment
- [ ] Generate a strong SECRET_KEY (50+ characters)
- [ ] Set DEBUG=False
- [ ] Configure ALLOWED_HOSTS
- [ ] Set up SSL/HTTPS
- [ ] Configure database with proper credentials
- [ ] Set up Redis for caching
- [ ] Configure email settings
- [ ] Set up proper logging directories
- [ ] Review and set CORS origins
- [ ] Configure Stripe production keys

### Security Monitoring
- [ ] Set up log monitoring
- [ ] Configure security alerts
- [ ] Regular security scans
- [ ] Monitor rate limiting effectiveness
- [ ] Review blocked IPs regularly

## Management Commands

### Security Monitoring
```bash
# Monitor security activities
python manage.py security_monitor --action monitor --hours 24

# Show security statistics
python manage.py security_monitor --action stats --hours 24

# Clear blocked IPs
python manage.py security_monitor --action clear_blocks

# Cleanup old logs
python manage.py security_monitor --action cleanup
```

### Inventory Alerts (Security-related)
```bash
# Generate inventory alerts (includes security checks)
python manage.py generate_inventory_alerts

# Handle expired inventory
python manage.py handle_expired_inventory
```

## API Security Best Practices

### For Frontend Developers
1. **Always include CSRF token** in state-changing requests
2. **Use HTTPS** in production
3. **Handle rate limiting** gracefully (429 responses)
4. **Validate user input** on the frontend
5. **Don't expose sensitive data** in client-side code

### For Backend Maintenance
1. **Regular security updates** for dependencies
2. **Monitor logs** for suspicious activity
3. **Review rate limiting** effectiveness
4. **Update CORS origins** as needed
5. **Rotate secrets** regularly

## Incident Response

### If Suspicious Activity Detected
1. Check security logs: `/var/log/django/security.log`
2. Review blocked IPs: `python manage.py security_monitor --action stats`
3. Analyze patterns in API logs
4. Consider temporary rate limit adjustments
5. Block malicious IPs if necessary

### If Rate Limits Too Restrictive
1. Review current limits in settings
2. Analyze legitimate user patterns
3. Adjust limits in `REST_FRAMEWORK['DEFAULT_THROTTLE_RATES']`
4. Monitor impact after changes

## Testing Security

### CSRF Testing
```bash
# Test CSRF token endpoint
curl -X GET http://localhost:8000/api/auth/csrf-token/

# Test CSRF protection on login
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test"}'
```

### Rate Limiting Testing
```bash
# Test rate limiting (should get 429 after limits)
for i in {1..10}; do
  curl -X POST http://localhost:8000/api/auth/login/ \
    -H "Content-Type: application/json" \
    -d '{"username":"test","password":"test"}'
done
```

## Additional Security Considerations

### Database Security
- Use strong database passwords
- Limit database user permissions
- Enable database logging
- Regular database backups
- Encrypt sensitive data at rest

### Infrastructure Security
- Use a reverse proxy (nginx/Apache)
- Configure firewall rules
- Regular OS updates
- Monitor system resources
- Use container security if applicable

### Application Security
- Regular dependency updates
- Code security reviews
- Input validation
- Output encoding
- Secure file uploads
