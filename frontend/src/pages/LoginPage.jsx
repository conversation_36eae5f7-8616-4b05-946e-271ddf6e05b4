import React, { useEffect, useRef, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Layout from '../components/Layout.jsx';
import { authService } from '../services/api';
import { useAuth } from '../contexts/AuthContext.jsx';

const LoginPage = () => {
  const navigate = useNavigate();
  const googleBtnRef = useRef(null);
  const [googleReady, setGoogleReady] = useState(false);
  const clientId = process.env.REACT_APP_GOOGLE_CLIENT_ID;
  const { login } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errorMessage, setErrorMessage] = useState('');


  useEffect(() => {
    const initGoogle = () => {
      if (!window.google || !googleBtnRef.current || !clientId) return;
      try {
        window.google.accounts.id.initialize({
          client_id: clientId,
          callback: async (response) => {
            try {
              const { data } = await authService.googleLogin(response.credential);
              if (data && data.access && data.refresh) {
                localStorage.setItem('token', data.access);
                localStorage.setItem('refreshToken', data.refresh);
                navigate('/');
              }
            } catch (err) {
              console.error('Google login failed', err);
              alert('Google login failed. Please try again.');
            }
          },
          ux_mode: 'popup',
        });
        window.google.accounts.id.renderButton(googleBtnRef.current, { theme: 'outline', size: 'large', width: 320 });
        setGoogleReady(true);
      } catch (e) {
        console.error('Failed to initialize Google Sign-In', e);
      }
    };

    if (document.readyState === 'complete') initGoogle();
    else window.addEventListener('load', initGoogle);
    return () => window.removeEventListener('load', initGoogle);
  }, [clientId, navigate]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrorMessage('');
    try {
      await login({ email, password });
      navigate('/');
    } catch (err) {
      setErrorMessage(err?.response?.data?.error || 'Invalid email or password');
    }
  };

  return (
    <Layout>
      {/* Main Content */}
      <div className="container mx-auto py-12 px-4">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
          <div className="py-4 px-6 bg-blue-500 text-white text-center">
            <h2 className="text-2xl font-bold">Login to Your Account</h2>
          </div>
          <div className="py-8 px-6">
            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <label htmlFor="email" className="block text-gray-700 font-medium mb-2">Email Address</label>
                <input type="email" id="email" className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-blue-500" placeholder="Enter your email" value={email} onChange={(e) => setEmail(e.target.value)} required />
              </div>
              <div className="mb-6">
                <label htmlFor="password" className="block text-gray-700 font-medium mb-2">Password</label>
                <input type="password" id="password" className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-blue-500" placeholder="Enter your password" value={password} onChange={(e) => setPassword(e.target.value)} required />
              </div>
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <input type="checkbox" id="remember" className="mr-2" />
                  <label htmlFor="remember" className="text-gray-600">Remember me</label>
                </div>
                <Link to="/forgot-password" className="text-blue-500 hover:text-blue-600">Forgot password?</Link>
              </div>
              {errorMessage && (
                <p className="text-sm text-red-600 mb-4" role="alert">{errorMessage}</p>
              )}
              <button type="submit" className="w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 focus:outline-none focus:bg-blue-600">Login</button>
            </form>

            <div className="mt-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="flex-1 h-px bg-gray-200" />
                <span className="text-gray-400 text-sm">OR</span>
                <div className="flex-1 h-px bg-gray-200" />
              </div>
              <div className="w-full flex justify-center">
                <div ref={googleBtnRef} />
              </div>
              {!clientId && (
                <p className="text-xs text-red-500 mt-2 text-center">Missing REACT_APP_GOOGLE_CLIENT_ID</p>
              )}
              {clientId && !googleReady && (
                <p className="text-xs text-gray-500 mt-2 text-center">Loading Google Sign-In…</p>
              )}
            </div>

            <div className="mt-6 text-center">
              <p className="text-gray-600">
                Don't have an account?
                <Link to="/register" className="text-blue-500 hover:text-blue-600 ml-1">Register</Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default LoginPage;
