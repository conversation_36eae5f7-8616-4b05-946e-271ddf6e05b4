<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>Payment {{ status|title }} for Order #{{ order.id }}</title>
    <style>
      body { background-color:#f5f7fb;margin:0;padding:0;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Arial,sans-serif; }
      .container { max-width:560px;margin:20px auto;background:#ffffff;border-radius:12px;overflow:hidden;box-shadow:0 4px 16px rgba(0,0,0,0.05); }
      .header { background:linear-gradient(90deg,#2363eb,#1f49b6);color:#fff;padding:20px 24px; }
      .brand { margin:0;font-size:20px;font-weight:700; }
      .content { padding:24px;color:#111827; }
      .muted { color:#6b7280;font-size:12px; }
      .cta { display:inline-block;padding:12px 18px;background:#2563eb;color:#fff!important;text-decoration:none;border-radius:8px;font-weight:600; }
      a { color:#2563eb; }
      .summary { background:#f9fafb;border:1px solid #eef2f7;border-radius:10px;padding:14px;margin-top:12px; }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1 class="brand">{{ site_name }}</h1>
      </div>
      <div class="content">
        <p>Hi{% if user.first_name %} {{ user.first_name }}{% endif %},</p>
        <p>Your payment for order <strong>#{{ order.id }}</strong> is <strong>{{ status|title }}</strong>.</p>
        <div class="summary">
          <div>Total: <strong>{{ order.total_price }}</strong></div>
          <div>Payment Status: <strong>{{ order.payment_status|title }}</strong></div>
        </div>
        <p style="margin:24px 0;">
          <a href="{{ frontend_url }}/orders/{{ order.id }}" class="cta" target="_blank">View your order</a>
        </p>
      </div>
      <div class="content muted" style="text-align:center;border-top:1px solid #eef2f7;">
        &copy; {{ site_name }} &bull; <a href="{{ frontend_url }}" target="_blank">Visit site</a>
      </div>
    </div>
  </body>
</html>

