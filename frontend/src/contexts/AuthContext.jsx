import React, { createContext, useState, useEffect, useContext, useCallback } from 'react';
import { authService } from '../services/api';

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchCurrentUser = useCallback(async () => {
    try {
      setLoading(true);
      const response = await authService.getCurrentUser();
      setCurrentUser(response.data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch user data');
      // If token is invalid, clear it
      if (err.response && err.response.status === 401) {
        logout();
      }
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    // Check if user is logged in on page load
    const token = localStorage.getItem('token');
    if (token) {
      fetchCurrentUser();
    } else {
      setLoading(false);
    }
  }, [fetchCurrentUser]);


  const login = async (credentials) => {
    try {
      setLoading(true);
      const response = await authService.login(credentials);
      const { access, refresh, user } = response.data;

      // Save tokens to localStorage
      localStorage.setItem('token', access);
      localStorage.setItem('refreshToken', refresh);

      // Set current user
      setCurrentUser(user);
      setError(null);

      return user;
    } catch (err) {
      setError(err.response?.data?.error || 'Login failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData) => {
    try {
      setLoading(true);
      const response = await authService.register(userData);
      const { access, refresh, user } = response.data;

      // Save tokens to localStorage
      localStorage.setItem('token', access);
      localStorage.setItem('refreshToken', refresh);

      // Set current user
      setCurrentUser(user);
      setError(null);

      return user;
    } catch (err) {
      setError(err.response?.data || 'Registration failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    authService.logout();
    setCurrentUser(null);
  };

  const updateProfile = async (userData) => {
    try {
      setLoading(true);
      const response = await authService.updateProfile(userData);
      setCurrentUser(response.data);
      setError(null);
      return response.data;
    } catch (err) {
      setError(err.response?.data || 'Failed to update profile');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const value = {
    currentUser,
    loading,
    error,
    login,
    register,
    logout,
    updateProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthContext;
