"""
Centralized error handling utilities for the eCommerce backend API.

This module provides consistent error response patterns and exception handling
across the entire backend application.
"""
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import exception_handler
from django.core.exceptions import ValidationError
from django.db import IntegrityError
import logging

logger = logging.getLogger(__name__)


def custom_exception_handler(exc, context):
    """
    Custom exception handler that provides consistent error responses.
    
    Args:
        exc: The exception instance
        context: The context in which the exception occurred
        
    Returns:
        Response: Standardized error response
    """
    # Call REST framework's default exception handler first
    response = exception_handler(exc, context)
    
    # If the response is None, it means the exception wasn't handled by DRF
    if response is None:
        # Handle Django validation errors
        if isinstance(exc, ValidationError):
            return Response({
                'error': 'Validation failed',
                'message': str(exc),
                'details': exc.message_dict if hasattr(exc, 'message_dict') else None
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Handle database integrity errors
        if isinstance(exc, IntegrityError):
            return Response({
                'error': 'Database integrity error',
                'message': 'The operation violates database constraints',
                'details': str(exc)
            }, status=status.HTTP_409_CONFLICT)
        
        # Handle other unexpected errors
        logger.error(f"Unhandled exception: {exc}", exc_info=True)
        return Response({
            'error': 'Internal server error',
            'message': 'An unexpected error occurred'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    # Customize the response format for DRF exceptions
    custom_response_data = {
        'error': response.data.get('detail', 'An error occurred'),
        'status_code': response.status_code
    }
    
    # Add field-specific errors for validation failures
    if response.status_code == status.HTTP_400_BAD_REQUEST:
        if isinstance(response.data, dict):
            # Extract field errors
            field_errors = {}
            for field, errors in response.data.items():
                if field != 'detail':
                    field_errors[field] = errors if isinstance(errors, list) else [errors]
            
            if field_errors:
                custom_response_data['field_errors'] = field_errors
                custom_response_data['error'] = 'Validation failed'
    
    response.data = custom_response_data
    return response


class APIErrorMixin:
    """
    Mixin class that provides standardized error response methods for API views.
    """
    
    def error_response(self, message, status_code=status.HTTP_400_BAD_REQUEST, details=None):
        """
        Create a standardized error response.
        
        Args:
            message (str): Error message
            status_code (int): HTTP status code
            details (dict): Additional error details
            
        Returns:
            Response: Standardized error response
        """
        data = {
            'error': message,
            'status_code': status_code
        }
        
        if details:
            data['details'] = details
            
        return Response(data, status=status_code)
    
    def validation_error_response(self, errors):
        """
        Create a validation error response.
        
        Args:
            errors (dict): Field validation errors
            
        Returns:
            Response: Validation error response
        """
        return Response({
            'error': 'Validation failed',
            'field_errors': errors,
            'status_code': status.HTTP_400_BAD_REQUEST
        }, status=status.HTTP_400_BAD_REQUEST)
    
    def not_found_response(self, resource='Resource'):
        """
        Create a not found error response.
        
        Args:
            resource (str): Name of the resource that wasn't found
            
        Returns:
            Response: Not found error response
        """
        return self.error_response(
            f'{resource} not found',
            status.HTTP_404_NOT_FOUND
        )
    
    def permission_denied_response(self, message='Permission denied'):
        """
        Create a permission denied error response.
        
        Args:
            message (str): Permission error message
            
        Returns:
            Response: Permission denied error response
        """
        return self.error_response(message, status.HTTP_403_FORBIDDEN)
    
    def conflict_response(self, message='Resource conflict'):
        """
        Create a conflict error response.
        
        Args:
            message (str): Conflict error message
            
        Returns:
            Response: Conflict error response
        """
        return self.error_response(message, status.HTTP_409_CONFLICT)


def handle_database_error(func):
    """
    Decorator to handle common database errors in API views.
    
    Args:
        func: The view function to wrap
        
    Returns:
        function: Wrapped function with error handling
    """
    def wrapper(self, request, *args, **kwargs):
        try:
            return func(self, request, *args, **kwargs)
        except ValidationError as e:
            return self.validation_error_response({
                'non_field_errors': [str(e)]
            })
        except IntegrityError as e:
            logger.error(f"Database integrity error in {func.__name__}: {e}")
            return self.conflict_response(
                'The operation violates database constraints'
            )
        except Exception as e:
            logger.error(f"Unexpected error in {func.__name__}: {e}", exc_info=True)
            return self.error_response(
                'An unexpected error occurred',
                status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    return wrapper


def log_api_error(view_name, error, request_data=None):
    """
    Log API errors with context information.
    
    Args:
        view_name (str): Name of the view where error occurred
        error (Exception): The error that occurred
        request_data (dict): Request data for context
    """
    logger.error(
        f"API Error in {view_name}: {str(error)}",
        extra={
            'view_name': view_name,
            'error_type': type(error).__name__,
            'request_data': request_data
        },
        exc_info=True
    )


class ErrorMessages:
    """
    Centralized error messages for consistency across the API.
    """
    # Authentication errors
    INVALID_CREDENTIALS = "Invalid username or password"
    TOKEN_EXPIRED = "Authentication token has expired"
    TOKEN_INVALID = "Invalid authentication token"
    PERMISSION_DENIED = "You do not have permission to perform this action"
    
    # Validation errors
    REQUIRED_FIELD = "This field is required"
    INVALID_FORMAT = "Invalid format"
    INVALID_CHOICE = "Invalid choice"
    
    # Resource errors
    NOT_FOUND = "Resource not found"
    ALREADY_EXISTS = "Resource already exists"
    CANNOT_DELETE = "Cannot delete this resource"
    
    # Business logic errors
    INSUFFICIENT_STOCK = "Insufficient stock available"
    INVALID_PROMOCODE = "Invalid or expired promocode"
    ORDER_ALREADY_PROCESSED = "Order has already been processed"
    PAYMENT_FAILED = "Payment processing failed"
    
    # Generic errors
    INTERNAL_ERROR = "An internal error occurred"
    NETWORK_ERROR = "Network error occurred"
    TIMEOUT_ERROR = "Request timed out"
