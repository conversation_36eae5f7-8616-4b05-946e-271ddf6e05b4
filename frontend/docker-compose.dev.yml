version: '3'

services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: ecommerce_db
      MYSQL_USER: ecommerce_user
      MYSQL_PASSWORD: ecommerce_password
      MYSQL_ROOT_PASSWORD: root_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  backend:
    image: python:3.11-slim
    working_dir: /app
    command: >
      sh -c "apt-get update && apt-get install -y gcc default-libmysqlclient-dev build-essential pkg-config && \
      pip install pipenv && \
      pipenv install --dev && \
      pipenv run python manage.py migrate && \
      pipenv run python manage.py runserver 0.0.0.0:8000"
    ports:
      - "8000:8000"
    volumes:
      - ../backend:/app
    environment:
      - PYTHONUNBUFFERED=1
      - PIPENV_VENV_IN_PROJECT=1
      - DATABASE_NAME=ecommerce_db
      - DATABASE_USER=ecommerce_user
      - DATABASE_PASSWORD=ecommerce_password
      - DATABASE_HOST=mysql
      - DATABASE_PORT=3306
      - CELERY_BROKER_URL=redis://redis:6379/0
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - mysql
      - redis


  react-app:
    image: node:18-alpine
    working_dir: /app
    command: sh -c "npm install && npm start"
    ports:
      - "3000:3000"
    volumes:
      - .:/app
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
      - NODE_TLS_REJECT_UNAUTHORIZED=0
    stdin_open: true
    tty: true


volumes:
  mysql_data:
