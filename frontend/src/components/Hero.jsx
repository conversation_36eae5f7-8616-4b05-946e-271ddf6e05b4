import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeftIcon, ChevronRightIcon, PlayIcon, SparklesIcon } from '@heroicons/react/24/outline';

const Hero = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  const heroSlides = [
    {
      id: 1,
      title: "Summer Collection 2024",
      subtitle: "Discover the latest trends",
      description: "Explore our curated selection of premium fashion and lifestyle products",
      cta: "Shop Collection",
      ctaLink: "/products?category=clothing",
      bgGradient: "from-orange-400 via-pink-500 to-purple-600",
      stats: { products: "500+", discount: "Up to 50%" }
    },
    {
      id: 2,
      title: "Tech Innovation Hub",
      subtitle: "Future is here",
      description: "Cutting-edge electronics and gadgets that transform your daily life",
      cta: "Explore Tech",
      ctaLink: "/products?category=electronics",
      bgGradient: "from-blue-500 via-cyan-500 to-teal-500",
      stats: { products: "200+", discount: "Free Shipping" }
    },
    {
      id: 3,
      title: "Home & Living",
      subtitle: "Create your sanctuary",
      description: "Transform your space with our premium home and kitchen essentials",
      cta: "Shop Home",
      ctaLink: "/products?category=home-kitchen",
      bgGradient: "from-green-400 via-emerald-500 to-teal-600",
      stats: { products: "300+", discount: "Best Prices" }
    }
  ];

  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, heroSlides.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    setIsAutoPlaying(false);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length);
    setIsAutoPlaying(false);
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
    setIsAutoPlaying(false);
  };

  return (
    <div className="relative h-screen overflow-hidden">
      <AnimatePresence mode="wait">
        <motion.div
          key={currentSlide}
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.7, ease: "easeInOut" }}
          className={`absolute inset-0 bg-gradient-to-br ${heroSlides[currentSlide].bgGradient}`}
        >
          <div className="absolute inset-0 bg-black bg-opacity-20" />

          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-white/10" />
          </div>

          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-center">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center w-full">
              {/* Content */}
              <motion.div
                initial={{ x: -50, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.6 }}
                className="text-white space-y-6"
              >
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.3, duration: 0.6 }}
                  className="inline-flex items-center bg-white bg-opacity-20 backdrop-blur-sm px-4 py-2 rounded-full text-sm font-medium"
                >
                  <SparklesIcon className="w-4 h-4 mr-2" />
                  {heroSlides[currentSlide].subtitle}
                </motion.div>

                <motion.h1
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.4, duration: 0.6 }}
                  className="text-5xl md:text-7xl font-bold leading-tight"
                >
                  {heroSlides[currentSlide].title}
                </motion.h1>

                <motion.p
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.5, duration: 0.6 }}
                  className="text-xl md:text-2xl text-gray-100 max-w-lg"
                >
                  {heroSlides[currentSlide].description}
                </motion.p>

                {/* Stats */}
                <motion.div
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.55, duration: 0.6 }}
                  className="flex gap-8"
                >
                  <div className="text-center">
                    <div className="text-2xl font-bold">{heroSlides[currentSlide].stats.products}</div>
                    <div className="text-sm text-gray-200">Products</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{heroSlides[currentSlide].stats.discount}</div>
                    <div className="text-sm text-gray-200">Special Offer</div>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.6, duration: 0.6 }}
                  className="flex flex-col sm:flex-row gap-4"
                >
                  <Link
                    to={heroSlides[currentSlide].ctaLink}
                    className="group bg-white text-gray-900 px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 hover:shadow-xl flex items-center justify-center"
                  >
                    {heroSlides[currentSlide].cta}
                    <motion.div
                      className="ml-2"
                      whileHover={{ x: 5 }}
                      transition={{ type: "spring", stiffness: 400 }}
                    >
                      →
                    </motion.div>
                  </Link>

                  <button
                    onClick={() => setIsAutoPlaying(!isAutoPlaying)}
                    className="group border-2 border-white text-white px-8 py-4 rounded-xl font-semibold hover:bg-white hover:text-gray-900 transition-all duration-300 flex items-center justify-center"
                  >
                    <PlayIcon className="w-5 h-5 mr-2" />
                    {isAutoPlaying ? 'Pause' : 'Play'} Tour
                  </button>
                </motion.div>
              </motion.div>

              {/* Interactive Visual Element */}
              <motion.div
                initial={{ x: 50, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.6 }}
                className="hidden lg:block"
              >
                <div className="relative">
                  <motion.div
                    animate={{
                      y: [0, -20, 0],
                      rotate: [0, 5, 0]
                    }}
                    transition={{
                      duration: 6,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    className="w-96 h-96 bg-white bg-opacity-10 backdrop-blur-sm rounded-3xl border border-white border-opacity-20 flex items-center justify-center relative overflow-hidden"
                  >
                    {/* Animated background */}
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                      className="absolute inset-0 bg-gradient-to-r from-transparent via-white via-transparent to-transparent opacity-5"
                    />

                    <div className="text-center z-10">
                      <motion.div
                        animate={{ scale: [1, 1.1, 1] }}
                        transition={{ duration: 3, repeat: Infinity }}
                        className="w-24 h-24 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center"
                      >
                        <div className="w-12 h-12 bg-white rounded-full" />
                      </motion.div>
                      <h3 className="text-2xl font-bold text-white mb-2">Premium Quality</h3>
                      <p className="text-white text-opacity-80">Curated for Excellence</p>
                    </div>
                  </motion.div>

                  {/* Floating Elements */}
                  {[...Array(3)].map((_, i) => (
                    <motion.div
                      key={i}
                      animate={{
                        y: [0, -15, 0],
                        x: [0, 10, 0],
                        rotate: [0, 180, 360]
                      }}
                      transition={{
                        duration: 4 + i,
                        repeat: Infinity,
                        ease: "easeInOut",
                        delay: i * 1.5
                      }}
                      className={`absolute w-${4 + i * 2} h-${4 + i * 2} bg-white bg-opacity-20 rounded-full`}
                      style={{
                        top: `${20 + i * 30}%`,
                        right: `${-10 + i * 5}%`
                      }}
                    />
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Navigation Controls */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className="flex items-center space-x-4">
          <button
            onClick={prevSlide}
            className="p-3 bg-white bg-opacity-20 backdrop-blur-sm rounded-full text-white hover:bg-opacity-30 transition-all duration-300 hover:scale-110"
          >
            <ChevronLeftIcon className="w-6 h-6" />
          </button>

          <div className="flex space-x-2">
            {heroSlides.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide
                    ? 'bg-white scale-125'
                    : 'bg-white bg-opacity-50 hover:bg-opacity-75'
                }`}
              />
            ))}
          </div>

          <button
            onClick={nextSlide}
            className="p-3 bg-white bg-opacity-20 backdrop-blur-sm rounded-full text-white hover:bg-opacity-30 transition-all duration-300 hover:scale-110"
          >
            <ChevronRightIcon className="w-6 h-6" />
          </button>
        </div>
      </div>

      {/* Progress Bar */}
      {isAutoPlaying && (
        <div className="absolute bottom-0 left-0 w-full h-1 bg-white bg-opacity-20 z-20">
          <motion.div
            className="h-full bg-white"
            initial={{ width: "0%" }}
            animate={{ width: "100%" }}
            transition={{ duration: 5, ease: "linear" }}
            key={currentSlide}
          />
        </div>
      )}
    </div>
  );
};

export default Hero;
