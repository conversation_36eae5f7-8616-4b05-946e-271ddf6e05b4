#!/usr/bin/env node

/**
 * Theme Switcher Script
 * 
 * Usage: node switch-theme.js [theme-name]
 * Available themes: modernDark, oceanBreeze, sunsetGlow, forestGreen, royalPurple
 */

const fs = require('fs');
const path = require('path');

const availableThemes = [
  'modernDark',
  'oceanBreeze', 
  'sunsetGlow',
  'forestGreen',
  'royalPurple'
];

const themeName = process.argv[2];

if (!themeName) {
  console.log('🎨 Theme Switcher for eCommerce Platform\n');
  console.log('Usage: node switch-theme.js [theme-name]\n');
  console.log('Available themes:');
  availableThemes.forEach(theme => {
    console.log(`  - ${theme}`);
  });
  console.log('\nExample: node switch-theme.js oceanBreeze');
  process.exit(1);
}

if (!availableThemes.includes(themeName)) {
  console.error(`❌ Error: "${themeName}" is not a valid theme.`);
  console.log('\nAvailable themes:');
  availableThemes.forEach(theme => {
    console.log(`  - ${theme}`);
  });
  process.exit(1);
}

const configPath = path.join(__dirname, 'tailwind.config.js');

try {
  let configContent = fs.readFileSync(configPath, 'utf8');
  
  // Replace the selected theme line
  const oldPattern = /const selectedTheme = themes\.\w+;/;
  const newLine = `const selectedTheme = themes.${themeName};`;
  
  if (configContent.match(oldPattern)) {
    configContent = configContent.replace(oldPattern, newLine);
    fs.writeFileSync(configPath, configContent);
    
    console.log(`✅ Successfully switched to "${themeName}" theme!`);
    console.log('\n📝 Next steps:');
    console.log('1. Restart your development server (npm start)');
    console.log('2. Visit /theme-preview to see the changes');
    console.log('3. Check your main pages to see the new theme applied');
  } else {
    console.error('❌ Error: Could not find theme selection line in tailwind.config.js');
    console.log('Please manually edit the file and change the selectedTheme variable.');
  }
} catch (error) {
  console.error('❌ Error reading/writing tailwind.config.js:', error.message);
  process.exit(1);
}
