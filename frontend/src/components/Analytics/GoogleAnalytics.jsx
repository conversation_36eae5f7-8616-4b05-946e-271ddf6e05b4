import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

// Google Analytics Measurement ID - Get from environment variable
const GA_MEASUREMENT_ID = process.env.REACT_APP_GA_TRACKING_ID || 'G-XXXXXXXXXX';

/**
 * GoogleAnalytics component that initializes Google Analytics and tracks page views
 * This component should be included once in your app, typically in the App component
 */
const GoogleAnalytics = ({ userId = null }) => {
  const location = useLocation();

  // Initialize Google Analytics
  useEffect(() => {
    // Check if the GA script is already loaded
    if (!window.gtag) {
      // Create script elements
      const gaScript = document.createElement('script');
      gaScript.async = true;
      gaScript.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;

      const inlineScript = document.createElement('script');
      inlineScript.innerHTML = `
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '${GA_MEASUREMENT_ID}', {
          send_page_view: false,
          custom_map: {
            'custom_parameter_1': 'user_type',
            'custom_parameter_2': 'product_category'
          }
        });
      `;

      // Append scripts to document
      document.head.appendChild(gaScript);
      document.head.appendChild(inlineScript);
    }
  }, []);

  // Set user ID for tracking individual users
  useEffect(() => {
    if (window.gtag && userId) {
      window.gtag('config', GA_MEASUREMENT_ID, {
        user_id: userId,
        custom_map: {
          'custom_parameter_1': 'user_type'
        }
      });
      console.log(`User ID set for tracking: ${userId}`);
    }
  }, [userId]);

  // Track page views with enhanced data
  useEffect(() => {
    if (window.gtag) {
      const pageData = {
        page_path: location.pathname + location.search,
        page_title: document.title,
        page_location: window.location.href
      };

      // Add user ID if available
      if (userId) {
        pageData.user_id = userId;
      }

      window.gtag('config', GA_MEASUREMENT_ID, pageData);

      // Also send as a page_view event for better tracking
      window.gtag('event', 'page_view', {
        page_title: document.title,
        page_location: window.location.href,
        page_path: location.pathname + location.search,
        user_id: userId || 'anonymous'
      });

      console.log(`Enhanced page view tracked: ${location.pathname + location.search}`, pageData);
    }
  }, [location, userId]);

  return null; // This component doesn't render anything
};

/**
 * Track a custom event in Google Analytics with enhanced eCommerce tracking
 * @param {string} eventName - Name of the event
 * @param {Object} eventParams - Additional parameters for the event
 */
export const trackEvent = (eventName, eventParams = {}) => {
  if (window.gtag) {
    // Add timestamp and session info to all events
    const enhancedParams = {
      ...eventParams,
      timestamp: new Date().toISOString(),
      session_id: getSessionId(),
      user_agent: navigator.userAgent,
      screen_resolution: `${window.screen.width}x${window.screen.height}`,
      viewport_size: `${window.innerWidth}x${window.innerHeight}`
    };

    window.gtag('event', eventName, enhancedParams);
    console.log(`Enhanced event tracked: ${eventName}`, enhancedParams);
  }
};

/**
 * Track product impression (when product is viewed in a list)
 * @param {Object} product - Product object
 * @param {string} listName - Name of the list (e.g., 'search_results', 'category_page', 'home_featured')
 * @param {number} position - Position in the list
 */
export const trackProductImpression = (product, listName, position) => {
  if (window.gtag) {
    window.gtag('event', 'view_item_list', {
      item_list_id: listName,
      item_list_name: listName,
      items: [{
        item_id: product.id,
        item_name: product.title || product.name,
        item_category: product.category,
        item_brand: product.brand || 'Unknown',
        price: product.price,
        quantity: 1,
        index: position
      }]
    });
    console.log(`Product impression tracked: ${product.title} in ${listName} at position ${position}`);
  }
};

/**
 * Track product detail view
 * @param {Object} product - Product object
 * @param {string} source - Source of the view (e.g., 'search', 'category', 'recommendation')
 */
export const trackProductView = (product, source = 'direct') => {
  if (window.gtag) {
    window.gtag('event', 'view_item', {
      currency: 'USD',
      value: product.price,
      items: [{
        item_id: product.id,
        item_name: product.title || product.name,
        item_category: product.category,
        item_brand: product.brand || 'Unknown',
        price: product.price,
        quantity: 1
      }],
      source: source,
      product_id: product.id
    });
    console.log(`Product view tracked: ${product.title} from ${source}`);
  }
};

/**
 * Track add to cart event
 * @param {Object} product - Product object
 * @param {number} quantity - Quantity added
 * @param {string} source - Source of the add to cart action
 */
export const trackAddToCart = (product, quantity = 1, source = 'product_page') => {
  if (window.gtag) {
    window.gtag('event', 'add_to_cart', {
      currency: 'USD',
      value: product.price * quantity,
      items: [{
        item_id: product.id,
        item_name: product.title || product.name,
        item_category: product.category,
        item_brand: product.brand || 'Unknown',
        price: product.price,
        quantity: quantity
      }],
      source: source,
      product_id: product.id
    });
    console.log(`Add to cart tracked: ${product.title} x${quantity} from ${source}`);
  }
};

/**
 * Track checkout start
 * @param {Array} items - Array of cart items
 * @param {number} totalValue - Total cart value
 */
export const trackCheckoutStart = (items, totalValue) => {
  if (window.gtag) {
    const formattedItems = items.map(item => ({
      item_id: item.product?.id || item.id,
      item_name: item.product?.title || item.product?.name || item.name,
      item_category: item.product?.category || 'Unknown',
      item_brand: item.product?.brand || 'Unknown',
      price: item.product?.price || item.price,
      quantity: item.quantity
    }));

    window.gtag('event', 'begin_checkout', {
      currency: 'USD',
      value: totalValue,
      items: formattedItems,
      checkout_step: 1
    });
    console.log(`Checkout start tracked with ${items.length} items, total: $${totalValue}`);
  }
};

/**
 * Track purchase completion
 * @param {Object} orderData - Order data object
 */
export const trackPurchase = (orderData) => {
  if (window.gtag) {
    const formattedItems = orderData.items.map(item => ({
      item_id: item.product?.id || item.id,
      item_name: item.product?.title || item.product?.name || item.name,
      item_category: item.product?.category || 'Unknown',
      item_brand: item.product?.brand || 'Unknown',
      price: item.product?.price || item.price,
      quantity: item.quantity
    }));

    window.gtag('event', 'purchase', {
      transaction_id: orderData.id,
      value: orderData.total_price,
      currency: 'USD',
      items: formattedItems,
      shipping: orderData.shipping_cost || 0,
      tax: orderData.tax_amount || 0
    });
    console.log(`Purchase tracked: Order ${orderData.id}, total: $${orderData.total_price}`);
  }
};

/**
 * Track search event
 * @param {string} searchTerm - The search term
 * @param {number} resultCount - Number of results returned
 */
export const trackSearch = (searchTerm, resultCount = 0) => {
  if (window.gtag) {
    window.gtag('event', 'search', {
      search_term: searchTerm,
      result_count: resultCount
    });
    console.log(`Search tracked: "${searchTerm}" with ${resultCount} results`);
  }
};

/**
 * Get or create session ID
 */
const getSessionId = () => {
  let sessionId = sessionStorage.getItem('ga_session_id');
  if (!sessionId) {
    sessionId = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    sessionStorage.setItem('ga_session_id', sessionId);
  }
  return sessionId;
};

export default GoogleAnalytics;
