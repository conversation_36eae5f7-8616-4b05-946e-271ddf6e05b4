import React, { useEffect, useState } from 'react';
import Layout from '../components/Layout.jsx';
import toast from 'react-hot-toast';
import { salesService, analyticsService } from '../services/api';

const StatCard = ({ label, value, sub }) => (
  <div className="p-6 rounded-lg bg-white shadow border border-gray-100">
    <div className="text-sm text-gray-500">{label}</div>
    <div className="mt-2 text-2xl font-semibold">{value}</div>
    {sub ? <div className="text-xs text-gray-500 mt-1">{sub}</div> : null}
  </div>
);

const MiniBarChart = ({ data = [] }) => {
  const values = data.map(d => Number(d.total_revenue || d.total || d.amount || 0));
  const max = Math.max(1, ...values);
  return (
    <div className="h-32 w-full flex items-end gap-1">
      {data.map((d, idx) => (
        <div key={idx} className="flex-1 bg-blue-500/20 hover:bg-blue-500/30 transition" title={`${(d.period || d.label || d.date)}: $${Number(values[idx]).toFixed(2)}`} style={{ height: `${(values[idx] / max) * 100}%` }} />
      ))}
    </div>
  );
};

const SalesDashboardPage = () => {
  const [loading, setLoading] = useState(true);
  const [summary, setSummary] = useState({ today: {}, this_week: {}, this_month: {} });
  const [revenue, setRevenue] = useState([]);
  const [topProducts, setTopProducts] = useState([]);

  useEffect(() => {
    const load = async () => {
      try {
        const [sumRes, revRes, gaRes] = await Promise.all([
          salesService.getDashboardSummary(),
          salesService.getRevenueAnalytics('monthly'),
          analyticsService.getGeneral({ range: 'month' })
        ]);
        setSummary(sumRes.data || {});
        setRevenue((revRes.data?.revenue_by_period || []).map(x => ({
          period: x.period,
          total_revenue: x.total_revenue
        })));
        setTopProducts((gaRes.data?.top_products?.most_purchased || []).slice(0, 5));
      } catch (e) {
        console.error(e);
        toast.error('Failed to load sales dashboard');
      } finally {
        setLoading(false);
      }
    };
    load();
  }, []);

  const fmt = (n) => `$${Number(n || 0).toFixed(2)}`;

  return (
    <Layout>
      <div className="container mx-auto py-10 px-4 space-y-8">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Sales Dashboard</h1>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <StatCard label="Today Revenue" value={fmt(summary?.today?.revenue)} sub={`${summary?.today?.orders || 0} orders`} />
          <StatCard label="This Week Revenue" value={fmt(summary?.this_week?.revenue)} sub={`${summary?.this_week?.orders || 0} orders`} />
          <StatCard label="This Month Revenue" value={fmt(summary?.this_month?.revenue)} sub={`${summary?.this_month?.orders || 0} orders`} />
        </div>

        {/* Revenue Trend */}
        <div className="bg-white rounded-lg shadow border border-gray-100">
          <div className="px-6 py-4 border-b flex items-center justify-between">
            <h2 className="text-lg font-semibold">Revenue Trend (Monthly)</h2>
          </div>
          <div className="p-6">
            {loading ? <div>Loading...</div> : revenue.length ? <MiniBarChart data={revenue} /> : <div>No data</div>}
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white rounded-lg shadow border border-gray-100">
          <div className="px-6 py-4 border-b">
            <h2 className="text-lg font-semibold">Top Products (Purchases)</h2>
          </div>
          <div className="p-6">
            {loading ? (
              <div>Loading...</div>
            ) : topProducts.length ? (
              <ul className="divide-y">
                {topProducts.map((p, idx) => (
                  <li key={idx} className="py-2 flex items-center justify-between">
                    <div className="text-gray-700">{p['product__title'] || 'Product'}{p['product__id'] ? ` (#${p['product__id']})` : ''}</div>
                    <div className="text-sm text-gray-500">{p['purchase_count'] || 0} purchases</div>
                  </li>
                ))}
              </ul>
            ) : (
              <div>No top product data</div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default SalesDashboardPage;

