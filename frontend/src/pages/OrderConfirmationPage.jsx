import React from 'react';
import { Link } from 'react-router-dom';
import Layout from '../components/Layout.jsx';

const OrderConfirmationPage = () => {
  return (
    <Layout>

      {/* Main Content */}
      <div className="container mx-auto py-12 px-4">
        <div className="bg-white rounded-lg shadow-md p-8 max-w-2xl mx-auto text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold mb-4">Order Confirmed!</h1>
          <p className="text-gray-600 mb-6">Thank you for your purchase. Your order has been received and is being processed.</p>
          <div className="bg-gray-50 p-4 rounded mb-6">
            <h2 className="font-semibold mb-2">Order #12345</h2>
            <p className="text-gray-600">Estimated delivery: 3-5 business days</p>
          </div>
          <Link to="/" className="bg-blue-500 text-white py-2 px-6 rounded-lg hover:bg-blue-600 inline-block">
            Continue Shopping
          </Link>
        </div>
      </div>
    </Layout>
  );
};

export default OrderConfirmationPage;
