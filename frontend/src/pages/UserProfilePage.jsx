import React from 'react';
import { Link } from 'react-router-dom';
import Layout from '../components/Layout.jsx';

const UserProfilePage = () => {
  return (
    <Layout>

      {/* Main Content */}
      <div className="container mx-auto py-12 px-4">
        <h1 className="text-3xl font-bold mb-8">My Profile</h1>
        <div className="bg-white rounded-lg shadow-md p-8">
          <h2 className="text-xl font-semibold mb-4">User Profile Page</h2>
          <p className="text-gray-600 mb-4">This is a placeholder for the user profile page.</p>
          <Link to="/" className="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 inline-block">
            Back to Home
          </Link>
        </div>
      </div>
    </Layout>
  );
};

export default UserProfilePage;
