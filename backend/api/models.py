"""
Models for the eCommerce platform.
This module defines all database models used in the application.
"""
import uuid
from django.db import models
from django.contrib.auth.models import AbstractU<PERSON>, UserManager as DjangoUserManager
from django.utils.text import slugify
from django.core.exceptions import ValidationError
from django.db.models import Q, F, Count, Sum, Avg, Prefetch


# Custom Managers for Query Optimization
class UserManager(DjangoUserManager):
    """Custom manager for User model with optimized queries"""

    def active_users(self):
        """Get all active users"""
        return self.filter(is_active=True)

    def with_roles(self):
        """Get users with their roles prefetched"""
        return self.prefetch_related('user_roles__role')

    def with_orders(self):
        """Get users with their orders prefetched"""
        return self.prefetch_related('orders')

    def customers(self):
        """Get users who have made orders"""
        return self.filter(orders__isnull=False).distinct()

    def by_email_domain(self, domain):
        """Get users by email domain"""
        return self.filter(email__iendswith=f'@{domain}')


class CategoryManager(models.Manager):
    """Custom manager for Category model with optimized queries"""

    def root_categories(self):
        """Get root categories (no parent)"""
        return self.filter(parent_category__isnull=True)

    def with_subcategories(self):
        """Get categories with subcategories prefetched"""
        return self.prefetch_related('subcategories')

    def with_products(self):
        """Get categories with products prefetched"""
        return self.prefetch_related('products')

    def with_product_count(self):
        """Get categories with product count"""
        return self.annotate(product_count=Count('products'))


class ProductManager(models.Manager):
    """Custom manager for Product model with optimized queries"""

    def active(self):
        """Get active products"""
        return self.filter(is_active=True)

    def in_stock(self):
        """Get products that are in stock"""
        return self.filter(stock_quantity__gt=0, is_active=True)

    def out_of_stock(self):
        """Get products that are out of stock"""
        return self.filter(stock_quantity=0, is_active=True)

    def low_stock(self, threshold=10):
        """Get products with low stock"""
        return self.filter(stock_quantity__lte=threshold, stock_quantity__gt=0, is_active=True)

    def with_category(self):
        """Get products with category data"""
        return self.select_related('category')

    def with_images(self):
        """Get products with all images prefetched"""
        return self.prefetch_related('additional_images')

    def with_reviews(self):
        """Get products with reviews prefetched"""
        return self.prefetch_related('reviews__user')

    def with_offers(self):
        """Get products with active offers"""
        return self.prefetch_related('offers').filter(offers__is_active=True)

    def by_category(self, category):
        """Get products by category"""
        return self.filter(category=category, is_active=True)

    def by_price_range(self, min_price=None, max_price=None):
        """Get products within price range"""
        queryset = self.filter(is_active=True)
        if min_price is not None:
            queryset = queryset.filter(price__gte=min_price)
        if max_price is not None:
            queryset = queryset.filter(price__lte=max_price)
        return queryset

    def with_average_rating(self):
        """Get products with average rating"""
        return self.annotate(avg_rating=Avg('reviews__rating'))

    def popular(self, limit=10):
        """Get popular products based on order count"""
        return self.annotate(
            order_count=Count('orderline')
        ).filter(is_active=True).order_by('-order_count')[:limit]


class OrderManager(models.Manager):
    """Custom manager for Order model with optimized queries"""

    def with_user(self):
        """Get orders with user data"""
        return self.select_related('user')

    def with_lines(self):
        """Get orders with order lines prefetched"""
        return self.prefetch_related('order_lines__product')

    def paid(self):
        """Get paid orders"""
        return self.filter(payment_status='paid')

    def pending(self):
        """Get pending orders"""
        return self.filter(payment_status='pending')

    def delivered(self):
        """Get delivered orders"""
        return self.filter(delivery_status='delivered')

    def by_user(self, user):
        """Get orders by user"""
        return self.filter(user=user)

    def recent(self, days=30):
        """Get recent orders"""
        from django.utils import timezone
        cutoff_date = timezone.now() - timezone.timedelta(days=days)
        return self.filter(created_at__gte=cutoff_date)

    def with_promocode(self):
        """Get orders that used promocodes"""
        return self.filter(applied_promocode__isnull=False).select_related('applied_promocode')


class CartManager(models.Manager):
    """Custom manager for Cart model with optimized queries"""

    def active(self):
        """Get active carts"""
        return self.filter(status='active')

    def with_items(self):
        """Get carts with items prefetched"""
        return self.prefetch_related('items__product')

    def by_user(self, user):
        """Get carts by user"""
        return self.filter(created_by=user)

    def abandoned(self, days=7):
        """Get abandoned carts"""
        from django.utils import timezone
        cutoff_date = timezone.now() - timezone.timedelta(days=days)
        return self.filter(
            status='active',
            updated_at__lt=cutoff_date
        )

class User(AbstractUser):
    """
    Extended User model with additional fields for eCommerce functionality.
    Inherits from Django's AbstractUser and adds custom fields.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    slug = models.SlugField(unique=True, blank=True, db_index=True)
    email = models.EmailField(unique=True, db_index=True)
    phone = models.CharField(max_length=20, blank=True, null=True, db_index=True)
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True)
    locale = models.CharField(max_length=10, default='en-US', db_index=True)
    bio = models.TextField(blank=True, null=True)
    company = models.CharField(max_length=255, blank=True, null=True, db_index=True)
    email_validated = models.BooleanField(default=False, db_index=True)
    phone_validated = models.BooleanField(default=False, db_index=True)
    reset_token = models.CharField(max_length=100, blank=True, null=True, db_index=True)
    last_login = models.DateTimeField(blank=True, null=True, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, db_index=True)

    # Add related_name to avoid clashes with auth.User
    groups = models.ManyToManyField(
        'auth.Group',
        verbose_name='groups',
        blank=True,
        help_text='The groups this user belongs to.',
        related_name='api_user_set',
        related_query_name='api_user',
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        verbose_name='user permissions',
        blank=True,
        help_text='Specific permissions for this user.',
        related_name='api_user_set',
        related_query_name='api_user',
    )

    # Custom manager
    objects = UserManager()

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.username)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.username

    class Meta:
        indexes = [
            models.Index(fields=['email', 'is_active']),
            models.Index(fields=['username', 'is_active']),
            models.Index(fields=['created_at', 'is_active']),
            models.Index(fields=['last_login', 'is_active']),
            models.Index(fields=['email_validated', 'phone_validated']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(email__isnull=False) & ~models.Q(email=''),
                name='user_email_not_empty'
            ),
            models.CheckConstraint(
                check=models.Q(username__isnull=False) & ~models.Q(username=''),
                name='user_username_not_empty'
            ),
        ]

class Role(models.Model):
    """
    Role model for user permissions.
    Defines different roles like 'admin', 'manager', 'customer', etc.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True, db_index=True)
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.name

    class Meta:
        indexes = [
            models.Index(fields=['name']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(name__isnull=False) & ~models.Q(name=''),
                name='role_name_not_empty'
            ),
        ]

class UserRole(models.Model):
    """
    Many-to-many relationship between Users and Roles.
    Allows assigning multiple roles to users with timestamps.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='user_roles', db_index=True)
    role = models.ForeignKey(Role, on_delete=models.CASCADE, db_index=True)
    assigned_at = models.DateTimeField(auto_now_add=True, db_index=True)

    class Meta:
        unique_together = ('user', 'role')
        indexes = [
            models.Index(fields=['user', 'role']),
            models.Index(fields=['assigned_at']),
            models.Index(fields=['user', 'assigned_at']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.role.name}"

class Credential(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='credentials')
    provider_id = models.CharField(max_length=255)
    provider_key = models.UUIDField(default=uuid.uuid4)
    password_hash = models.CharField(max_length=255, blank=True, null=True)
    password_salt = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username} - {self.provider_id}"

class SocialProfile(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='social_profiles')
    platform = models.CharField(max_length=100)
    platform_user = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'platform')

    def __str__(self):
        return f"{self.user.username} - {self.platform}"

class Category(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    parent_category = models.ForeignKey('self', on_delete=models.SET_NULL, blank=True, null=True, related_name='subcategories', db_index=True)
    slug = models.SlugField(unique=True, db_index=True)
    name = models.CharField(max_length=255, db_index=True)
    description = models.TextField(blank=True, null=True)
    tags = models.JSONField(default=list, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, db_index=True)

    # Custom manager
    objects = CategoryManager()

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = 'Categories'
        indexes = [
            models.Index(fields=['name', 'parent_category']),
            models.Index(fields=['slug']),
            models.Index(fields=['created_at']),
            models.Index(fields=['parent_category', 'created_at']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(name__isnull=False) & ~models.Q(name=''),
                name='category_name_not_empty'
            ),
            models.CheckConstraint(
                check=models.Q(slug__isnull=False) & ~models.Q(slug=''),
                name='category_slug_not_empty'
            ),
        ]

class Product(models.Model):
    """
    Product model representing items for sale in the eCommerce platform.
    Includes inventory tracking, pricing, and categorization.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='products', db_index=True)
    title = models.CharField(max_length=255, db_index=True)
    slug = models.SlugField(unique=True, blank=True, db_index=True)
    summary = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    picture = models.ImageField(upload_to='products/', blank=True, null=True)
    price = models.DecimalField(max_digits=10, decimal_places=2, db_index=True)
    discount_type = models.CharField(max_length=20, blank=True, null=True, db_index=True)  # percentage, fixed, etc.
    discount_value = models.DecimalField(max_digits=10, decimal_places=2, default=0, db_index=True)
    tags = models.JSONField(default=list, blank=True)
    stock_quantity = models.IntegerField(default=0, db_index=True)
    is_active = models.BooleanField(default=True, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, db_index=True)

    # Custom manager
    objects = ProductManager()

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)

    @property
    def all_images(self):
        """Return a list of all product images including the main picture"""
        images = []
        if self.picture:
            images.append(self.picture.url)

        # Add additional images
        additional_images = self.additional_images.all().order_by('order')
        for img in additional_images:
            if img.image:
                images.append(img.image.url)

        return images

    def update_inventory(self, quantity_change, change_type, user, reference_order=None, notes=None):
        """
        Update product inventory with proper tracking

        Args:
            quantity_change: Integer, positive for additions, negative for subtractions
            change_type: String, type of inventory change (see InventoryChange.CHANGE_TYPE_CHOICES)
            user: User object, the user making the change
            reference_order: Optional Order object, if change is related to an order
            notes: Optional string, additional notes about the change

        Returns:
            InventoryChange object if successful

        Raises:
            ValidationError if resulting inventory would be negative
        """
        previous_quantity = self.stock_quantity
        new_quantity = previous_quantity + quantity_change

        if new_quantity < 0:
            raise ValidationError("Cannot reduce inventory below zero.")

        # Update the product's stock quantity
        self.stock_quantity = new_quantity
        self.save(update_fields=['stock_quantity', 'updated_at'])

        # Create inventory change record
        # Use apps.get_model to avoid forward reference issues
        from django.apps import apps
        InventoryChange = apps.get_model('api', 'InventoryChange')
        inventory_change = InventoryChange.objects.create(
            product=self,
            quantity_change=quantity_change,
            change_type=change_type,
            previous_quantity=previous_quantity,
            new_quantity=new_quantity,
            changed_by=user,
            reference_order=reference_order,
            notes=notes
        )

        return inventory_change

    def add_inventory(self, quantity, user, notes=None):
        """Add inventory to the product"""
        if quantity <= 0:
            raise ValidationError("Quantity to add must be positive")
        return self.update_inventory(quantity, 'add', user, notes=notes)

    def subtract_inventory(self, quantity, user, notes=None):
        """Subtract inventory from the product"""
        if quantity <= 0:
            raise ValidationError("Quantity to subtract must be positive")
        return self.update_inventory(-quantity, 'subtract', user, notes=notes)

    def order_inventory(self, quantity, user, order, notes=None):
        """Subtract inventory due to an order"""
        if quantity <= 0:
            raise ValidationError("Order quantity must be positive")
        return self.update_inventory(-quantity, 'order', user, reference_order=order, notes=notes)

    def return_inventory(self, quantity, user, order, notes=None):
        """Add inventory due to an order return/cancellation"""
        if quantity <= 0:
            raise ValidationError("Return quantity must be positive")
        return self.update_inventory(quantity, 'return', user, reference_order=order, notes=notes)

    def add_batch_inventory(self, batch_data, user, notes=None):
        """Add inventory from a new batch"""
        from django.apps import apps
        InventoryBatch = apps.get_model('api', 'InventoryBatch')

        # Create the batch
        batch = InventoryBatch.objects.create(
            product=self,
            supplier=batch_data['supplier'],
            batch_number=batch_data['batch_number'],
            quantity_received=batch_data['quantity'],
            quantity_remaining=batch_data['quantity'],
            cost_per_unit=batch_data['cost_per_unit'],
            manufacturing_date=batch_data.get('manufacturing_date'),
            expiry_date=batch_data.get('expiry_date'),
            notes=batch_data.get('notes', '')
        )

        # Update product stock quantity
        previous_quantity = self.stock_quantity
        self.stock_quantity += batch_data['quantity']
        self.save(update_fields=['stock_quantity', 'updated_at'])

        # Create inventory change record
        from django.apps import apps
        InventoryChange = apps.get_model('api', 'InventoryChange')
        inventory_change = InventoryChange.objects.create(
            product=self,
            batch=batch,
            quantity_change=batch_data['quantity'],
            change_type='batch_add',
            previous_quantity=previous_quantity,
            new_quantity=self.stock_quantity,
            changed_by=user,
            notes=notes or f"New batch {batch.batch_number} from {batch.supplier.name}"
        )

        return batch, inventory_change

    def subtract_batch_inventory(self, quantity, user, order=None, notes=None):
        """Subtract inventory using FIFO (First In, First Out) method"""
        if quantity <= 0:
            raise ValidationError("Quantity to subtract must be positive")

        if self.stock_quantity < quantity:
            raise ValidationError(f"Insufficient inventory. Available: {self.stock_quantity}, Requested: {quantity}")

        from django.apps import apps
        InventoryBatch = apps.get_model('api', 'InventoryBatch')
        InventoryChange = apps.get_model('api', 'InventoryChange')

        # Get available batches ordered by FIFO (expiry date, then received date)
        available_batches = self.inventory_batches.filter(
            quantity_remaining__gt=0,
            is_active=True
        ).order_by('expiry_date', 'received_date')

        if not available_batches.exists():
            raise ValidationError("No available inventory batches found")

        remaining_to_subtract = quantity
        batch_changes = []

        for batch in available_batches:
            if remaining_to_subtract <= 0:
                break

            # Calculate how much to take from this batch
            quantity_from_batch = min(remaining_to_subtract, batch.quantity_remaining)

            # Update batch quantity
            previous_batch_quantity = batch.quantity_remaining
            batch.quantity_remaining -= quantity_from_batch
            batch.save(update_fields=['quantity_remaining'])

            # Create inventory change record for this batch
            inventory_change = InventoryChange.objects.create(
                product=self,
                batch=batch,
                quantity_change=-quantity_from_batch,
                change_type='batch_subtract',
                previous_quantity=previous_batch_quantity,
                new_quantity=batch.quantity_remaining,
                changed_by=user,
                reference_order=order,
                notes=notes or f"FIFO deduction from batch {batch.batch_number}"
            )

            batch_changes.append({
                'batch': batch,
                'quantity_used': quantity_from_batch,
                'inventory_change': inventory_change
            })

            remaining_to_subtract -= quantity_from_batch

        if remaining_to_subtract > 0:
            raise ValidationError(f"Could not fulfill complete quantity. Short by {remaining_to_subtract} units")

        # Update product stock quantity
        previous_quantity = self.stock_quantity
        self.stock_quantity -= quantity
        self.save(update_fields=['stock_quantity', 'updated_at'])

        # Create overall inventory change record
        overall_change = InventoryChange.objects.create(
            product=self,
            quantity_change=-quantity,
            change_type='order' if order else 'subtract',
            previous_quantity=previous_quantity,
            new_quantity=self.stock_quantity,
            changed_by=user,
            reference_order=order,
            notes=notes or f"FIFO inventory deduction across {len(batch_changes)} batches"
        )

        return batch_changes, overall_change

    @property
    def total_batch_inventory(self):
        """Calculate total inventory from all active batches"""
        return self.inventory_batches.filter(is_active=True).aggregate(
            total=models.Sum('quantity_remaining')
        )['total'] or 0

    @property
    def expired_batches(self):
        """Get all expired batches"""
        from django.utils import timezone
        return self.inventory_batches.filter(
            expiry_date__lt=timezone.now().date(),
            quantity_remaining__gt=0,
            is_active=True
        )

    @property
    def expiring_soon_batches(self, days=30):
        """Get batches expiring within specified days"""
        from django.utils import timezone
        cutoff_date = timezone.now().date() + timezone.timedelta(days=days)
        return self.inventory_batches.filter(
            expiry_date__lte=cutoff_date,
            expiry_date__gte=timezone.now().date(),
            quantity_remaining__gt=0,
            is_active=True
        )

    def __str__(self):
        return self.title

    class Meta:
        indexes = [
            models.Index(fields=['category', 'is_active']),
            models.Index(fields=['title', 'is_active']),
            models.Index(fields=['price', 'is_active']),
            models.Index(fields=['stock_quantity', 'is_active']),
            models.Index(fields=['created_at', 'is_active']),
            models.Index(fields=['category', 'price', 'is_active']),
            models.Index(fields=['category', 'stock_quantity', 'is_active']),
            models.Index(fields=['discount_type', 'discount_value', 'is_active']),
            models.Index(fields=['slug']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(title__isnull=False) & ~models.Q(title=''),
                name='product_title_not_empty'
            ),
            models.CheckConstraint(
                check=models.Q(price__gte=0),
                name='product_price_non_negative'
            ),
            models.CheckConstraint(
                check=models.Q(stock_quantity__gte=0),
                name='product_stock_non_negative'
            ),
            models.CheckConstraint(
                check=models.Q(discount_value__gte=0),
                name='product_discount_value_non_negative'
            ),
        ]


class ProductImage(models.Model):
    """Model to store additional product images (up to 4 additional images)"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='additional_images')
    image = models.ImageField(upload_to='products/', blank=True, null=True)
    order = models.PositiveSmallIntegerField(default=0)
    alt_text = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order']
        # Ensure we can only have 4 additional images per product (5 total with the main picture)
        constraints = [
            models.UniqueConstraint(fields=['product', 'order'], name='unique_product_image_order')
        ]

    def __str__(self):
        return f"{self.product.title} - Image {self.order + 1}"

class ProductModerationLog(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='moderation_logs')
    moderator = models.ForeignKey(User, on_delete=models.CASCADE)
    action = models.CharField(max_length=100)
    comments = models.TextField(blank=True, null=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.product.title} - {self.action} by {self.moderator.username}"

class Cart(models.Model):
    STATUS_CHOICES = (
        ('active', 'Active'),
        ('abandoned', 'Abandoned'),
        ('converted', 'Converted to Order'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='carts', db_index=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', db_index=True)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, db_index=True)

    # Custom manager
    objects = CartManager()

    def __str__(self):
        return f"Cart {self.id} - {self.created_by.username}"

    class Meta:
        indexes = [
            models.Index(fields=['created_by', 'status']),
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['created_by', 'created_at']),
        ]

class CartItem(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    cart = models.ForeignKey(Cart, on_delete=models.CASCADE, related_name='items', db_index=True)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, db_index=True)
    price = models.DecimalField(max_digits=10, decimal_places=2, db_index=True)
    quantity = models.IntegerField(default=1, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)

    def __str__(self):
        return f"{self.product.title} ({self.quantity}) in Cart {self.cart.id}"

    class Meta:
        indexes = [
            models.Index(fields=['cart', 'product']),
            models.Index(fields=['cart', 'created_at']),
            models.Index(fields=['product', 'created_at']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(quantity__gt=0),
                name='cartitem_quantity_positive'
            ),
            models.CheckConstraint(
                check=models.Q(price__gte=0),
                name='cartitem_price_non_negative'
            ),
            models.UniqueConstraint(
                fields=['cart', 'product'],
                name='unique_cart_product'
            ),
        ]

class Order(models.Model):
    PAYMENT_STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('paid', 'Paid'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
    )

    DELIVERY_STATUS_CHOICES = (
        ('processing', 'Processing'),
        ('shipped', 'Shipped'),
        ('delivered', 'Delivered'),
        ('cancelled', 'Cancelled'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='orders', db_index=True)

    # Pricing details
    subtotal = models.DecimalField(max_digits=10, decimal_places=2, help_text="Order subtotal before discounts", db_index=True)
    promocode_discount = models.DecimalField(max_digits=10, decimal_places=2, default=0, help_text="Discount amount from promocode", db_index=True)
    total_price = models.DecimalField(max_digits=10, decimal_places=2, help_text="Final order total after all discounts", db_index=True)

    # Promocode relationship
    applied_promocode = models.ForeignKey('Promocode', on_delete=models.SET_NULL, null=True, blank=True, related_name='orders', help_text="Promocode applied to this order", db_index=True)

    # Status fields
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending', db_index=True)
    delivery_status = models.CharField(max_length=20, choices=DELIVERY_STATUS_CHOICES, default='processing', db_index=True)

    # Stripe integration
    stripe_payment_intent_id = models.CharField(max_length=255, blank=True, null=True, db_index=True)
    stripe_payment_method_id = models.CharField(max_length=255, blank=True, null=True, db_index=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)

    # Custom manager
    objects = OrderManager()

    def clean(self):
        """Validate order data"""
        if self.subtotal < 0:
            raise ValidationError("Subtotal cannot be negative")

        if self.promocode_discount < 0:
            raise ValidationError("Promocode discount cannot be negative")

        if self.total_price < 0:
            raise ValidationError("Total price cannot be negative")

        # Validate that total_price = subtotal - promocode_discount
        expected_total = self.subtotal - self.promocode_discount
        if abs(self.total_price - expected_total) > 0.01:  # Allow for small rounding differences
            raise ValidationError("Total price must equal subtotal minus promocode discount")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def apply_promocode(self, promocode_code, user):
        """Apply a promocode to this order"""
        try:
            promocode = Promocode.objects.get(code=promocode_code.upper(), is_active=True)
        except Promocode.DoesNotExist:
            raise ValidationError("Invalid promocode")

        # Validate promocode
        if not promocode.is_valid():
            raise ValidationError("Promocode is not valid or has expired")

        if not promocode.can_be_used_by_user(user):
            raise ValidationError("You cannot use this promocode")

        # Check minimum order amount
        if self.subtotal < promocode.minimum_order_amount:
            raise ValidationError(f"Minimum order amount of ${promocode.minimum_order_amount} required")

        # Check if promocode applies to products in this order
        applicable_products = []
        for order_line in self.order_lines.all():
            if promocode.is_applicable_to_product(order_line.product):
                applicable_products.append(order_line)

        if not applicable_products:
            raise ValidationError("This promocode does not apply to any products in your order")

        # Calculate discount amount
        applicable_amount = sum(line.price * line.quantity for line in applicable_products)
        discount_amount = promocode.calculate_discount(applicable_amount)

        # Apply the promocode
        self.applied_promocode = promocode
        self.promocode_discount = discount_amount
        self.total_price = self.subtotal - self.promocode_discount
        self.save()

        return discount_amount

    def remove_promocode(self):
        """Remove applied promocode from this order"""
        self.applied_promocode = None
        self.promocode_discount = 0
        self.total_price = self.subtotal
        self.save()

    def __str__(self):
        return f"Order {self.id} - {self.user.username}"

    class Meta:
        indexes = [
            models.Index(fields=['user', 'payment_status']),
            models.Index(fields=['user', 'delivery_status']),
            models.Index(fields=['payment_status', 'delivery_status']),
            models.Index(fields=['created_at', 'payment_status']),
            models.Index(fields=['created_at', 'delivery_status']),
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['total_price', 'payment_status']),
            models.Index(fields=['applied_promocode', 'created_at']),
            models.Index(fields=['stripe_payment_intent_id']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(subtotal__gte=0),
                name='order_subtotal_non_negative'
            ),
            models.CheckConstraint(
                check=models.Q(promocode_discount__gte=0),
                name='order_promocode_discount_non_negative'
            ),
            models.CheckConstraint(
                check=models.Q(total_price__gte=0),
                name='order_total_price_non_negative'
            ),
        ]

class OrderLine(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='order_lines', db_index=True)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, db_index=True)
    price = models.DecimalField(max_digits=10, decimal_places=2, db_index=True)
    quantity = models.IntegerField(default=1, db_index=True)

    def __str__(self):
        return f"{self.product.title} ({self.quantity}) in Order {self.order.id}"

    class Meta:
        indexes = [
            models.Index(fields=['order', 'product']),
            models.Index(fields=['product', 'order']),
            models.Index(fields=['order', 'price']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(quantity__gt=0),
                name='orderline_quantity_positive'
            ),
            models.CheckConstraint(
                check=models.Q(price__gte=0),
                name='orderline_price_non_negative'
            ),
        ]

class Review(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reviews')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='reviews')
    rating = models.DecimalField(max_digits=3, decimal_places=1)
    comment = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'product')

    def __str__(self):
        return f"{self.product.title} - {self.rating} by {self.user.username}"

class PersonalizedRecommendation(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='recommendations')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    recommendation_score = models.FloatField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'product')

    def __str__(self):
        return f"{self.product.title} for {self.user.username} - Score: {self.recommendation_score}"

class Offer(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='offers')
    name = models.CharField(max_length=255)
    offer_type = models.CharField(max_length=20)  # e.g., "percentage", "flat", "bundle"
    discount_value = models.DecimalField(max_digits=10, decimal_places=2)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    is_festival = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.name} - {self.product.title}"

class UserBehavior(models.Model):
    ACTION_CHOICES = (
        ('search', 'Search'),
        ('view', 'View'),
        ('click', 'Click'),
        ('add_to_cart', 'Add to Cart'),
        ('purchase', 'Purchase'),
        ('page_view', 'Page View'),
        ('login', 'Login'),
        ('logout', 'Logout'),
        ('register', 'Register'),
        ('wishlist_add', 'Add to Wishlist'),
        ('review', 'Write Review'),
        ('share', 'Share Product'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='behaviors')
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='user_behaviors', null=True, blank=True)
    metadata = models.JSONField(default=dict, blank=True)
    action_time = models.DateTimeField(auto_now_add=True)
    session_id = models.CharField(max_length=100, blank=True, null=True)
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)
    referrer = models.URLField(blank=True, null=True)
    page = models.CharField(max_length=255, blank=True, null=True)

    def __str__(self):
        product_title = self.product.title if self.product else "N/A"
        return f"{self.user.username} - {self.action} - {product_title}"


class StripeCustomer(models.Model):
    """Model to store Stripe customer information"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='stripe_customer')
    stripe_customer_id = models.CharField(max_length=255, unique=True, db_index=True)
    email = models.EmailField(db_index=True)
    name = models.CharField(max_length=255, blank=True, null=True)
    phone = models.CharField(max_length=20, blank=True, null=True)
    address = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['stripe_customer_id']),
            models.Index(fields=['email', 'created_at']),
            models.Index(fields=['user', 'created_at']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(email__isnull=False) & ~models.Q(email=''),
                name='stripe_customer_email_not_empty'
            ),
        ]

    def __str__(self):
        return f"Stripe Customer {self.stripe_customer_id} for {self.user.username}"


class StripePaymentMethod(models.Model):
    """Model to store Stripe payment method information"""
    PAYMENT_METHOD_TYPES = (
        ('card', 'Card'),
        ('bank_account', 'Bank Account'),
        ('apple_pay', 'Apple Pay'),
        ('google_pay', 'Google Pay'),
        ('paypal', 'PayPal'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    customer = models.ForeignKey(StripeCustomer, on_delete=models.CASCADE, related_name='payment_methods')
    stripe_payment_method_id = models.CharField(max_length=255, unique=True, db_index=True)
    type = models.CharField(max_length=20, choices=PAYMENT_METHOD_TYPES, db_index=True)
    card_brand = models.CharField(max_length=20, blank=True, null=True)
    card_last4 = models.CharField(max_length=4, blank=True, null=True)
    card_exp_month = models.IntegerField(blank=True, null=True)
    card_exp_year = models.IntegerField(blank=True, null=True)
    is_default = models.BooleanField(default=False, db_index=True)
    is_active = models.BooleanField(default=True, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['customer', 'is_default']),
            models.Index(fields=['customer', 'is_active']),
            models.Index(fields=['type', 'is_active']),
            models.Index(fields=['stripe_payment_method_id']),
        ]
        constraints = [
            # Note: MySQL doesn't support conditional unique constraints
            # This will be enforced at the application level
        ]

    def __str__(self):
        if self.type == 'card' and self.card_last4:
            return f"{self.card_brand} ending in {self.card_last4}"
        return f"{self.type} payment method"


class StripePayment(models.Model):
    """Enhanced model to store Stripe payment information"""
    PAYMENT_STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('succeeded', 'Succeeded'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
        ('partially_refunded', 'Partially Refunded'),
        ('canceled', 'Canceled'),
        ('disputed', 'Disputed'),
    )

    PAYMENT_TYPE_CHOICES = (
        ('payment', 'Payment'),
        ('refund', 'Refund'),
        ('partial_refund', 'Partial Refund'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='stripe_payments')
    customer = models.ForeignKey(StripeCustomer, on_delete=models.SET_NULL, null=True, blank=True, related_name='payments')
    payment_intent_id = models.CharField(max_length=255, db_index=True)
    payment_method = models.ForeignKey(StripePaymentMethod, on_delete=models.SET_NULL, null=True, blank=True)
    stripe_payment_method_id = models.CharField(max_length=255, blank=True, null=True, db_index=True)
    charge_id = models.CharField(max_length=255, blank=True, null=True, db_index=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2, db_index=True)
    amount_refunded = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    currency = models.CharField(max_length=3, default='USD', db_index=True)
    status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending', db_index=True)
    payment_type = models.CharField(max_length=20, choices=PAYMENT_TYPE_CHOICES, default='payment', db_index=True)
    failure_code = models.CharField(max_length=50, blank=True, null=True)
    failure_message = models.TextField(blank=True, null=True)
    receipt_email = models.EmailField(blank=True, null=True)
    receipt_url = models.URLField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['payment_intent_id']),
            models.Index(fields=['order', 'status']),
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['amount', 'currency']),
            models.Index(fields=['payment_type', 'status']),
            models.Index(fields=['created_at', 'status']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(amount__gte=0),
                name='stripe_payment_amount_non_negative'
            ),
            models.CheckConstraint(
                check=models.Q(amount_refunded__gte=0),
                name='stripe_payment_amount_refunded_non_negative'
            ),
            models.CheckConstraint(
                check=models.Q(amount_refunded__lte=models.F('amount')),
                name='stripe_payment_refund_not_exceed_amount'
            ),
        ]

    def __str__(self):
        return f"Payment {self.payment_intent_id} for Order {self.order.id} - {self.status}"

    @property
    def is_refundable(self):
        """Check if payment can be refunded"""
        return self.status == 'succeeded' and self.amount_refunded < self.amount

    @property
    def refundable_amount(self):
        """Get the amount that can still be refunded"""
        return self.amount - self.amount_refunded


class StripeRefund(models.Model):
    """Model to store Stripe refund information"""
    REFUND_STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('succeeded', 'Succeeded'),
        ('failed', 'Failed'),
        ('canceled', 'Canceled'),
    )

    REFUND_REASON_CHOICES = (
        ('duplicate', 'Duplicate'),
        ('fraudulent', 'Fraudulent'),
        ('requested_by_customer', 'Requested by Customer'),
        ('expired_uncaptured_charge', 'Expired Uncaptured Charge'),
        ('other', 'Other'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    payment = models.ForeignKey(StripePayment, on_delete=models.CASCADE, related_name='refunds')
    stripe_refund_id = models.CharField(max_length=255, unique=True, db_index=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2, db_index=True)
    currency = models.CharField(max_length=3, default='USD')
    status = models.CharField(max_length=20, choices=REFUND_STATUS_CHOICES, default='pending', db_index=True)
    reason = models.CharField(max_length=30, choices=REFUND_REASON_CHOICES, blank=True, null=True)
    receipt_number = models.CharField(max_length=255, blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_refunds')
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['stripe_refund_id']),
            models.Index(fields=['payment', 'status']),
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['amount', 'currency']),
            models.Index(fields=['created_by', 'created_at']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(amount__gt=0),
                name='stripe_refund_amount_positive'
            ),
        ]

    def __str__(self):
        return f"Refund {self.stripe_refund_id} - ${self.amount}"


class StripeWebhookEvent(models.Model):
    """Model to store and track Stripe webhook events"""
    EVENT_TYPES = (
        ('payment_intent.succeeded', 'Payment Intent Succeeded'),
        ('payment_intent.payment_failed', 'Payment Intent Failed'),
        ('payment_intent.canceled', 'Payment Intent Canceled'),
        ('charge.succeeded', 'Charge Succeeded'),
        ('charge.failed', 'Charge Failed'),
        ('charge.refunded', 'Charge Refunded'),
        ('charge.dispute.created', 'Dispute Created'),
        ('customer.created', 'Customer Created'),
        ('customer.updated', 'Customer Updated'),
        ('payment_method.attached', 'Payment Method Attached'),
        ('invoice.payment_succeeded', 'Invoice Payment Succeeded'),
        ('invoice.payment_failed', 'Invoice Payment Failed'),
    )

    PROCESSING_STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('processed', 'Processed'),
        ('failed', 'Failed'),
        ('ignored', 'Ignored'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    stripe_event_id = models.CharField(max_length=255, unique=True, db_index=True)
    event_type = models.CharField(max_length=50, choices=EVENT_TYPES, db_index=True)
    processing_status = models.CharField(max_length=20, choices=PROCESSING_STATUS_CHOICES, default='pending', db_index=True)
    api_version = models.CharField(max_length=20, blank=True, null=True)
    livemode = models.BooleanField(default=False, db_index=True)
    data = models.JSONField()
    processed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True, null=True)
    retry_count = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['stripe_event_id']),
            models.Index(fields=['event_type', 'processing_status']),
            models.Index(fields=['processing_status', 'created_at']),
            models.Index(fields=['livemode', 'event_type']),
            models.Index(fields=['created_at', 'processing_status']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(retry_count__gte=0),
                name='webhook_event_retry_count_non_negative'
            ),
        ]

    def __str__(self):
        return f"Webhook {self.event_type} - {self.stripe_event_id}"


class PaymentAnalytics(models.Model):
    """Model to store payment analytics and metrics"""
    PERIOD_CHOICES = (
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('yearly', 'Yearly'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    period_type = models.CharField(max_length=10, choices=PERIOD_CHOICES, db_index=True)
    period_start = models.DateTimeField(db_index=True)
    period_end = models.DateTimeField(db_index=True)
    total_payments = models.IntegerField(default=0)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    successful_payments = models.IntegerField(default=0)
    failed_payments = models.IntegerField(default=0)
    refunded_payments = models.IntegerField(default=0)
    total_refunded_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    average_payment_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    unique_customers = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['period_type', 'period_start']),
            models.Index(fields=['period_start', 'period_end']),
            models.Index(fields=['total_amount', 'period_type']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['period_type', 'period_start'],
                name='unique_analytics_period'
            ),
            models.CheckConstraint(
                check=models.Q(period_end__gt=models.F('period_start')),
                name='analytics_period_end_after_start'
            ),
        ]

    def __str__(self):
        return f"{self.period_type} analytics for {self.period_start.date()}"


class Supplier(models.Model):
    """Model to track suppliers for inventory batches"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    contact_person = models.CharField(max_length=255, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    phone = models.CharField(max_length=20, blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'


class InventoryBatch(models.Model):
    """Model to track inventory at batch level with expiry dates and supplier info"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='inventory_batches')
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, related_name='inventory_batches')
    batch_number = models.CharField(max_length=100)
    quantity_received = models.IntegerField()
    quantity_remaining = models.IntegerField()
    cost_per_unit = models.DecimalField(max_digits=10, decimal_places=2)
    manufacturing_date = models.DateField(blank=True, null=True)
    expiry_date = models.DateField(blank=True, null=True)
    received_date = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    def clean(self):
        """Validate batch data"""
        if self.quantity_remaining < 0:
            raise ValidationError("Remaining quantity cannot be negative.")

        if self.quantity_remaining > self.quantity_received:
            raise ValidationError("Remaining quantity cannot exceed received quantity.")

        if self.expiry_date and self.manufacturing_date:
            if self.expiry_date <= self.manufacturing_date:
                raise ValidationError("Expiry date must be after manufacturing date.")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    @property
    def is_expired(self):
        """Check if the batch is expired"""
        if self.expiry_date:
            from django.utils import timezone
            return timezone.now().date() > self.expiry_date
        return False

    @property
    def days_until_expiry(self):
        """Calculate days until expiry"""
        if self.expiry_date:
            from django.utils import timezone
            delta = self.expiry_date - timezone.now().date()
            return delta.days
        return None

    @property
    def is_low_stock(self, threshold=10):
        """Check if batch has low stock"""
        return self.quantity_remaining <= threshold

    def __str__(self):
        return f"{self.product.title} - Batch {self.batch_number} ({self.quantity_remaining}/{self.quantity_received})"

    class Meta:
        ordering = ['expiry_date', 'received_date']  # FIFO ordering
        unique_together = ['product', 'batch_number', 'supplier']


class InventoryChange(models.Model):
    """Model to track inventory changes for products"""
    CHANGE_TYPE_CHOICES = (
        ('add', 'Addition'),
        ('subtract', 'Subtraction'),
        ('order', 'Order Placement'),
        ('return', 'Order Return'),
        ('adjustment', 'Manual Adjustment'),
        ('initial', 'Initial Stock'),
        ('batch_add', 'Batch Addition'),
        ('batch_subtract', 'Batch Subtraction'),
        ('expiry', 'Expired Stock Removal'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='inventory_changes')
    batch = models.ForeignKey(InventoryBatch, on_delete=models.CASCADE, null=True, blank=True, related_name='inventory_changes')
    quantity_change = models.IntegerField()  # Positive for additions, negative for subtractions
    change_type = models.CharField(max_length=20, choices=CHANGE_TYPE_CHOICES)
    previous_quantity = models.IntegerField()
    new_quantity = models.IntegerField()
    changed_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='inventory_changes')
    reference_order = models.ForeignKey(Order, on_delete=models.SET_NULL, null=True, blank=True, related_name='inventory_changes')
    notes = models.TextField(blank=True, null=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    def clean(self):
        """Validate that new_quantity is not negative"""
        if self.new_quantity < 0:
            raise ValidationError("Inventory cannot be negative.")

        # Ensure quantity_change matches the difference between new and previous
        if self.new_quantity - self.previous_quantity != self.quantity_change:
            raise ValidationError("Quantity change must equal the difference between new and previous quantities.")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        batch_info = f" (Batch: {self.batch.batch_number})" if self.batch else ""
        return f"{self.product.title} - {self.change_type} - {self.quantity_change} units{batch_info}"


# Notification System Models
class InventoryAlert(models.Model):
    """Model to track inventory alerts and notifications"""

    ALERT_TYPES = [
        ('low_stock', 'Low Stock'),
        ('out_of_stock', 'Out of Stock'),
        ('expiring_soon', 'Expiring Soon'),
        ('expired', 'Expired'),
        ('reorder_suggestion', 'Reorder Suggestion'),
    ]

    PRIORITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPES)
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='medium')
    title = models.CharField(max_length=255)
    message = models.TextField()

    # Related objects
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='inventory_alerts', null=True, blank=True)
    batch = models.ForeignKey(InventoryBatch, on_delete=models.CASCADE, related_name='alerts', null=True, blank=True)

    # Alert status
    is_active = models.BooleanField(default=True)
    is_acknowledged = models.BooleanField(default=False)
    acknowledged_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='acknowledged_alerts')
    acknowledged_at = models.DateTimeField(null=True, blank=True)

    # Metadata
    threshold_value = models.IntegerField(null=True, blank=True, help_text="Threshold value that triggered this alert")
    expiry_date = models.DateField(null=True, blank=True, help_text="Expiry date for expiry-related alerts")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.get_alert_type_display()} - {self.title}"

    def acknowledge(self, user):
        """Mark alert as acknowledged"""
        from django.utils import timezone
        self.is_acknowledged = True
        self.acknowledged_by = user
        self.acknowledged_at = timezone.now()
        self.save(update_fields=['is_acknowledged', 'acknowledged_by', 'acknowledged_at', 'updated_at'])

    def dismiss(self):
        """Dismiss/deactivate the alert"""
        self.is_active = False
        self.save(update_fields=['is_active', 'updated_at'])

    @classmethod
    def create_low_stock_alert(cls, product, current_stock, threshold):
        """Create a low stock alert for a product"""
        # Check if similar alert already exists and is active
        existing_alert = cls.objects.filter(
            product=product,
            alert_type='low_stock',
            is_active=True,
            is_acknowledged=False
        ).first()

        if existing_alert:
            # Update existing alert
            existing_alert.message = f"Product '{product.title}' is running low on stock. Current stock: {current_stock} units (threshold: {threshold})"
            existing_alert.threshold_value = threshold
            existing_alert.save(update_fields=['message', 'threshold_value', 'updated_at'])
            return existing_alert

        # Create new alert
        priority = 'high' if current_stock == 0 else 'medium'
        alert_type = 'out_of_stock' if current_stock == 0 else 'low_stock'

        return cls.objects.create(
            alert_type=alert_type,
            priority=priority,
            title=f"{'Out of Stock' if current_stock == 0 else 'Low Stock'}: {product.title}",
            message=f"Product '{product.title}' {'is out of stock' if current_stock == 0 else 'is running low on stock'}. Current stock: {current_stock} units" + (f" (threshold: {threshold})" if current_stock > 0 else ""),
            product=product,
            threshold_value=threshold
        )

    @classmethod
    def create_expiry_alert(cls, batch, days_until_expiry):
        """Create an expiry alert for a batch"""
        # Check if similar alert already exists and is active
        existing_alert = cls.objects.filter(
            batch=batch,
            alert_type__in=['expiring_soon', 'expired'],
            is_active=True,
            is_acknowledged=False
        ).first()

        if existing_alert:
            # Update existing alert based on current status
            if days_until_expiry < 0:
                existing_alert.alert_type = 'expired'
                existing_alert.priority = 'critical'
                existing_alert.title = f"Expired: {batch.product.title} (Batch {batch.batch_number})"
                existing_alert.message = f"Batch {batch.batch_number} of '{batch.product.title}' has expired on {batch.expiry_date}. Remaining stock: {batch.quantity_remaining} units"
            else:
                existing_alert.alert_type = 'expiring_soon'
                existing_alert.priority = 'high' if days_until_expiry <= 7 else 'medium'
                existing_alert.title = f"Expiring Soon: {batch.product.title} (Batch {batch.batch_number})"
                existing_alert.message = f"Batch {batch.batch_number} of '{batch.product.title}' will expire in {days_until_expiry} days ({batch.expiry_date}). Remaining stock: {batch.quantity_remaining} units"

            existing_alert.expiry_date = batch.expiry_date
            existing_alert.save(update_fields=['alert_type', 'priority', 'title', 'message', 'expiry_date', 'updated_at'])
            return existing_alert

        # Create new alert
        if days_until_expiry < 0:
            alert_type = 'expired'
            priority = 'critical'
            title = f"Expired: {batch.product.title} (Batch {batch.batch_number})"
            message = f"Batch {batch.batch_number} of '{batch.product.title}' has expired on {batch.expiry_date}. Remaining stock: {batch.quantity_remaining} units"
        else:
            alert_type = 'expiring_soon'
            priority = 'high' if days_until_expiry <= 7 else 'medium'
            title = f"Expiring Soon: {batch.product.title} (Batch {batch.batch_number})"
            message = f"Batch {batch.batch_number} of '{batch.product.title}' will expire in {days_until_expiry} days ({batch.expiry_date}). Remaining stock: {batch.quantity_remaining} units"

        return cls.objects.create(
            alert_type=alert_type,
            priority=priority,
            title=title,
            message=message,
            product=batch.product,
            batch=batch,
            expiry_date=batch.expiry_date
        )

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['alert_type', 'is_active']),
            models.Index(fields=['priority', 'is_active']),
            models.Index(fields=['product', 'is_active']),
            models.Index(fields=['is_acknowledged', 'is_active']),
        ]


class Promocode(models.Model):
    """
    Promocode model for managing discount codes.
    Supports different types of discounts and applicability rules.
    """
    DISCOUNT_TYPE_CHOICES = (
        ('percentage', 'Percentage'),
        ('fixed', 'Fixed Amount'),
    )

    APPLICABLE_TO_CHOICES = (
        ('all', 'All Products'),
        ('category', 'Specific Categories'),
        ('product', 'Specific Products'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    code = models.CharField(max_length=50, unique=True, help_text="Unique promocode")
    description = models.TextField(blank=True, null=True, help_text="Description of the promocode")

    # Discount details
    discount_type = models.CharField(max_length=20, choices=DISCOUNT_TYPE_CHOICES, default='percentage')
    discount_value = models.DecimalField(max_digits=10, decimal_places=2, help_text="Discount amount or percentage")

    # Validity period
    valid_from = models.DateTimeField(help_text="Start date and time for promocode validity")
    valid_until = models.DateTimeField(help_text="End date and time for promocode validity")

    # Usage limits
    usage_limit = models.IntegerField(default=1, help_text="Maximum number of times this code can be used (0 for unlimited)")
    times_used = models.IntegerField(default=0, help_text="Number of times this code has been used")

    # Applicability
    applicable_to = models.CharField(max_length=20, choices=APPLICABLE_TO_CHOICES, default='all')
    applicable_categories = models.ManyToManyField(Category, blank=True, related_name='promocodes', help_text="Categories this promocode applies to")
    applicable_products = models.ManyToManyField(Product, blank=True, related_name='promocodes', help_text="Products this promocode applies to")

    # Minimum order requirements
    minimum_order_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, help_text="Minimum order amount to apply this promocode")

    # Status
    is_active = models.BooleanField(default=True, help_text="Whether this promocode is active")

    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_promocodes', help_text="User who created this promocode")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['valid_from', 'valid_until']),
            models.Index(fields=['is_active']),
        ]

    def clean(self):
        """Validate promocode data"""
        from django.utils import timezone

        # Validate discount value
        if self.discount_type == 'percentage' and (self.discount_value < 0 or self.discount_value > 100):
            raise ValidationError("Percentage discount must be between 0 and 100")
        elif self.discount_type == 'fixed' and self.discount_value < 0:
            raise ValidationError("Fixed discount amount cannot be negative")

        # Validate date range
        if self.valid_from >= self.valid_until:
            raise ValidationError("Valid from date must be before valid until date")

        # Validate usage limit
        if self.usage_limit < 0:
            raise ValidationError("Usage limit cannot be negative")

        # Validate minimum order amount
        if self.minimum_order_amount < 0:
            raise ValidationError("Minimum order amount cannot be negative")

    def save(self, *args, **kwargs):
        self.code = self.code.upper()  # Store codes in uppercase
        self.clean()
        super().save(*args, **kwargs)

    def is_valid(self):
        """Check if promocode is currently valid"""
        from django.utils import timezone
        now = timezone.now()

        return (
            self.is_active and
            self.valid_from <= now <= self.valid_until and
            (self.usage_limit == 0 or self.times_used < self.usage_limit)
        )

    def can_be_used_by_user(self, user):
        """Check if a specific user can use this promocode"""
        if not self.is_valid():
            return False

        # Check if user has already used this code (if single use)
        if self.usage_limit == 1:
            return not PromoCodeUsage.objects.filter(promocode=self, user=user).exists()

        return True

    def is_applicable_to_product(self, product):
        """Check if promocode applies to a specific product"""
        if self.applicable_to == 'all':
            return True
        elif self.applicable_to == 'product':
            return self.applicable_products.filter(id=product.id).exists()
        elif self.applicable_to == 'category':
            return self.applicable_categories.filter(id=product.category.id).exists()

        return False

    def calculate_discount(self, order_amount):
        """Calculate discount amount for given order amount"""
        if self.discount_type == 'percentage':
            return (self.discount_value / 100) * order_amount
        else:  # fixed
            return min(self.discount_value, order_amount)

    def __str__(self):
        return f"{self.code} - {self.discount_value}{'%' if self.discount_type == 'percentage' else '$'}"


class PromoCodeUsage(models.Model):
    """
    Track promocode usage history for detailed redemption tracking.
    Records when, by whom, and on which order a promocode was used.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    promocode = models.ForeignKey(Promocode, on_delete=models.CASCADE, related_name='usage_history')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='promocode_usage')
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='promocode_usage')

    # Discount details at time of use
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, help_text="Actual discount amount applied")
    order_amount_before_discount = models.DecimalField(max_digits=10, decimal_places=2, help_text="Order total before promocode discount")
    order_amount_after_discount = models.DecimalField(max_digits=10, decimal_places=2, help_text="Order total after promocode discount")

    # Metadata
    used_at = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(blank=True, null=True, help_text="IP address from which promocode was used")
    user_agent = models.TextField(blank=True, null=True, help_text="User agent string")

    class Meta:
        ordering = ['-used_at']
        unique_together = ('promocode', 'order')  # Prevent multiple uses of same code on same order
        indexes = [
            models.Index(fields=['promocode', 'user']),
            models.Index(fields=['used_at']),
            models.Index(fields=['order']),
        ]

    def clean(self):
        """Validate promocode usage data"""
        if self.discount_amount < 0:
            raise ValidationError("Discount amount cannot be negative")

        if self.order_amount_before_discount < self.order_amount_after_discount:
            raise ValidationError("Order amount after discount cannot be greater than before discount")

        if self.discount_amount != (self.order_amount_before_discount - self.order_amount_after_discount):
            raise ValidationError("Discount amount must equal the difference between before and after amounts")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

        # Update promocode usage count
        self.promocode.times_used = self.promocode.usage_history.count()
        self.promocode.save(update_fields=['times_used'])

    def __str__(self):
        return f"{self.promocode.code} used by {self.user.username} on Order {self.order.id}"


class SalesReport(models.Model):
    """Model to track sales data for reporting and analytics"""
    REPORT_TYPE_CHOICES = (
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('yearly', 'Yearly'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    report_date = models.DateField()
    report_type = models.CharField(max_length=10, choices=REPORT_TYPE_CHOICES)

    # Sales metrics
    total_orders = models.IntegerField(default=0)
    total_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_units_sold = models.IntegerField(default=0)
    total_discount_given = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    # Customer metrics
    new_customers = models.IntegerField(default=0)
    returning_customers = models.IntegerField(default=0)

    # Product metrics
    top_selling_product = models.ForeignKey(Product, on_delete=models.SET_NULL, null=True, blank=True, related_name='top_sales_reports')
    top_selling_product_units = models.IntegerField(default=0)

    # Calculated fields
    average_order_value = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def calculate_metrics(self):
        """Calculate and update sales metrics for the report period"""
        from django.db.models import Sum, Count, Avg
        from django.utils import timezone

        # Determine date range based on report type and date
        if self.report_type == 'daily':
            start_date = self.report_date
            end_date = self.report_date
        elif self.report_type == 'weekly':
            # Week starts on Monday
            start_date = self.report_date - timezone.timedelta(days=self.report_date.weekday())
            end_date = start_date + timezone.timedelta(days=6)
        elif self.report_type == 'monthly':
            start_date = self.report_date.replace(day=1)
            if self.report_date.month == 12:
                end_date = self.report_date.replace(year=self.report_date.year + 1, month=1, day=1) - timezone.timedelta(days=1)
            else:
                end_date = self.report_date.replace(month=self.report_date.month + 1, day=1) - timezone.timedelta(days=1)
        elif self.report_type == 'yearly':
            start_date = self.report_date.replace(month=1, day=1)
            end_date = self.report_date.replace(month=12, day=31)

        # Filter orders for the period
        orders = Order.objects.filter(
            created_at__date__gte=start_date,
            created_at__date__lte=end_date,
            payment_status='paid'
        )

        # Calculate basic metrics
        order_stats = orders.aggregate(
            total_orders=Count('id'),
            total_revenue=Sum('total_price'),
            total_discount=Sum('promocode_discount'),
            avg_order_value=Avg('total_price')
        )

        self.total_orders = order_stats['total_orders'] or 0
        self.total_revenue = order_stats['total_revenue'] or 0
        self.total_discount_given = order_stats['total_discount'] or 0
        self.average_order_value = order_stats['avg_order_value'] or 0

        # Calculate total units sold
        order_lines = OrderLine.objects.filter(order__in=orders)
        self.total_units_sold = order_lines.aggregate(total_units=Sum('quantity'))['total_units'] or 0

        # Calculate customer metrics
        customer_orders = orders.values('user').distinct()
        self.new_customers = 0  # Would need additional logic to determine new vs returning
        self.returning_customers = customer_orders.count()

        # Find top selling product
        top_product = order_lines.values('product').annotate(
            total_sold=Sum('quantity')
        ).order_by('-total_sold').first()

        if top_product:
            self.top_selling_product_id = top_product['product']
            self.top_selling_product_units = top_product['total_sold']

        self.save()

    def __str__(self):
        return f"{self.report_type.title()} Sales Report - {self.report_date}"

    class Meta:
        ordering = ['-report_date']
        unique_together = ['report_date', 'report_type']
