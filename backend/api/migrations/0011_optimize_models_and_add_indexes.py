# Generated by Django 4.2.7 on 2025-10-09 19:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0010_add_inventory_alerts'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='category',
            options={'verbose_name_plural': 'Categories'},
        ),
        migrations.AlterModelOptions(
            name='user',
            options={},
        ),
        migrations.AlterModelManagers(
            name='user',
            managers=[
            ],
        ),
        migrations.AlterField(
            model_name='cart',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name='cart',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('abandoned', 'Abandoned'), ('converted', 'Converted to Order')], db_index=True, default='active', max_length=20),
        ),
        migrations.AlterField(
            model_name='cart',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, db_index=True),
        ),
        migrations.AlterField(
            model_name='cartitem',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name='cartitem',
            name='price',
            field=models.DecimalField(db_index=True, decimal_places=2, max_digits=10),
        ),
        migrations.AlterField(
            model_name='cartitem',
            name='quantity',
            field=models.IntegerField(db_index=True, default=1),
        ),
        migrations.AlterField(
            model_name='category',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name='category',
            name='name',
            field=models.CharField(db_index=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='category',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, db_index=True),
        ),
        migrations.AlterField(
            model_name='order',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name='order',
            name='delivery_status',
            field=models.CharField(choices=[('processing', 'Processing'), ('shipped', 'Shipped'), ('delivered', 'Delivered'), ('cancelled', 'Cancelled')], db_index=True, default='processing', max_length=20),
        ),
        migrations.AlterField(
            model_name='order',
            name='payment_status',
            field=models.CharField(choices=[('pending', 'Pending'), ('paid', 'Paid'), ('failed', 'Failed'), ('refunded', 'Refunded')], db_index=True, default='pending', max_length=20),
        ),
        migrations.AlterField(
            model_name='order',
            name='promocode_discount',
            field=models.DecimalField(db_index=True, decimal_places=2, default=0, help_text='Discount amount from promocode', max_digits=10),
        ),
        migrations.AlterField(
            model_name='order',
            name='stripe_payment_intent_id',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='order',
            name='stripe_payment_method_id',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='order',
            name='subtotal',
            field=models.DecimalField(db_index=True, decimal_places=2, help_text='Order subtotal before discounts', max_digits=10),
        ),
        migrations.AlterField(
            model_name='order',
            name='total_price',
            field=models.DecimalField(db_index=True, decimal_places=2, help_text='Final order total after all discounts', max_digits=10),
        ),
        migrations.AlterField(
            model_name='orderline',
            name='price',
            field=models.DecimalField(db_index=True, decimal_places=2, max_digits=10),
        ),
        migrations.AlterField(
            model_name='orderline',
            name='quantity',
            field=models.IntegerField(db_index=True, default=1),
        ),
        migrations.AlterField(
            model_name='product',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='discount_type',
            field=models.CharField(blank=True, db_index=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='discount_value',
            field=models.DecimalField(db_index=True, decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AlterField(
            model_name='product',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='price',
            field=models.DecimalField(db_index=True, decimal_places=2, max_digits=10),
        ),
        migrations.AlterField(
            model_name='product',
            name='stock_quantity',
            field=models.IntegerField(db_index=True, default=0),
        ),
        migrations.AlterField(
            model_name='product',
            name='title',
            field=models.CharField(db_index=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='product',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, db_index=True),
        ),
        migrations.AlterField(
            model_name='role',
            name='name',
            field=models.CharField(db_index=True, max_length=100, unique=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='company',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='email',
            field=models.EmailField(db_index=True, max_length=254, unique=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='email_validated',
            field=models.BooleanField(db_index=True, default=False),
        ),
        migrations.AlterField(
            model_name='user',
            name='last_login',
            field=models.DateTimeField(blank=True, db_index=True, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='locale',
            field=models.CharField(db_index=True, default='en-US', max_length=10),
        ),
        migrations.AlterField(
            model_name='user',
            name='phone',
            field=models.CharField(blank=True, db_index=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='phone_validated',
            field=models.BooleanField(db_index=True, default=False),
        ),
        migrations.AlterField(
            model_name='user',
            name='reset_token',
            field=models.CharField(blank=True, db_index=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, db_index=True),
        ),
        migrations.AlterField(
            model_name='userrole',
            name='assigned_at',
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AddIndex(
            model_name='cart',
            index=models.Index(fields=['created_by', 'status'], name='api_cart_created_784b2f_idx'),
        ),
        migrations.AddIndex(
            model_name='cart',
            index=models.Index(fields=['status', 'created_at'], name='api_cart_status_49a21c_idx'),
        ),
        migrations.AddIndex(
            model_name='cart',
            index=models.Index(fields=['created_by', 'created_at'], name='api_cart_created_894731_idx'),
        ),
        migrations.AddIndex(
            model_name='cartitem',
            index=models.Index(fields=['cart', 'product'], name='api_cartite_cart_id_1d4d0d_idx'),
        ),
        migrations.AddIndex(
            model_name='cartitem',
            index=models.Index(fields=['cart', 'created_at'], name='api_cartite_cart_id_07238c_idx'),
        ),
        migrations.AddIndex(
            model_name='cartitem',
            index=models.Index(fields=['product', 'created_at'], name='api_cartite_product_1a6dd7_idx'),
        ),
        migrations.AddIndex(
            model_name='category',
            index=models.Index(fields=['name', 'parent_category'], name='api_categor_name_d63e96_idx'),
        ),
        migrations.AddIndex(
            model_name='category',
            index=models.Index(fields=['slug'], name='api_categor_slug_794f78_idx'),
        ),
        migrations.AddIndex(
            model_name='category',
            index=models.Index(fields=['created_at'], name='api_categor_created_c87ac2_idx'),
        ),
        migrations.AddIndex(
            model_name='category',
            index=models.Index(fields=['parent_category', 'created_at'], name='api_categor_parent__fd4ab6_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['user', 'payment_status'], name='api_order_user_id_2b3a95_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['user', 'delivery_status'], name='api_order_user_id_4d8e1e_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['payment_status', 'delivery_status'], name='api_order_payment_9fc3c6_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['created_at', 'payment_status'], name='api_order_created_cf0b65_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['created_at', 'delivery_status'], name='api_order_created_4d711d_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['user', 'created_at'], name='api_order_user_id_d6ac48_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['total_price', 'payment_status'], name='api_order_total_p_5a6139_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['applied_promocode', 'created_at'], name='api_order_applied_cf2e16_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['stripe_payment_intent_id'], name='api_order_stripe__671da6_idx'),
        ),
        migrations.AddIndex(
            model_name='orderline',
            index=models.Index(fields=['order', 'product'], name='api_orderli_order_i_c8c419_idx'),
        ),
        migrations.AddIndex(
            model_name='orderline',
            index=models.Index(fields=['product', 'order'], name='api_orderli_product_eb1600_idx'),
        ),
        migrations.AddIndex(
            model_name='orderline',
            index=models.Index(fields=['order', 'price'], name='api_orderli_order_i_793577_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['category', 'is_active'], name='api_product_categor_058e7c_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['title', 'is_active'], name='api_product_title_002a86_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['price', 'is_active'], name='api_product_price_f3bb48_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['stock_quantity', 'is_active'], name='api_product_stock_q_b8796c_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['created_at', 'is_active'], name='api_product_created_08ae97_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['category', 'price', 'is_active'], name='api_product_categor_d638dd_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['category', 'stock_quantity', 'is_active'], name='api_product_categor_ea04bc_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['discount_type', 'discount_value', 'is_active'], name='api_product_discoun_27d630_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['slug'], name='api_product_slug_e70af7_idx'),
        ),
        migrations.AddIndex(
            model_name='role',
            index=models.Index(fields=['name'], name='api_role_name_7b16d4_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['email', 'is_active'], name='api_user_email_0918d8_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['username', 'is_active'], name='api_user_usernam_b8c15e_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['created_at', 'is_active'], name='api_user_created_7e600b_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['last_login', 'is_active'], name='api_user_last_lo_43da8b_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['email_validated', 'phone_validated'], name='api_user_email_v_49201d_idx'),
        ),
        migrations.AddIndex(
            model_name='userrole',
            index=models.Index(fields=['user', 'role'], name='api_userrol_user_id_6017f9_idx'),
        ),
        migrations.AddIndex(
            model_name='userrole',
            index=models.Index(fields=['assigned_at'], name='api_userrol_assigne_ae5485_idx'),
        ),
        migrations.AddIndex(
            model_name='userrole',
            index=models.Index(fields=['user', 'assigned_at'], name='api_userrol_user_id_33de81_idx'),
        ),
        migrations.AddConstraint(
            model_name='cartitem',
            constraint=models.CheckConstraint(check=models.Q(('quantity__gt', 0)), name='cartitem_quantity_positive'),
        ),
        migrations.AddConstraint(
            model_name='cartitem',
            constraint=models.CheckConstraint(check=models.Q(('price__gte', 0)), name='cartitem_price_non_negative'),
        ),
        migrations.AddConstraint(
            model_name='cartitem',
            constraint=models.UniqueConstraint(fields=('cart', 'product'), name='unique_cart_product'),
        ),
        migrations.AddConstraint(
            model_name='category',
            constraint=models.CheckConstraint(check=models.Q(('name__isnull', False), models.Q(('name', ''), _negated=True)), name='category_name_not_empty'),
        ),
        migrations.AddConstraint(
            model_name='category',
            constraint=models.CheckConstraint(check=models.Q(('slug__isnull', False), models.Q(('slug', ''), _negated=True)), name='category_slug_not_empty'),
        ),
        migrations.AddConstraint(
            model_name='order',
            constraint=models.CheckConstraint(check=models.Q(('subtotal__gte', 0)), name='order_subtotal_non_negative'),
        ),
        migrations.AddConstraint(
            model_name='order',
            constraint=models.CheckConstraint(check=models.Q(('promocode_discount__gte', 0)), name='order_promocode_discount_non_negative'),
        ),
        migrations.AddConstraint(
            model_name='order',
            constraint=models.CheckConstraint(check=models.Q(('total_price__gte', 0)), name='order_total_price_non_negative'),
        ),
        migrations.AddConstraint(
            model_name='orderline',
            constraint=models.CheckConstraint(check=models.Q(('quantity__gt', 0)), name='orderline_quantity_positive'),
        ),
        migrations.AddConstraint(
            model_name='orderline',
            constraint=models.CheckConstraint(check=models.Q(('price__gte', 0)), name='orderline_price_non_negative'),
        ),
        migrations.AddConstraint(
            model_name='product',
            constraint=models.CheckConstraint(check=models.Q(('title__isnull', False), models.Q(('title', ''), _negated=True)), name='product_title_not_empty'),
        ),
        migrations.AddConstraint(
            model_name='product',
            constraint=models.CheckConstraint(check=models.Q(('price__gte', 0)), name='product_price_non_negative'),
        ),
        migrations.AddConstraint(
            model_name='product',
            constraint=models.CheckConstraint(check=models.Q(('stock_quantity__gte', 0)), name='product_stock_non_negative'),
        ),
        migrations.AddConstraint(
            model_name='product',
            constraint=models.CheckConstraint(check=models.Q(('discount_value__gte', 0)), name='product_discount_value_non_negative'),
        ),
        migrations.AddConstraint(
            model_name='role',
            constraint=models.CheckConstraint(check=models.Q(('name__isnull', False), models.Q(('name', ''), _negated=True)), name='role_name_not_empty'),
        ),
        migrations.AddConstraint(
            model_name='user',
            constraint=models.CheckConstraint(check=models.Q(('email__isnull', False), models.Q(('email', ''), _negated=True)), name='user_email_not_empty'),
        ),
        migrations.AddConstraint(
            model_name='user',
            constraint=models.CheckConstraint(check=models.Q(('username__isnull', False), models.Q(('username', ''), _negated=True)), name='user_username_not_empty'),
        ),
    ]
