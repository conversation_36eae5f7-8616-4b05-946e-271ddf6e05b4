"""
Comprehensive Stripe utility functions for the eCommerce platform.

This module contains helper functions for interacting with the Stripe API,
including creating payment intents, handling webhooks, processing payments,
customer management, payment methods, refunds, and analytics.
"""
import stripe
import logging
from django.conf import settings
from django.utils import timezone
from django.db import models
from decimal import Decimal
from typing import Dict, List, Optional, Union
from .models import StripeCustomer, StripePayment, StripePaymentMethod, StripeRefund, StripeWebhookEvent

# Configure Stripe API key
stripe.api_key = settings.STRIPE_SECRET_KEY
stripe.api_version = settings.STRIPE_API_VERSION

# Configure logging
logger = logging.getLogger('api.stripe')

def create_payment_intent(order, customer_email=None):
    """
    Create a Stripe PaymentIntent for an order.
    
    Args:
        order: Order model instance
        customer_email: Optional customer email for receipt
        
    Returns:
        dict: Payment intent details including client_secret
    """
    # Convert decimal to integer cents for Stripe
    amount_cents = int(order.total_price * 100)
    
    # Create metadata for the payment intent
    metadata = {
        'order_id': str(order.id),
        'user_id': str(order.user.id),
    }
    
    # Create the payment intent
    intent = stripe.PaymentIntent.create(
        amount=amount_cents,
        currency='usd',
        metadata=metadata,
        receipt_email=customer_email,
        payment_method_types=['card'],
    )
    
    # Update the order with the payment intent ID
    order.stripe_payment_intent_id = intent.id
    order.save(update_fields=['stripe_payment_intent_id'])
    
    return {
        'client_secret': intent.client_secret,
        'payment_intent_id': intent.id,
    }

def retrieve_payment_intent(payment_intent_id):
    """
    Retrieve a payment intent from Stripe.
    
    Args:
        payment_intent_id: Stripe payment intent ID
        
    Returns:
        Stripe PaymentIntent object
    """
    return stripe.PaymentIntent.retrieve(payment_intent_id)

def confirm_payment_intent(payment_intent_id, payment_method_id=None):
    """
    Confirm a payment intent.
    
    Args:
        payment_intent_id: Stripe payment intent ID
        payment_method_id: Optional payment method ID
        
    Returns:
        Stripe PaymentIntent object
    """
    if payment_method_id:
        return stripe.PaymentIntent.confirm(
            payment_intent_id,
            payment_method=payment_method_id
        )
    return stripe.PaymentIntent.confirm(payment_intent_id)

def cancel_payment_intent(payment_intent_id):
    """
    Cancel a payment intent.
    
    Args:
        payment_intent_id: Stripe payment intent ID
        
    Returns:
        Stripe PaymentIntent object
    """
    return stripe.PaymentIntent.cancel(payment_intent_id)

def construct_event(payload, sig_header):
    """
    Construct a Stripe event from webhook payload.

    Args:
        payload: Request body from Stripe webhook
        sig_header: Stripe signature header

    Returns:
        Stripe Event object
    """
    try:
        return stripe.Webhook.construct_event(
            payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
        )
    except ValueError:
        # Invalid payload
        logger.error("Invalid webhook payload received")
        return None
    except stripe.error.SignatureVerificationError:
        # Invalid signature
        logger.error("Invalid webhook signature")
        return None


# Customer Management Functions
def create_or_get_stripe_customer(user, email=None, name=None, phone=None, address=None):
    """
    Create or retrieve a Stripe customer for a user.

    Args:
        user: Django User instance
        email: Customer email (optional, defaults to user.email)
        name: Customer name (optional)
        phone: Customer phone (optional)
        address: Customer address dict (optional)

    Returns:
        StripeCustomer instance
    """
    try:
        # Try to get existing customer
        stripe_customer = StripeCustomer.objects.get(user=user)
        logger.info(f"Retrieved existing Stripe customer {stripe_customer.stripe_customer_id} for user {user.id}")
        return stripe_customer
    except StripeCustomer.DoesNotExist:
        # Create new customer
        customer_data = {
            'email': email or user.email,
            'metadata': {'user_id': str(user.id)}
        }

        if name:
            customer_data['name'] = name
        if phone:
            customer_data['phone'] = phone
        if address:
            customer_data['address'] = address

        try:
            stripe_customer_obj = stripe.Customer.create(**customer_data)

            # Create local customer record
            stripe_customer = StripeCustomer.objects.create(
                user=user,
                stripe_customer_id=stripe_customer_obj.id,
                email=customer_data['email'],
                name=name,
                phone=phone,
                address=address or {},
                metadata={'created_via': 'api'}
            )

            logger.info(f"Created new Stripe customer {stripe_customer.stripe_customer_id} for user {user.id}")
            return stripe_customer

        except stripe.error.StripeError as e:
            logger.error(f"Failed to create Stripe customer for user {user.id}: {str(e)}")
            raise


def update_stripe_customer(stripe_customer, email=None, name=None, phone=None, address=None):
    """
    Update a Stripe customer.

    Args:
        stripe_customer: StripeCustomer instance
        email: New email (optional)
        name: New name (optional)
        phone: New phone (optional)
        address: New address dict (optional)

    Returns:
        Updated StripeCustomer instance
    """
    try:
        update_data = {}

        if email:
            update_data['email'] = email
            stripe_customer.email = email
        if name:
            update_data['name'] = name
            stripe_customer.name = name
        if phone:
            update_data['phone'] = phone
            stripe_customer.phone = phone
        if address:
            update_data['address'] = address
            stripe_customer.address = address

        if update_data:
            # Update in Stripe
            stripe.Customer.modify(stripe_customer.stripe_customer_id, **update_data)

            # Update local record
            stripe_customer.save()

            logger.info(f"Updated Stripe customer {stripe_customer.stripe_customer_id}")

        return stripe_customer

    except stripe.error.StripeError as e:
        logger.error(f"Failed to update Stripe customer {stripe_customer.stripe_customer_id}: {str(e)}")
        raise


def delete_stripe_customer(stripe_customer):
    """
    Delete a Stripe customer.

    Args:
        stripe_customer: StripeCustomer instance

    Returns:
        bool: Success status
    """
    try:
        # Delete from Stripe
        stripe.Customer.delete(stripe_customer.stripe_customer_id)

        # Delete local record
        stripe_customer.delete()

        logger.info(f"Deleted Stripe customer {stripe_customer.stripe_customer_id}")
        return True

    except stripe.error.StripeError as e:
        logger.error(f"Failed to delete Stripe customer {stripe_customer.stripe_customer_id}: {str(e)}")
        return False


# Payment Method Management Functions
def attach_payment_method(stripe_customer, payment_method_id, set_as_default=False):
    """
    Attach a payment method to a customer.

    Args:
        stripe_customer: StripeCustomer instance
        payment_method_id: Stripe payment method ID
        set_as_default: Whether to set as default payment method

    Returns:
        StripePaymentMethod instance
    """
    try:
        # Attach payment method to customer in Stripe
        payment_method = stripe.PaymentMethod.attach(
            payment_method_id,
            customer=stripe_customer.stripe_customer_id
        )

        # Get payment method details
        pm_details = stripe.PaymentMethod.retrieve(payment_method_id)

        # Extract card details if it's a card
        card_brand = None
        card_last4 = None
        card_exp_month = None
        card_exp_year = None

        if pm_details.type == 'card' and pm_details.card:
            card_brand = pm_details.card.brand
            card_last4 = pm_details.card.last4
            card_exp_month = pm_details.card.exp_month
            card_exp_year = pm_details.card.exp_year

        # If setting as default, unset other default payment methods
        if set_as_default:
            StripePaymentMethod.objects.filter(
                customer=stripe_customer,
                is_default=True
            ).update(is_default=False)

        # Create local payment method record
        stripe_payment_method = StripePaymentMethod.objects.create(
            customer=stripe_customer,
            stripe_payment_method_id=payment_method_id,
            type=pm_details.type,
            card_brand=card_brand,
            card_last4=card_last4,
            card_exp_month=card_exp_month,
            card_exp_year=card_exp_year,
            is_default=set_as_default,
            metadata={'attached_via': 'api'}
        )

        logger.info(f"Attached payment method {payment_method_id} to customer {stripe_customer.stripe_customer_id}")
        return stripe_payment_method

    except stripe.error.StripeError as e:
        logger.error(f"Failed to attach payment method {payment_method_id}: {str(e)}")
        raise


def detach_payment_method(stripe_payment_method):
    """
    Detach a payment method from a customer.

    Args:
        stripe_payment_method: StripePaymentMethod instance

    Returns:
        bool: Success status
    """
    try:
        # Detach from Stripe
        stripe.PaymentMethod.detach(stripe_payment_method.stripe_payment_method_id)

        # Delete local record
        stripe_payment_method.delete()

        logger.info(f"Detached payment method {stripe_payment_method.stripe_payment_method_id}")
        return True

    except stripe.error.StripeError as e:
        logger.error(f"Failed to detach payment method {stripe_payment_method.stripe_payment_method_id}: {str(e)}")
        return False


def set_default_payment_method(stripe_customer, stripe_payment_method):
    """
    Set a payment method as the default for a customer.

    Args:
        stripe_customer: StripeCustomer instance
        stripe_payment_method: StripePaymentMethod instance

    Returns:
        bool: Success status
    """
    try:
        # Update customer's default payment method in Stripe
        stripe.Customer.modify(
            stripe_customer.stripe_customer_id,
            invoice_settings={'default_payment_method': stripe_payment_method.stripe_payment_method_id}
        )

        # Update local records
        StripePaymentMethod.objects.filter(
            customer=stripe_customer,
            is_default=True
        ).update(is_default=False)

        stripe_payment_method.is_default = True
        stripe_payment_method.save()

        logger.info(f"Set payment method {stripe_payment_method.stripe_payment_method_id} as default")
        return True

    except stripe.error.StripeError as e:
        logger.error(f"Failed to set default payment method: {str(e)}")
        return False


def get_customer_payment_methods(stripe_customer, type=None):
    """
    Get all payment methods for a customer.

    Args:
        stripe_customer: StripeCustomer instance
        type: Payment method type filter (optional)

    Returns:
        QuerySet of StripePaymentMethod instances
    """
    queryset = StripePaymentMethod.objects.filter(
        customer=stripe_customer,
        is_active=True
    )

    if type:
        queryset = queryset.filter(type=type)

    return queryset.order_by('-is_default', '-created_at')


# Refund Management Functions
def create_refund(stripe_payment, amount=None, reason=None, created_by=None):
    """
    Create a refund for a payment.

    Args:
        stripe_payment: StripePayment instance
        amount: Refund amount (optional, defaults to full amount)
        reason: Refund reason (optional)
        created_by: User who created the refund (optional)

    Returns:
        StripeRefund instance
    """
    try:
        # Validate refund amount
        if amount is None:
            amount = stripe_payment.refundable_amount
        elif amount > stripe_payment.refundable_amount:
            raise ValueError(f"Refund amount ${amount} exceeds refundable amount ${stripe_payment.refundable_amount}")

        # Create refund in Stripe
        refund_data = {
            'charge': stripe_payment.charge_id,
            'amount': int(amount * 100),  # Convert to cents
        }

        if reason:
            refund_data['reason'] = reason

        stripe_refund = stripe.Refund.create(**refund_data)

        # Create local refund record
        refund = StripeRefund.objects.create(
            payment=stripe_payment,
            stripe_refund_id=stripe_refund.id,
            amount=amount,
            currency=stripe_payment.currency,
            status=stripe_refund.status,
            reason=reason,
            receipt_number=stripe_refund.receipt_number,
            created_by=created_by,
            metadata={'created_via': 'api'}
        )

        # Update payment refunded amount
        stripe_payment.amount_refunded += amount
        if stripe_payment.amount_refunded >= stripe_payment.amount:
            stripe_payment.status = 'refunded'
        else:
            stripe_payment.status = 'partially_refunded'
        stripe_payment.save()

        logger.info(f"Created refund {stripe_refund.id} for payment {stripe_payment.payment_intent_id}")
        return refund

    except stripe.error.StripeError as e:
        logger.error(f"Failed to create refund for payment {stripe_payment.payment_intent_id}: {str(e)}")
        raise
    except ValueError as e:
        logger.error(f"Invalid refund amount: {str(e)}")
        raise


def get_payment_refunds(stripe_payment):
    """
    Get all refunds for a payment.

    Args:
        stripe_payment: StripePayment instance

    Returns:
        QuerySet of StripeRefund instances
    """
    return StripeRefund.objects.filter(payment=stripe_payment).order_by('-created_at')


# Enhanced Payment Intent Functions
def create_payment_intent_with_customer(order, stripe_customer, payment_method_id=None, save_payment_method=False):
    """
    Create a Stripe PaymentIntent with customer and optional payment method.

    Args:
        order: Order model instance
        stripe_customer: StripeCustomer instance
        payment_method_id: Optional payment method ID
        save_payment_method: Whether to save the payment method for future use

    Returns:
        dict: Payment intent details including client_secret
    """
    try:
        # Convert decimal to integer cents for Stripe
        amount_cents = int(order.total_price * 100)

        # Create metadata for the payment intent
        metadata = {
            'order_id': str(order.id),
            'user_id': str(order.user.id),
            'customer_id': stripe_customer.stripe_customer_id,
        }

        # Create payment intent data
        intent_data = {
            'amount': amount_cents,
            'currency': 'usd',
            'customer': stripe_customer.stripe_customer_id,
            'metadata': metadata,
            'receipt_email': stripe_customer.email,
            'payment_method_types': ['card'],
        }

        if payment_method_id:
            intent_data['payment_method'] = payment_method_id
            intent_data['confirmation_method'] = 'manual'
            intent_data['confirm'] = True

        if save_payment_method:
            intent_data['setup_future_usage'] = 'on_session'

        # Create the payment intent
        intent = stripe.PaymentIntent.create(**intent_data)

        # Update the order with the payment intent ID
        order.stripe_payment_intent_id = intent.id
        if payment_method_id:
            order.stripe_payment_method_id = payment_method_id
        order.save(update_fields=['stripe_payment_intent_id', 'stripe_payment_method_id'])

        logger.info(f"Created payment intent {intent.id} for order {order.id}")

        return {
            'client_secret': intent.client_secret,
            'payment_intent_id': intent.id,
            'status': intent.status,
            'requires_action': intent.status == 'requires_action',
        }

    except stripe.error.StripeError as e:
        logger.error(f"Failed to create payment intent for order {order.id}: {str(e)}")
        raise


# Webhook Processing Functions
def process_webhook_event(event_data):
    """
    Process a Stripe webhook event and store it in the database.

    Args:
        event_data: Stripe event object

    Returns:
        StripeWebhookEvent instance
    """
    try:
        # Check if event already exists
        webhook_event, created = StripeWebhookEvent.objects.get_or_create(
            stripe_event_id=event_data.id,
            defaults={
                'event_type': event_data.type,
                'api_version': event_data.api_version,
                'livemode': event_data.livemode,
                'data': event_data.data,
                'processing_status': 'pending'
            }
        )

        if created:
            logger.info(f"Created webhook event record for {event_data.type} - {event_data.id}")
        else:
            logger.info(f"Webhook event {event_data.id} already exists")

        return webhook_event

    except Exception as e:
        logger.error(f"Failed to process webhook event {event_data.id}: {str(e)}")
        raise


def mark_webhook_processed(webhook_event, success=True, error_message=None):
    """
    Mark a webhook event as processed.

    Args:
        webhook_event: StripeWebhookEvent instance
        success: Whether processing was successful
        error_message: Error message if processing failed
    """
    webhook_event.processing_status = 'processed' if success else 'failed'
    webhook_event.processed_at = timezone.now()
    if error_message:
        webhook_event.error_message = error_message
    webhook_event.save()


# Payment Analytics Functions
def get_payment_statistics(start_date=None, end_date=None):
    """
    Get payment statistics for a date range.

    Args:
        start_date: Start date (optional)
        end_date: End date (optional)

    Returns:
        dict: Payment statistics
    """
    from django.db.models import Sum, Count, Avg

    queryset = StripePayment.objects.all()

    if start_date:
        queryset = queryset.filter(created_at__gte=start_date)
    if end_date:
        queryset = queryset.filter(created_at__lte=end_date)

    stats = queryset.aggregate(
        total_payments=Count('id'),
        total_amount=Sum('amount'),
        average_amount=Avg('amount'),
        successful_payments=Count('id', filter=models.Q(status='succeeded')),
        failed_payments=Count('id', filter=models.Q(status='failed')),
        refunded_payments=Count('id', filter=models.Q(status__in=['refunded', 'partially_refunded'])),
        total_refunded=Sum('amount_refunded')
    )

    # Calculate success rate
    if stats['total_payments'] > 0:
        stats['success_rate'] = (stats['successful_payments'] / stats['total_payments']) * 100
    else:
        stats['success_rate'] = 0

    return stats


def get_revenue_by_period(period='monthly', start_date=None, end_date=None):
    """
    Get revenue statistics grouped by time period.

    Args:
        period: Time period ('daily', 'weekly', 'monthly', 'yearly')
        start_date: Start date (optional)
        end_date: End date (optional)

    Returns:
        QuerySet: Revenue data grouped by period
    """
    from django.db.models import Sum, Count
    from django.db.models.functions import TruncDay, TruncWeek, TruncMonth, TruncYear

    queryset = StripePayment.objects.filter(status='succeeded')

    if start_date:
        queryset = queryset.filter(created_at__gte=start_date)
    if end_date:
        queryset = queryset.filter(created_at__lte=end_date)

    # Choose truncation function based on period
    trunc_functions = {
        'daily': TruncDay,
        'weekly': TruncWeek,
        'monthly': TruncMonth,
        'yearly': TruncYear,
    }

    trunc_func = trunc_functions.get(period, TruncMonth)

    return queryset.annotate(
        period=trunc_func('created_at')
    ).values('period').annotate(
        total_revenue=Sum('amount'),
        payment_count=Count('id')
    ).order_by('period')


# Utility Functions
def format_stripe_amount(amount_cents):
    """
    Convert Stripe amount (in cents) to decimal.

    Args:
        amount_cents: Amount in cents

    Returns:
        Decimal: Amount in dollars
    """
    return Decimal(amount_cents) / 100


def format_amount_for_stripe(amount):
    """
    Convert decimal amount to Stripe format (cents).

    Args:
        amount: Decimal amount

    Returns:
        int: Amount in cents
    """
    return int(amount * 100)


def validate_webhook_signature(payload, signature, secret=None):
    """
    Validate Stripe webhook signature.

    Args:
        payload: Request payload
        signature: Stripe signature header
        secret: Webhook secret (optional, uses settings default)

    Returns:
        bool: Whether signature is valid
    """
    webhook_secret = secret or settings.STRIPE_WEBHOOK_SECRET

    try:
        stripe.Webhook.construct_event(payload, signature, webhook_secret)
        return True
    except (ValueError, stripe.error.SignatureVerificationError):
        return False
