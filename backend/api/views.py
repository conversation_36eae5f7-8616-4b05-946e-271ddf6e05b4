"""
API views for the eCommerce platform.

This module contains all the view classes that handle API requests,
including authentication, product management, cart operations,
order processing, and inventory management.
"""
from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.throttling import UserRateThrottle, AnonRateThrottle
from rest_framework_simplejwt.tokens import RefreshToken
from django.shortcuts import get_object_or_404
from django.db.models import Avg, Sum, F, Q
from django.contrib.auth import authenticate
from django.core.mail import send_mail
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.cache import cache
import secrets
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.contrib.auth import password_validation

import logging

logger = logging.getLogger(__name__)

def _client_ip(request):
    xff = request.META.get('HTTP_X_FORWARDED_FOR')
    if xff:
        return xff.split(',')[0].strip()
    return request.META.get('REMOTE_ADDR') or 'unknown'


try:
    from drf_yasg.utils import swagger_auto_schema
    from drf_yasg import openapi

    # Use swagger_auto_schema directly as extend_schema
    extend_schema = swagger_auto_schema

    class OpenApiResponse(openapi.Response):
        """Wrapper for drf-yasg Response"""
        def __init__(self, description='', schema=None):
            super().__init__(description=description, schema=schema)

    def inline_serializer(name=None, fields=None):
        """Create inline serializer for drf-yasg"""
        from rest_framework import serializers
        attrs = {'Meta': type('Meta', (), {'fields': '__all__'})}
        if fields:
            attrs.update(fields)
        return type(name or 'InlineSerializer', (serializers.Serializer,), attrs)

    class _OpenApiParameterDummy:
        QUERY = openapi.IN_QUERY
        PATH = openapi.IN_PATH
        HEADER = openapi.IN_HEADER
        def __init__(self, *a, **k):
            pass
    OpenApiParameter = _OpenApiParameterDummy

    class _Types:
        INT = openapi.TYPE_INTEGER
        STR = openapi.TYPE_STRING
        NUMBER = openapi.TYPE_NUMBER
        UUID = openapi.TYPE_STRING
        OBJECT = openapi.TYPE_OBJECT
    OpenApiTypes = _Types()
except Exception:
    def extend_schema(*args, **kwargs):
        def _decorator(obj):
            return obj
        return _decorator
    class _Dummy:
        def __init__(self, *a, **k):
            pass
        def __call__(self, *a, **k):
            return None
    OpenApiResponse = _Dummy
    def inline_serializer(name=None, fields=None):
        return object()
    class _OpenApiParameterDummy:
        QUERY = 'query'
        PATH = 'path'
        HEADER = 'header'
        def __init__(self, *a, **k):
            pass
    OpenApiParameter = _OpenApiParameterDummy
    class _Types:
        INT = int
        STR = str
        NUMBER = float
        UUID = str
        OBJECT = dict
    OpenApiTypes = _Types()
from rest_framework import serializers as rf_serializers

from rest_framework.exceptions import PermissionDenied
import uuid
import random
import string
import json
from .permissions import IsManager, IsManagerOrReadOnly
from .throttling import (
    LoginRateThrottle, RegisterRateThrottle, PasswordResetRateThrottle,
    OrderRateThrottle, PaymentRateThrottle, BurstRateThrottle
)
from .stripe_utils import (
    create_payment_intent, retrieve_payment_intent,
    confirm_payment_intent, cancel_payment_intent, construct_event,
    create_or_get_stripe_customer, attach_payment_method, detach_payment_method,
    set_default_payment_method, get_customer_payment_methods, create_refund,
    create_payment_intent_with_customer, process_webhook_event, mark_webhook_processed,
    get_payment_statistics, get_revenue_by_period
)
from .tasks import (
    schedule,
    schedule_reco_rebuild,
    send_order_confirmation_email,
    send_payment_status_notification,
    notify_low_stock_alert,
    send_push_notification,
    build_user_recommendations,
)

from .models import (
    User, Role, UserRole, Category, Product, ProductImage, ProductModerationLog,
    Cart, CartItem, Order, OrderLine, Review, Offer, UserBehavior,
    InventoryChange, InventoryBatch, Supplier, SalesReport, InventoryAlert,
    StripePayment, StripeCustomer, StripePaymentMethod, StripeRefund, StripeWebhookEvent,
    PaymentAnalytics, Promocode, PromoCodeUsage, PersonalizedRecommendation
)
from .serializers import (
    UserSerializer, RoleSerializer, UserRoleSerializer,
    CategorySerializer, ProductSerializer, ProductImageSerializer, ProductModerationLogSerializer,
    CartSerializer, CartItemSerializer, OrderSerializer, OrderLineSerializer,
    ReviewSerializer, OfferSerializer, UserBehaviorSerializer,
    UserRegistrationSerializer, InventoryChangeSerializer, InventoryBatchSerializer,
    SupplierSerializer, SalesReportSerializer, InventoryAlertSerializer,
    StripePaymentSerializer, StripeCustomerSerializer, StripePaymentMethodSerializer,
    StripeRefundSerializer, StripeWebhookEventSerializer, PaymentAnalyticsSerializer,
    PromocodeSerializer, PromoCodeUsageSerializer, PersonalizedRecommendationSerializer
)

# Authentication Views
class RegisterView(APIView):
    permission_classes = [permissions.AllowAny]
    throttle_classes = [RegisterRateThrottle]

    @extend_schema(
        request=UserRegistrationSerializer,
        responses={
            201: inline_serializer(name='RegisterResponse', fields={
                'refresh': rf_serializers.CharField(),
                'access': rf_serializers.CharField(),
                'user': UserSerializer()
            })
        }
    )

    def post(self, request):
        serializer = UserRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            refresh = RefreshToken.for_user(user)
            # Send welcome email (best-effort)
            try:
                ctx = {
                    'site_name': getattr(settings, 'SITE_NAME', 'MyEcommerce'),
                    'frontend_url': getattr(settings, 'FRONTEND_URL', ''),
                    'user': user,
                }
                html = render_to_string('emails/registration_welcome.html', ctx)
                plain = strip_tags(html)
                send_mail(
                    subject=f"Welcome to {ctx['site_name']}!",
                    message=plain,
                    from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', None),
                    recipient_list=[user.email],
                    html_message=html,
                    fail_silently=True,
                )
            except Exception:
                pass

            return Response({
                'refresh': str(refresh),
                'access': str(refresh.access_token),
                'user': UserSerializer(user).data
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class LoginView(APIView):
    permission_classes = [permissions.AllowAny]
    throttle_classes = [LoginRateThrottle]

    @extend_schema(
        request=inline_serializer(name='LoginRequest', fields={
            'email': rf_serializers.EmailField(required=False),
            'username': rf_serializers.CharField(required=False),
            'password': rf_serializers.CharField(write_only=True)
        }),
        responses={
            200: inline_serializer(name='LoginResponse', fields={
                'refresh': rf_serializers.CharField(),
                'access': rf_serializers.CharField(),
                'user': UserSerializer()
            })
        }
    )

    def post(self, request):
        email = request.data.get('email')
        username = request.data.get('username')
        password = request.data.get('password')

        if not password:
            return Response({'error': 'Password is required'}, status=status.HTTP_400_BAD_REQUEST)

        # If an email is provided (or the username looks like an email), resolve to username
        if email or (username and '@' in str(username)):
            try:
                lookup_email = email or username
                user_obj = User.objects.get(email__iexact=lookup_email)
                username = user_obj.get_username()
            except User.DoesNotExist:
                # Do not reveal whether the email exists
                return Response({'error': 'Invalid credentials'}, status=status.HTTP_401_UNAUTHORIZED)

        user = authenticate(request=request, username=username, password=password)
        if user:
            refresh = RefreshToken.for_user(user)
            return Response({
                'refresh': str(refresh),
                'access': str(refresh.access_token),
                'user': UserSerializer(user).data
            })
        return Response({'error': 'Invalid credentials'}, status=status.HTTP_401_UNAUTHORIZED)

class ForgotPasswordView(APIView):
    permission_classes = [permissions.AllowAny]
    throttle_classes = [PasswordResetRateThrottle]

    @extend_schema(
        request=inline_serializer(name='ForgotPasswordRequest', fields={
            'email': rf_serializers.EmailField()
        }),
        responses={
            200: inline_serializer(name='ForgotPasswordResponse', fields={'message': rf_serializers.CharField()})
        }
    )

    def post(self, request):
        email = request.data.get('email')
        if not email:
            return Response({'error': 'Email is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(email=email)

            # Generate a secure reset token and store short-lived reference in cache
            reset_token = secrets.token_urlsafe(32)

            # Persist token on the user for validation (cache drives expiration)
            user.reset_token = reset_token
            user.save(update_fields=['reset_token'])

            # Cache mapping token -> user_id with TTL for expiration enforcement
            ttl = int(getattr(settings, 'RESET_PASSWORD_TOKEN_TTL_SECONDS', 3600))
            cache.set(f'password_reset:{reset_token}', str(user.id), ttl)

            # Build reset link and send email (HTML template with text fallback)
            reset_link = f"{settings.FRONTEND_URL}/reset-password?token={reset_token}"
            subject = f"Reset your {getattr(settings, 'SITE_NAME', 'Account')} password"
            context = {
                'site_name': getattr(settings, 'SITE_NAME', 'MyEcommerce'),
                'frontend_url': settings.FRONTEND_URL,
                'reset_link': reset_link,
                'ttl_minutes': max(1, ttl // 60),
                'user': user,
            }
            html_message = render_to_string('emails/reset_password.html', context)
            plain_message = strip_tags(html_message)

            try:
                send_mail(subject, plain_message, getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'), [email], fail_silently=False, html_message=html_message)
            except Exception:
                # Do not leak email backend errors to client; log server-side in production
                pass

            return Response({'message': 'Password reset instructions sent to your email'})

        except User.DoesNotExist:
            # For security reasons, don't reveal that the email doesn't exist
            return Response({'message': 'Password reset instructions sent to your email'})


# Recommendations API
class RecommendationView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        parameters=[
            OpenApiParameter(name='limit', type=OpenApiTypes.INT, location=OpenApiParameter.QUERY, description='Max items to return (default 12)', required=False)
        ],
        responses={200: PersonalizedRecommendationSerializer(many=True)}
    )

    def get(self, request):
        """Return personalized recommendations for the current user.
        Optional query param: limit (default 12)
        """
        limit = int(request.query_params.get('limit', 12))
        qs = (PersonalizedRecommendation.objects
              .filter(user=request.user)
              .select_related('product')
              .order_by('-recommendation_score')[:limit])
        serializer = PersonalizedRecommendationSerializer(qs, many=True)
        return Response(serializer.data)

    @extend_schema(
        request=None,
        responses={
            202: inline_serializer(name='RecoRebuildResponse', fields={'status': rf_serializers.CharField()})
        }
    )

    def post(self, request):
        """Trigger rebuild of recommendations for current user (async if Celery available)."""
        try:
            schedule_reco_rebuild(request.user.id, event='manual')
        except Exception:
            # Best-effort, still return accepted
            pass
        return Response({"status": "rebuild_scheduled"}, status=status.HTTP_202_ACCEPTED)

            # Uncomment this to actually send the email if email settings are configured

class ResetPasswordView(APIView):
    permission_classes = [permissions.AllowAny]
    throttle_classes = [PasswordResetRateThrottle]

    @extend_schema(
        request=inline_serializer(name='ResetPasswordRequest', fields={
            'token': rf_serializers.CharField(),
            'new_password': rf_serializers.CharField()
        }),
        responses={
            200: inline_serializer(name='ResetPasswordResponse', fields={'message': rf_serializers.CharField()})
        }
    )
    def post(self, request):
        token = request.data.get('token')
        new_password = request.data.get('new_password')

        if not token or not new_password:
            return Response({'error': 'Token and new password are required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Validate token via cache-based TTL
            user_id = cache.get(f'password_reset:{token}')
            if not user_id:
                return Response({'error': 'Invalid or expired token'}, status=status.HTTP_400_BAD_REQUEST)

            user = User.objects.get(id=user_id, reset_token=token)

            # Enforce password validators
            try:
                password_validation.validate_password(new_password, user=user)
            except ValidationError as ve:
                return Response({'error': 'Password does not meet requirements', 'details': ve.messages}, status=status.HTTP_400_BAD_REQUEST)

            # Set the new password
            user.set_password(new_password)
            user.reset_token = None
            user.save()

            # Invalidate the token in cache
            cache.delete(f'password_reset:{token}')

            return Response({'message': 'Password has been reset successfully'})

        except User.DoesNotExist:
            return Response({'error': 'Invalid or expired token'}, status=status.HTTP_400_BAD_REQUEST)


class CSRFTokenView(APIView):
    """
    View to get CSRF token for frontend applications.
    """
    permission_classes = [permissions.AllowAny]
    throttle_classes = [BurstRateThrottle]

    @extend_schema(
        responses={
            200: inline_serializer(name='CSRFTok', fields={
                'csrfToken': rf_serializers.CharField()
            })
        }
    )

    def get(self, request):
        from django.middleware.csrf import get_token
        csrf_token = get_token(request)
        return Response({'csrfToken': csrf_token})


class GoogleAuthView(APIView):
    permission_classes = [permissions.AllowAny]
    throttle_classes = [LoginRateThrottle, BurstRateThrottle]

    @extend_schema(
        request=inline_serializer(name='GoogleAuthRequest', fields={
            'id_token': rf_serializers.CharField()
        }),
        responses={
            200: inline_serializer(name='GoogleAuthResponse', fields={
                'refresh': rf_serializers.CharField(),
                'access': rf_serializers.CharField(),
                'user': UserSerializer()
            })
        }
    )

    def post(self, request):
        ip = _client_ip(request)
        ua = request.META.get('HTTP_USER_AGENT', '-')
        logger.info(f"GoogleAuth attempt ip=%s ua=%s", ip, ua)

        if not getattr(settings, 'GOOGLE_OAUTH_ENABLED', True):
            return Response({'error': 'Google OAuth is disabled'}, status=status.HTTP_400_BAD_REQUEST)
        client_id = getattr(settings, 'GOOGLE_CLIENT_ID', None)
        if not client_id:
            return Response({'error': 'Server not configured for Google OAuth (missing GOOGLE_CLIENT_ID)'}, status=status.HTTP_400_BAD_REQUEST)

        id_token_str = request.data.get('id_token')
        if not id_token_str:
            return Response({'error': 'id_token is required'}, status=status.HTTP_400_BAD_REQUEST)

        payload = None
        # First try local verification if google-auth is installed
        try:
            from google.oauth2 import id_token as google_id_token  # type: ignore
            from google.auth.transport import requests as google_requests  # type: ignore
            req = google_requests.Request()
            payload = google_id_token.verify_oauth2_token(id_token_str, req, client_id)
        except Exception:
            # Fallback to Google's tokeninfo endpoint (no extra dependencies)
            try:
                import urllib.request, urllib.parse, json as _json, time
                url = 'https://oauth2.googleapis.com/tokeninfo?' + urllib.parse.urlencode({'id_token': id_token_str})
                with urllib.request.urlopen(url, timeout=5) as resp:
                    data = resp.read()
                payload = _json.loads(data.decode('utf-8'))
                aud = payload.get('aud') or payload.get('audience')
                if aud != client_id:
                    return Response({'error': 'Invalid token audience'}, status=status.HTTP_400_BAD_REQUEST)
                exp = payload.get('exp')
                if exp and int(exp) < int(time.time()):
                    return Response({'error': 'Token expired'}, status=status.HTTP_400_BAD_REQUEST)
            except Exception:
                logger.warning("GoogleAuth invalid token ip=%s", ip)
                return Response({'error': 'Invalid Google token'}, status=status.HTTP_400_BAD_REQUEST)

        email = payload.get('email') if payload else None
        if not email:
            return Response({'error': 'Email not present in token'}, status=status.HTTP_400_BAD_REQUEST)
        email_verified = str(payload.get('email_verified', 'true')).lower() in ('true', '1', 't', 'yes')
        name = payload.get('name') or email.split('@')[0]

        # Find or create user by email
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            base = email.split('@')[0]
            username = base
            i = 0
            while User.objects.filter(username=username).exists():
                i += 1
                username = f"{base}{i}"
            user = User(username=username, email=email, email_validated=email_verified, is_active=True)
            # Best-effort name split
            try:
                first, last = name.split(' ', 1)
                user.first_name, user.last_name = first, last
            except Exception:
                user.first_name = name
            user.set_unusable_password()
            user.save()
        else:


            if email_verified and not user.email_validated:
                user.email_validated = True
                user.save(update_fields=['email_validated'])

        logger.info("GoogleAuth success ip=%s email=%s verified=%s", ip, email, email_verified)

        # Issue JWT tokens
        refresh = RefreshToken.for_user(user)
        return Response({
            'refresh': str(refresh),
            'access': str(refresh.access_token),
            'user': UserSerializer(user).data
        })

# User Views
class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter]
    search_fields = ['username', 'email', 'first_name', 'last_name']

    def get_permissions(self):
        if self.action == 'create':
            return [permissions.IsAdminUser()]
        return super().get_permissions()

    @action(detail=False, methods=['get'])
    def me(self, request):
        serializer = self.get_serializer(request.user)
        return Response(serializer.data)

# Role Views
class RoleViewSet(viewsets.ModelViewSet):
    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    permission_classes = [permissions.IsAdminUser]

# Category Views
class CategoryViewSet(viewsets.ModelViewSet):
    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name', 'description']

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [permissions.IsAdminUser()]
        return super().get_permissions()

    @action(detail=True, methods=['get'])
    def products(self, request, pk=None):
        category = self.get_object()
        products = Product.objects.filter(category=category, is_active=True)
        serializer = ProductSerializer(products, many=True)
        return Response(serializer.data)

# Product Views
class ProductViewSet(viewsets.ModelViewSet):
    queryset = Product.objects.filter(is_active=True)
    serializer_class = ProductSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [filters.SearchFilter]
    search_fields = ['title', 'description', 'tags']

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsManager()]  # Allow both managers and admins
        return super().get_permissions()

    @action(detail=True, methods=['get'])
    def reviews(self, request, pk=None):
        product = self.get_object()
        reviews = Review.objects.filter(product=product)
        serializer = ReviewSerializer(reviews, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def rating(self, request, pk=None):
        product = self.get_object()
        avg_rating = Review.objects.filter(product=product).aggregate(Avg('rating'))
        return Response({'average_rating': avg_rating['rating__avg'] or 0})

# Product Image Views
class ProductImageViewSet(viewsets.ModelViewSet):
    """ViewSet for managing product images"""
    queryset = ProductImage.objects.all()
    serializer_class = ProductImageSerializer
    permission_classes = [IsManager]  # Allow both managers and admins

    def get_queryset(self):
        """Filter images by product if product_id is provided"""
        queryset = super().get_queryset()
        product_id = self.request.query_params.get('product_id')
        if product_id:
            queryset = queryset.filter(product__id=product_id)
        return queryset

    def perform_create(self, serializer):
        """Set the product when creating a new image"""
        product_id = self.request.data.get('product_id')
        product = get_object_or_404(Product, id=product_id)
        serializer.save(product=product)

# Product Moderation Views
class ProductModerationLogViewSet(viewsets.ModelViewSet):
    queryset = ProductModerationLog.objects.all()
    serializer_class = ProductModerationLogSerializer
    permission_classes = [IsManager]  # Allow both managers and admins

    @action(detail=False, methods=['post'])
    def moderate(self, request):
        product_id = request.data.get('product_id')
        action = request.data.get('action')
        comments = request.data.get('comments', '')

        product = get_object_or_404(Product, id=product_id)

        log = ProductModerationLog.objects.create(
            product=product,
            moderator=request.user,
            action=action,
            comments=comments
        )

        # Update product status based on action
        if action == 'approve':
            product.is_active = True
        elif action == 'reject':
            product.is_active = False
        product.save()

        serializer = self.get_serializer(log)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

# Cart Views
@extend_schema(parameters=[OpenApiParameter(name='id', type=OpenApiTypes.UUID, location=OpenApiParameter.PATH, required=True)])
class CartViewSet(viewsets.ModelViewSet):
    serializer_class = CartSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Cart.objects.filter(created_by=self.request.user)

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def add_item(self, request, pk=None):
        cart = self.get_object()
        product_id = request.data.get('product_id')
        quantity = int(request.data.get('quantity', 1))

        product = get_object_or_404(Product, id=product_id)

        # Check if item already exists in cart
        try:
            cart_item = CartItem.objects.get(cart=cart, product=product)
            cart_item.quantity += quantity
            cart_item.save()
        except CartItem.DoesNotExist:
            CartItem.objects.create(
                cart=cart,
                product=product,
                price=product.price,
                quantity=quantity
            )


        # Event trigger: rebuild recommendations for this user after cart change
        try:
            schedule_reco_rebuild(request.user.id, event='cart_change')
        except Exception:
            pass

        serializer = self.get_serializer(cart)
        return Response(serializer.data)


    @action(detail=True, methods=['post'])
    def remove_item(self, request, pk=None):
        cart = self.get_object()
        item_id = request.data.get('item_id')

        item = get_object_or_404(CartItem, id=item_id, cart=cart)
        item.delete()

        # Event trigger: rebuild recommendations for this user after cart change
        try:
            schedule_reco_rebuild(request.user.id, event='cart_change')
        except Exception:
            pass

        serializer = self.get_serializer(cart)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def update_item(self, request, pk=None):
        cart = self.get_object()
        item_id = request.data.get('item_id')
        quantity = int(request.data.get('quantity', 1))

        item = get_object_or_404(CartItem, id=item_id, cart=cart)
        item.quantity = quantity
        item.save()

        # Event trigger: rebuild recommendations for this user after cart change
        try:
            schedule_reco_rebuild(request.user.id, event='cart_change')
        except Exception:
            pass


        serializer = self.get_serializer(cart)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def checkout(self, request, pk=None):
        cart = self.get_object()

        # Calculate total price
        total_price = sum(item.price * item.quantity for item in cart.items.all())

        # Create order
        order = Order.objects.create(
            user=request.user,
            total_price=total_price,
            payment_status='pending',
            delivery_status='processing'
        )

        # Create order lines and update inventory
        for item in cart.items.all():
            OrderLine.objects.create(
                order=order,
                product=item.product,
                price=item.price,
                quantity=item.quantity
            )

            # Update inventory using batch-level FIFO method
            try:
                batch_changes, overall_change = item.product.subtract_batch_inventory(
                    quantity=item.quantity,
                    user=request.user,
                    order=order,
                    notes=f"Order {order.id} - Automatic FIFO inventory reduction"
                )

                # Log successful batch deductions
                for batch_change in batch_changes:
                    print(f"Deducted {batch_change['quantity_used']} units from batch {batch_change['batch'].batch_number}")

            except ValidationError as e:
                # If batch inventory update fails, try regular inventory update as fallback
                try:
                    item.product.order_inventory(
                        quantity=item.quantity,
                        user=request.user,
                        order=order,
                        notes=f"Order {order.id} - Fallback inventory reduction (batch method failed)"
                    )
                    print(f"Used fallback inventory method for product {item.product.id}")
                except ValidationError as fallback_error:
                    # If both methods fail, log the error but continue with order
                    print(f"Both batch and fallback inventory update failed for product {item.product.id}: {str(fallback_error)}")
                    # In production, you might want to cancel the order or handle this differently

        # Update cart status

        # Event trigger: rebuild recommendations for this user after checkout
        try:
            schedule_reco_rebuild(request.user.id, event='checkout')
        except Exception:
            pass


        # Trigger async notifications (Celery) with safe sync fallback
        try:
            schedule(send_order_confirmation_email, order.id)
        except Exception:
            pass

        # Low stock alerts for any products affected by this checkout
        try:
            threshold = getattr(settings, 'INVENTORY_LOW_STOCK_THRESHOLD', 10)
            for item in cart.items.select_related('product').all():
                product = item.product
                if product and product.stock_quantity is not None and product.stock_quantity <= threshold:
                    alert = InventoryAlert.create_low_stock_alert(product, product.stock_quantity, threshold)
                    schedule(notify_low_stock_alert, alert.id)
        except Exception:
            # Do not block checkout on alert errors
            pass

        cart.status = 'converted'
        cart.save()

        return Response({
            'order_id': order.id,
            'message': 'Order created successfully'
        }, status=status.HTTP_201_CREATED)
# Order Views
@extend_schema(parameters=[OpenApiParameter(name='id', type=OpenApiTypes.UUID, location=OpenApiParameter.PATH, required=True)])
class OrderViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Order.objects.filter(user=self.request.user)

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        order = self.get_object()

        if order.delivery_status in ['delivered', 'cancelled']:
            return Response({'error': 'Cannot cancel this order'}, status=status.HTTP_400_BAD_REQUEST)

        # Return inventory for each order line
        for order_line in order.order_lines.all():
            try:
                order_line.product.return_inventory(
                    quantity=order_line.quantity,
                    user=request.user,
                    order=order,
                    notes=f"Order {order.id} cancelled - Inventory returned"
                )
            except ValidationError as e:
                # Log error but continue with cancellation
                print(f"Inventory return error for product {order_line.product.id}: {str(e)}")

        order.delivery_status = 'cancelled'
        order.save()

        serializer = self.get_serializer(order)
        return Response(serializer.data)

# Admin Order Views
class AdminOrderViewSet(viewsets.ModelViewSet):
    queryset = Order.objects.all()
    serializer_class = OrderSerializer
    permission_classes = [IsManager]  # Allow both managers and admins

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        order = self.get_object()
        payment_status = request.data.get('payment_status')
        delivery_status = request.data.get('delivery_status')

        if payment_status:
            order.payment_status = payment_status

        if delivery_status:
            order.delivery_status = delivery_status

        order.save()

        # Notify user about status change (push/log fallback)
        try:
            schedule(
                send_push_notification,
                order.user.id,
                'Order status updated',
                f"Your order #{order.id} status has been updated. Payment: {order.payment_status}, Delivery: {order.delivery_status}",
                {'order_id': order.id}
            )
        except Exception:
            pass


        serializer = self.get_serializer(order)
        return Response(serializer.data)

# Review Views
class ReviewViewSet(viewsets.ModelViewSet):
    serializer_class = ReviewSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]

    def get_queryset(self):
        return Review.objects.all()

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

# Offer Views
class OfferViewSet(viewsets.ModelViewSet):
    queryset = Offer.objects.filter(is_active=True)
    serializer_class = OfferSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsManager()]  # Allow both managers and admins
        return super().get_permissions()

    @action(detail=False, methods=['get'])
    def active(self, request):
        from django.utils import timezone
        now = timezone.now()

        offers = Offer.objects.filter(
            is_active=True,
            start_date__lte=now,
            end_date__gte=now
        )

        serializer = self.get_serializer(offers, many=True)
        return Response(serializer.data)

# User Behavior Tracking
class UserBehaviorView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        request=inline_serializer(name='UserBehaviorRequest', fields={
            'action': rf_serializers.CharField(),
            'product_id': rf_serializers.IntegerField(required=False),
            'metadata': rf_serializers.DictField(required=False),
            'session_id': rf_serializers.CharField(required=False)
        }),
        responses={201: UserBehaviorSerializer}
    )

    def post(self, request):
        action = request.data.get('action')
        product_id = request.data.get('product_id')
        metadata = request.data.get('metadata', {})

        # Get additional tracking data
        session_id = request.data.get('session_id')
        page = metadata.get('page')

        # Get client info
        ip_address = self.get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        referrer = request.META.get('HTTP_REFERER', '')

        # Create behavior object
        behavior_data = {
            'user': request.user,
            'action': action,
            'metadata': metadata,
            'session_id': session_id,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'referrer': referrer,
            'page': page
        }

        # Add product if provided
        if product_id:
            try:
                product = Product.objects.get(id=product_id)
                behavior_data['product'] = product
            except Product.DoesNotExist:
                # If product doesn't exist, we still track the behavior without the product
                pass

        behavior = UserBehavior.objects.create(**behavior_data)

        # Event trigger: rebuild recommendations for this user (async if Celery available)
        try:
            schedule_reco_rebuild(request.user.id, event=request.data.get('action', 'behavior'))
        except Exception:
            pass

        serializer = UserBehaviorSerializer(behavior)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def get(self, request):
        """Get user behavior data for the current user or a specific user (admin only)"""
        user_id = request.query_params.get('user')

        if user_id and request.user.is_staff:
            # Admin can view any user's behavior
            behaviors = UserBehavior.objects.filter(user__id=user_id).order_by('-action_time')
        else:
            # Regular users can only view their own behavior
            behaviors = UserBehavior.objects.filter(user=request.user).order_by('-action_time')

        page = self.paginate_queryset(behaviors, request)
        if page is not None:
            serializer = UserBehaviorSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = UserBehaviorSerializer(behaviors, many=True)
        return Response(serializer.data)

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


# Analytics API Views
class GeneralAnalyticsView(APIView):
    """
    API view for general analytics data (admin/manager only)
    """
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        parameters=[
            OpenApiParameter(name='range', type=OpenApiTypes.STR, location=OpenApiParameter.QUERY, required=False, description='Time range: day|week|month|year')
        ]
    )

    def get(self, request):
        # Check if user is staff or manager
        if not (request.user.is_staff or request.user.groups.filter(name='manager').exists()):
            return Response(
                {'error': 'Permission denied. Admin or manager access required.'},
                status=status.HTTP_403_FORBIDDEN
            )

        from django.utils import timezone
        from datetime import timedelta
        from django.db.models import Count, Q, F, Sum, Avg
        from django.contrib.gis.geoip2 import GeoIP2
        from django.core.exceptions import ObjectDoesNotExist

        # Get time range from query params
        time_range = request.query_params.get('range', 'week')
        now = timezone.now()

        if time_range == 'day':
            start_date = now - timedelta(days=1)
        elif time_range == 'week':
            start_date = now - timedelta(weeks=1)
        elif time_range == 'month':
            start_date = now - timedelta(days=30)
        elif time_range == 'year':
            start_date = now - timedelta(days=365)
        else:
            start_date = now - timedelta(weeks=1)

        # Get user behaviors in the time range
        behaviors = UserBehavior.objects.filter(action_time__gte=start_date)

        # Unique users count
        unique_users = behaviors.values('user').distinct().count()

        # Page views count
        page_views = behaviors.filter(action='page_view').count()

        # Product views count
        product_views = behaviors.filter(action='view').count()

        # Add to cart count
        add_to_cart_count = behaviors.filter(action='add_to_cart').count()

        # Purchase count
        purchase_count = behaviors.filter(action='purchase').count()

        # Conversion rate (purchases / unique users)
        conversion_rate = (purchase_count / unique_users * 100) if unique_users > 0 else 0

        # Most viewed products
        most_viewed_products = behaviors.filter(
            action='view',
            product__isnull=False
        ).values(
            'product__id',
            'product__title'
        ).annotate(
            view_count=Count('id')
        ).order_by('-view_count')[:10]

        # Most added to cart products
        most_cart_products = behaviors.filter(
            action='add_to_cart',
            product__isnull=False
        ).values(
            'product__id',
            'product__title'
        ).annotate(
            cart_count=Count('id')
        ).order_by('-cart_count')[:10]

        # Most purchased products
        most_purchased_products = behaviors.filter(
            action='purchase',
            product__isnull=False
        ).values(
            'product__id',
            'product__title'
        ).annotate(
            purchase_count=Count('id')
        ).order_by('-purchase_count')[:10]

        # Users by region (based on IP addresses)
        users_by_region = {}
        try:
            g = GeoIP2()
            ip_addresses = behaviors.values_list('ip_address', flat=True).distinct()

            for ip in ip_addresses:
                if ip:
                    try:
                        country = g.country(ip)
                        country_name = country['country_name']
                        users_by_region[country_name] = users_by_region.get(country_name, 0) + 1
                    except:
                        users_by_region['Unknown'] = users_by_region.get('Unknown', 0) + 1
        except:
            users_by_region = {'Unknown': unique_users}

        # Daily activity for the past week
        daily_activity = []
        for i in range(7):
            day_start = now - timedelta(days=i+1)
            day_end = now - timedelta(days=i)
            day_behaviors = behaviors.filter(
                action_time__gte=day_start,
                action_time__lt=day_end
            )
            daily_activity.append({
                'date': day_start.strftime('%Y-%m-%d'),
                'page_views': day_behaviors.filter(action='page_view').count(),
                'product_views': day_behaviors.filter(action='view').count(),
                'add_to_cart': day_behaviors.filter(action='add_to_cart').count(),
                'purchases': day_behaviors.filter(action='purchase').count(),
                'unique_users': day_behaviors.values('user').distinct().count()
            })

        return Response({
            'summary': {
                'unique_users': unique_users,
                'page_views': page_views,
                'product_views': product_views,
                'add_to_cart_count': add_to_cart_count,
                'purchase_count': purchase_count,
                'conversion_rate': round(conversion_rate, 2)
            },
            'top_products': {
                'most_viewed': list(most_viewed_products),
                'most_added_to_cart': list(most_cart_products),
                'most_purchased': list(most_purchased_products)
            },
            'users_by_region': users_by_region,
            'daily_activity': daily_activity,
            'time_range': time_range
        })


class UserAnalyticsView(APIView):
    """
    API view for individual user analytics
    """
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        parameters=[
            OpenApiParameter(name='range', type=OpenApiTypes.STR, location=OpenApiParameter.QUERY, required=False, description='Time range: day|week|month|year')
        ]
    )

    def get(self, request, user_id=None):
        # Users can only view their own analytics unless they're staff/manager
        if user_id:
            if not (request.user.is_staff or request.user.groups.filter(name='manager').exists()):
                if str(request.user.id) != user_id:
                    return Response(
                        {'error': 'Permission denied. You can only view your own analytics.'},
                        status=status.HTTP_403_FORBIDDEN
                    )
            try:
                target_user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                return Response(
                    {'error': 'User not found.'},
                    status=status.HTTP_404_NOT_FOUND
                )
        else:
            target_user = request.user

        from django.utils import timezone
        from datetime import timedelta
        from django.db.models import Count, Q

        # Get time range from query params
        time_range = request.query_params.get('range', 'week')
        now = timezone.now()

        if time_range == 'day':
            start_date = now - timedelta(days=1)
        elif time_range == 'week':
            start_date = now - timedelta(weeks=1)
        elif time_range == 'month':
            start_date = now - timedelta(days=30)
        elif time_range == 'year':
            start_date = now - timedelta(days=365)
        else:
            start_date = now - timedelta(weeks=1)

        # Get user behaviors
        behaviors = UserBehavior.objects.filter(
            user=target_user,
            action_time__gte=start_date
        ).order_by('-action_time')

        # Activity summary
        activity_summary = {
            'page_views': behaviors.filter(action='page_view').count(),
            'product_views': behaviors.filter(action='view').count(),
            'searches': behaviors.filter(action='search').count(),
            'add_to_cart': behaviors.filter(action='add_to_cart').count(),
            'purchases': behaviors.filter(action='purchase').count(),
            'total_activities': behaviors.count()
        }

        # Most viewed products
        viewed_products = behaviors.filter(
            action='view',
            product__isnull=False
        ).values(
            'product__id',
            'product__title'
        ).annotate(
            view_count=Count('id')
        ).order_by('-view_count')[:5]

        # Recent activities (last 20)
        recent_activities = []
        for behavior in behaviors[:20]:
            recent_activities.append({
                'id': behavior.id,
                'action': behavior.action,
                'product': {
                    'id': behavior.product.id,
                    'title': behavior.product.title
                } if behavior.product else None,
                'page': behavior.page,
                'timestamp': behavior.action_time.isoformat(),
                'metadata': behavior.metadata
            })

        # Daily activity breakdown
        daily_breakdown = []
        for i in range(7):
            day_start = now - timedelta(days=i+1)
            day_end = now - timedelta(days=i)
            day_behaviors = behaviors.filter(
                action_time__gte=day_start,
                action_time__lt=day_end
            )
            daily_breakdown.append({
                'date': day_start.strftime('%Y-%m-%d'),
                'activities': day_behaviors.count(),
                'page_views': day_behaviors.filter(action='page_view').count(),
                'product_views': day_behaviors.filter(action='view').count(),
                'add_to_cart': day_behaviors.filter(action='add_to_cart').count()
            })

        return Response({
            'user': {
                'id': target_user.id,
                'username': target_user.username,
                'email': target_user.email if request.user == target_user or request.user.is_staff else None
            },
            'activity_summary': activity_summary,
            'viewed_products': list(viewed_products),
            'recent_activities': recent_activities,
            'daily_breakdown': daily_breakdown,
            'time_range': time_range
        })

# Home Page Data
class HomePageView(APIView):
    permission_classes = [permissions.AllowAny]

    def get(self, request):
        # Get featured products
        featured_products = Product.objects.filter(is_active=True)[:8]

        # Get categories
        categories = Category.objects.filter(parent_category=None)[:6]

        # Get active offers
        from django.utils import timezone
        now = timezone.now()
        offers = Offer.objects.filter(
            is_active=True,
            start_date__lte=now,
            end_date__gte=now
        )[:4]

        return Response({
            'featured_products': ProductSerializer(featured_products, many=True).data,
            'categories': CategorySerializer(categories, many=True).data,
            'offers': OfferSerializer(offers, many=True).data
        })

# Search View
class SearchView(APIView):
    """
    API endpoint for searching products using Typesense.

    This view provides powerful search capabilities including:
    - Full-text search across product fields
    - Filtering by category, price range, etc.
    - Sorting by relevance, price, etc.
    - Pagination of results
    """
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        parameters=[
            OpenApiParameter('q', OpenApiTypes.STR, OpenApiParameter.QUERY, description='Search query', required=False),
            OpenApiParameter('category', OpenApiTypes.INT, OpenApiParameter.QUERY, required=False),
            OpenApiParameter('min_price', OpenApiTypes.NUMBER, OpenApiParameter.QUERY, required=False),
            OpenApiParameter('max_price', OpenApiTypes.NUMBER, OpenApiParameter.QUERY, required=False),
            OpenApiParameter('sort_by', OpenApiTypes.STR, OpenApiParameter.QUERY, required=False),
            OpenApiParameter('page', OpenApiTypes.INT, OpenApiParameter.QUERY, required=False),
            OpenApiParameter('per_page', OpenApiTypes.INT, OpenApiParameter.QUERY, required=False)
        ]
    )

    def get(self, request):
        """
        Handle GET requests for product search.

        Query parameters:
        - q: Search query string
        - category: Category ID to filter by
        - min_price: Minimum price filter
        - max_price: Maximum price filter
        - sort_by: Field to sort by (e.g., price:asc, price:desc)
        - page: Page number for pagination
        - per_page: Number of results per page

        Returns:
            Response with search results and metadata
        """
        import os
        from .typesense_utils import search_products

        # Extract search parameters
        query = request.query_params.get('q', '')
        category_id = request.query_params.get('category', None)
        min_price = request.query_params.get('min_price', None)
        max_price = request.query_params.get('max_price', None)
        sort_by = request.query_params.get('sort_by', None)
        page = int(request.query_params.get('page', 1))
        per_page = int(request.query_params.get('per_page', 10))

        # Check if Typesense is enabled
        typesense_enabled = os.environ.get('TYPESENSE_ENABLED', 'False').lower() == 'true'

        # If Typesense is not enabled, use basic search
        if not typesense_enabled:
            # Basic search using Django ORM
            products = Product.objects.filter(is_active=True)

            if query:
                products = products.filter(title__icontains=query) | products.filter(description__icontains=query)

            if category_id:
                products = products.filter(category_id=category_id)

            # Apply pagination
            start = (page - 1) * per_page
            end = start + per_page
            paginated_products = products[start:end]

            serializer = ProductSerializer(paginated_products, many=True)

            return Response({
                'results': serializer.data,
                'found': products.count(),
                'page': page,
                'per_page': per_page,
                'total_pages': (products.count() + per_page - 1) // per_page,
                'search_type': 'basic'
            })

        # Build filters for Typesense
        filters = {}
        if category_id:
            filters['category_id'] = category_id

        # Add price range filter if provided
        price_filter = []
        if min_price:
            price_filter.append(f"price:>={float(min_price)}")
        if max_price:
            price_filter.append(f"price:<={float(max_price)}")
        if price_filter:
            filters['price_filter'] = ' && '.join(price_filter)

        # Only show active products
        filters['is_active'] = True

        try:
            # Search using Typesense
            search_results = search_products(
                query=query,
                filters=filters,
                sort_by=sort_by,
                page=page,
                per_page=per_page
            )

            # Extract product IDs from search results
            product_ids = [hit['document']['id'] for hit in search_results['hits']]

            # Fetch full product objects from database
            products = Product.objects.filter(id__in=product_ids)

            # Maintain the order from search results
            product_dict = {str(p.id): p for p in products}
            ordered_products = [product_dict[pid] for pid in product_ids if pid in product_dict]

            # Serialize and return results
            serializer = ProductSerializer(ordered_products, many=True)

            return Response({
                'results': serializer.data,
                'found': search_results['found'],
                'page': page,
                'per_page': per_page,
                'total_pages': (search_results['found'] + per_page - 1) // per_page,
                'search_type': 'typesense'
            })

        except Exception as e:
            # Fallback to basic search if Typesense fails
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Typesense search failed, falling back to basic search: {str(e)}")

            # Basic search using Django ORM
            products = Product.objects.filter(is_active=True)

            if query:
                products = products.filter(title__icontains=query) | products.filter(description__icontains=query)

            if category_id:
                products = products.filter(category_id=category_id)

            # Apply pagination
            start = (page - 1) * per_page
            end = start + per_page
            paginated_products = products[start:end]

            serializer = ProductSerializer(paginated_products, many=True)

            return Response({
                'results': serializer.data,
                'found': products.count(),
                'page': page,
                'per_page': per_page,
                'total_pages': (products.count() + per_page - 1) // per_page,
                'fallback': True  # Indicate that we're using the fallback search
            })

class ShippingOptionsView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        # In a real app, these would come from a database
        shipping_options = [
            {
                'id': 'standard',
                'name': 'Standard Shipping',
                'description': 'Delivery in 5-7 business days',
                'price': 5.99,
                'estimated_days': 7
            },
            {
                'id': 'express',
                'name': 'Express Shipping',
                'description': 'Delivery in 2-3 business days',
                'price': 12.99,
                'estimated_days': 3
            },
            {
                'id': 'overnight',
                'name': 'Overnight Shipping',
                'description': 'Next day delivery for orders placed before 2pm',
                'price': 19.99,
                'estimated_days': 1
            }
        ]
        return Response(shipping_options)

class PaymentProcessingView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    throttle_classes = [PaymentRateThrottle]

    @extend_schema(
        request=inline_serializer(name='PaymentProcessingRequest', fields={
            'payment_method_id': rf_serializers.CharField(required=False),
            'order_id': rf_serializers.IntegerField()
        }),
        responses={
            200: inline_serializer(name='PaymentProcessingResponse', fields={
                'success': rf_serializers.BooleanField(),
                'payment_intent_client_secret': rf_serializers.CharField(required=False),
                'payment_intent_id': rf_serializers.CharField(required=False),
                'payment_intent': rf_serializers.CharField(required=False),
                'requires_action': rf_serializers.BooleanField(required=False),
                'message': rf_serializers.CharField(required=False),
                'error': rf_serializers.CharField(required=False)
            })
        }
    )

    def post(self, request):
        payment_method_id = request.data.get('payment_method_id')
        order_id = request.data.get('order_id')

        if not order_id:
            return Response({
                'error': 'Order ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Get the order
            order = Order.objects.get(id=order_id, user=request.user)

            # If payment is already processed, return success
            if order.payment_status == 'paid':
                return Response({
                    'success': True,
                    'message': 'Payment already processed'
                })

            # If there's an existing payment intent, use it
            if order.stripe_payment_intent_id:
                payment_intent = retrieve_payment_intent(order.stripe_payment_intent_id)

                # If payment intent is already succeeded, update order
                if payment_intent.status == 'succeeded':
                    order.payment_status = 'paid'
                    order.save()

                    # Create or update StripePayment record
                    payment, created = StripePayment.objects.update_or_create(
                        order=order,
                        payment_intent_id=payment_intent.id,
                        defaults={
                            'amount': order.total_price,
                            'status': 'succeeded',
                            'payment_method_id': payment_intent.payment_method,
                            'metadata': payment_intent.metadata
                        }
                    )

                    return Response({
                        'success': True,
                        'payment_intent': payment_intent.id,
                        'message': 'Payment processed successfully'
                    })

                # If payment intent needs confirmation with a payment method
                if payment_method_id and payment_intent.status in ['requires_payment_method', 'requires_confirmation']:
                    try:
                        payment_intent = confirm_payment_intent(payment_intent.id, payment_method_id)
                        order.stripe_payment_method_id = payment_method_id
                        order.save(update_fields=['stripe_payment_method_id'])

                        return Response({
                            'success': True,
                            'requires_action': payment_intent.status == 'requires_action',
                            'payment_intent_client_secret': payment_intent.client_secret,
                            'message': 'Payment intent confirmed'
                        })
                    except Exception as e:
                        return Response({
                            'error': str(e)
                        }, status=status.HTTP_400_BAD_REQUEST)

            # Create a new payment intent
            try:
                payment_intent_data = create_payment_intent(
                    order,
                    customer_email=request.user.email
                )

                # Create StripePayment record
                payment = StripePayment.objects.create(
                    order=order,
                    payment_intent_id=payment_intent_data['payment_intent_id'],
                    amount=order.total_price,
                    status='pending'
                )

                return Response({
                    'success': True,
                    'payment_intent_client_secret': payment_intent_data['client_secret'],
                    'payment_intent_id': payment_intent_data['payment_intent_id'],
                    'message': 'Payment intent created'
                })
            except Exception as e:
                return Response({
                    'error': str(e)
                }, status=status.HTTP_400_BAD_REQUEST)

        except Order.DoesNotExist:
            return Response({
                'error': 'Order not found'
            }, status=status.HTTP_404_NOT_FOUND)

class StripeWebhookView(APIView):
    """
    Handle Stripe webhook events.

    This view receives webhook events from Stripe and processes them accordingly.
    It handles payment intents, refunds, and other payment-related events.
    """
    permission_classes = [permissions.AllowAny]  # Stripe needs to access this endpoint without authentication

    @extend_schema(
        request=OpenApiTypes.OBJECT
    )

    def post(self, request):
        payload = request.body
        sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')

        if not sig_header:
            return Response({'error': 'Stripe signature header is missing'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            event = construct_event(payload, sig_header)
            if not event:
                return Response({'error': 'Invalid Stripe webhook signature'}, status=status.HTTP_400_BAD_REQUEST)

            # Handle the event based on its type
            if event.type == 'payment_intent.succeeded':
                self.handle_payment_intent_succeeded(event.data.object)
            elif event.type == 'payment_intent.payment_failed':
                self.handle_payment_intent_failed(event.data.object)
            elif event.type == 'charge.refunded':
                self.handle_charge_refunded(event.data.object)

            # Return a success response to Stripe
            return Response({'status': 'success'})

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
    def handle_payment_intent_succeeded(self, payment_intent):
        """Handle successful payment intent"""
        try:
            # Find the order associated with this payment intent
            order = Order.objects.get(stripe_payment_intent_id=payment_intent.id)

            # Update order status
            order.payment_status = 'paid'
            order.save()

            # Update or create StripePayment record
            payment, created = StripePayment.objects.update_or_create(
                order=order,
                payment_intent_id=payment_intent.id,
                defaults={
                    'amount': order.total_price,
                    'status': 'succeeded',
                    'payment_method_id': payment_intent.payment_method,
                    'metadata': payment_intent.metadata
                }
            )
            # Notify user about successful payment
            try:
                schedule(send_payment_status_notification, order.id, 'succeeded')
            except Exception:
                pass

            # Event trigger: rebuild recommendations after successful payment
            try:
                schedule_reco_rebuild(order.user.id, event='payment_succeeded')
            except Exception:
                pass
        except Order.DoesNotExist:
            # Log error - payment intent doesn't match any order
            print(f"Error: Payment intent {payment_intent.id} doesn't match any order")

    def handle_payment_intent_failed(self, payment_intent):
        """Handle failed payment intent"""
        try:
            # Find the order associated with this payment intent
            order = Order.objects.get(stripe_payment_intent_id=payment_intent.id)

            # Update order status
            order.payment_status = 'failed'
            order.save()

            # Update or create StripePayment record
            payment, created = StripePayment.objects.update_or_create(
                order=order,
                payment_intent_id=payment_intent.id,
                defaults={
                    'amount': order.total_price,
                    'status': 'failed',
                    'payment_method_id': payment_intent.payment_method,
                    'metadata': payment_intent.metadata
                }
            )

            # Notify user about failed payment
            try:
                schedule(send_payment_status_notification, order.id, 'failed')
            except Exception:
                pass

        except Order.DoesNotExist:
            # Log error - payment intent doesn't match any order
            print(f"Error: Payment intent {payment_intent.id} doesn't match any order")

    def handle_charge_refunded(self, charge):
        """Handle refunded charge"""
        try:
            # Find the payment intent associated with this charge
            payment_intent_id = charge.payment_intent

            # Find the order associated with this payment intent
            order = Order.objects.get(stripe_payment_intent_id=payment_intent_id)

            # Update order status
            order.payment_status = 'refunded'
            order.save()

            # Update or create StripePayment record
            payment, created = StripePayment.objects.update_or_create(
                order=order,
                payment_intent_id=payment_intent_id,
                defaults={
                    'amount': order.total_price,
                    'status': 'refunded',
                    'metadata': {'charge_id': charge.id}
                }
            )

            # Notify user about refunded payment
            try:
                schedule(send_payment_status_notification, order.id, 'refunded')
            except Exception:
                pass

        except Order.DoesNotExist:
            # Log error - payment intent doesn't match any order
            print(f"Error: Payment intent {payment_intent_id} doesn't match any order")


# Enhanced Stripe Payment Views
@extend_schema(parameters=[OpenApiParameter(name='id', type=OpenApiTypes.UUID, location=OpenApiParameter.PATH, required=True)])
class StripeCustomerViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Stripe customers.

    Provides CRUD operations for Stripe customers including:
    - Creating customers with automatic Stripe integration
    - Updating customer information
    - Retrieving customer details
    - Managing customer payment methods
    """
    serializer_class = StripeCustomerSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Filter customers to only show the current user's customer record."""
        return StripeCustomer.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        """Create a Stripe customer with automatic Stripe integration."""
        user = self.request.user

        # Check if customer already exists
        if StripeCustomer.objects.filter(user=user).exists():
            raise ValidationError("Customer already exists for this user")

        # Create Stripe customer
        stripe_customer = create_or_get_stripe_customer(
            user=user,
            email=serializer.validated_data.get('email', user.email),
            name=serializer.validated_data.get('name'),
            phone=serializer.validated_data.get('phone'),
            address=serializer.validated_data.get('address')
        )

        # The customer is already created by the utility function
        return stripe_customer

    @action(detail=True, methods=['get'])
    def payment_methods(self, request, pk=None):
        """Get all payment methods for a customer."""
        customer = self.get_object()
        payment_methods = get_customer_payment_methods(customer)
        serializer = StripePaymentMethodSerializer(payment_methods, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def attach_payment_method(self, request, pk=None):
        """Attach a payment method to the customer."""
        customer = self.get_object()
        payment_method_id = request.data.get('payment_method_id')
        set_as_default = request.data.get('set_as_default', False)

        if not payment_method_id:
            return Response(
                {'error': 'payment_method_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            payment_method = attach_payment_method(
                customer, payment_method_id, set_as_default
            )
            serializer = StripePaymentMethodSerializer(payment_method)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


@extend_schema(parameters=[OpenApiParameter(name='id', type=OpenApiTypes.UUID, location=OpenApiParameter.PATH, required=True)])
class StripePaymentMethodViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Stripe payment methods.

    Provides operations for:
    - Viewing payment methods
    - Setting default payment methods
    - Detaching payment methods
    """
    serializer_class = StripePaymentMethodSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Filter payment methods to only show the current user's methods."""
        try:
            customer = StripeCustomer.objects.get(user=self.request.user)
            return StripePaymentMethod.objects.filter(customer=customer, is_active=True)
        except StripeCustomer.DoesNotExist:
            return StripePaymentMethod.objects.none()

    @action(detail=True, methods=['post'])
    def set_default(self, request, pk=None):
        """Set a payment method as the default."""
        payment_method = self.get_object()

        try:
            success = set_default_payment_method(
                payment_method.customer, payment_method
            )
            if success:
                return Response({'message': 'Payment method set as default'})
            else:
                return Response(
                    {'error': 'Failed to set as default'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    def destroy(self, request, *args, **kwargs):
        """Detach a payment method."""
        payment_method = self.get_object()

        try:
            success = detach_payment_method(payment_method)
            if success:
                return Response(status=status.HTTP_204_NO_CONTENT)
            else:
                return Response(
                    {'error': 'Failed to detach payment method'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


@extend_schema(parameters=[OpenApiParameter(name='id', type=OpenApiTypes.UUID, location=OpenApiParameter.PATH, required=True)])
class StripeRefundViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Stripe refunds.

    Provides operations for:
    - Creating refunds
    - Viewing refund history
    - Tracking refund status
    """
    serializer_class = StripeRefundSerializer
    permission_classes = [permissions.IsAuthenticated, IsManagerOrReadOnly]

    def get_queryset(self):
        """Filter refunds based on user permissions."""
        if self.request.user.user_roles.filter(role__name='manager').exists():
            # Managers can see all refunds
            return StripeRefund.objects.all().order_by('-created_at')
        else:
            # Regular users can only see their own refunds
            return StripeRefund.objects.filter(
                payment__order__user=self.request.user
            ).order_by('-created_at')

    def perform_create(self, serializer):
        """Create a refund for a payment."""
        payment_id = serializer.validated_data['payment_id']
        amount = serializer.validated_data.get('amount')
        reason = serializer.validated_data.get('reason')

        try:
            payment = StripePayment.objects.get(id=payment_id)

            # Check if user has permission to refund this payment
            if not self.request.user.user_roles.filter(role__name='manager').exists():
                if payment.order.user != self.request.user:
                    raise PermissionDenied("You can only refund your own payments")

            # Create the refund
            refund = create_refund(
                stripe_payment=payment,
                amount=amount,
                reason=reason,
                created_by=self.request.user
            )

            return refund

        except StripePayment.DoesNotExist:
            raise ValidationError("Payment not found")
        except Exception as e:
            raise ValidationError(f"Failed to create refund: {str(e)}")


class EnhancedPaymentProcessingView(APIView):
    """
    Enhanced payment processing view with customer and payment method management.

    Supports:
    - Creating payment intents with saved customers
    - Using saved payment methods
    - Saving payment methods for future use
    """
    permission_classes = [permissions.IsAuthenticated]
    throttle_classes = [PaymentRateThrottle]

    @extend_schema(
        request=inline_serializer(name='EnhancedPaymentProcessingRequest', fields={
            'order_id': rf_serializers.IntegerField(),
            'payment_method_id': rf_serializers.CharField(required=False),
            'save_payment_method': rf_serializers.BooleanField(required=False),
            'use_saved_method': rf_serializers.BooleanField(required=False)
        }),
        responses={
            200: inline_serializer(name='EnhancedPaymentProcessingResponse', fields={
                'success': rf_serializers.BooleanField(),
                'payment_intent_client_secret': rf_serializers.CharField(required=False),
                'payment_intent_id': rf_serializers.CharField(required=False),
                'requires_action': rf_serializers.BooleanField(required=False),
                'status': rf_serializers.CharField(required=False),
                'message': rf_serializers.CharField(required=False),
                'error': rf_serializers.CharField(required=False)
            })
        }
    )

    def post(self, request):
        order_id = request.data.get('order_id')
        payment_method_id = request.data.get('payment_method_id')
        save_payment_method = request.data.get('save_payment_method', False)
        use_saved_method = request.data.get('use_saved_method', False)

        if not order_id:
            return Response({
                'error': 'Order ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            order = Order.objects.get(id=order_id, user=request.user)

            # Create or get Stripe customer
            stripe_customer = create_or_get_stripe_customer(
                user=request.user,
                email=request.user.email
            )

            # If using a saved payment method, get it from the database
            if use_saved_method and payment_method_id:
                try:
                    saved_method = StripePaymentMethod.objects.get(
                        stripe_payment_method_id=payment_method_id,
                        customer=stripe_customer,
                        is_active=True
                    )
                    payment_method_id = saved_method.stripe_payment_method_id
                except StripePaymentMethod.DoesNotExist:
                    return Response({
                        'error': 'Saved payment method not found'
                    }, status=status.HTTP_400_BAD_REQUEST)

            # Create payment intent with customer
            payment_intent_data = create_payment_intent_with_customer(
                order=order,
                stripe_customer=stripe_customer,
                payment_method_id=payment_method_id,
                save_payment_method=save_payment_method
            )

            # Create enhanced StripePayment record
            payment = StripePayment.objects.create(
                order=order,
                customer=stripe_customer,
                payment_intent_id=payment_intent_data['payment_intent_id'],
                stripe_payment_method_id=payment_method_id,
                amount=order.total_price,
                status='pending',
                receipt_email=request.user.email
            )

            return Response({
                'success': True,
                'payment_intent_client_secret': payment_intent_data['client_secret'],
                'payment_intent_id': payment_intent_data['payment_intent_id'],
                'requires_action': payment_intent_data.get('requires_action', False),
                'status': payment_intent_data.get('status', 'requires_payment_method'),
                'message': 'Payment intent created successfully'
            })

        except Order.DoesNotExist:
            return Response({
                'error': 'Order not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class PaymentAnalyticsView(APIView):
    """
    View for payment analytics and reporting.

    Provides comprehensive payment statistics including:
    - Revenue analytics
    - Payment success rates
    - Refund statistics
    - Time-based revenue reports
    """
    permission_classes = [permissions.IsAuthenticated, IsManager]

    def get(self, request):
        """Get payment analytics based on query parameters."""
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        period = request.query_params.get('period', 'monthly')

        try:
            # Parse dates if provided
            if start_date:
                from datetime import datetime
                start_date = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            if end_date:
                from datetime import datetime
                end_date = datetime.fromisoformat(end_date.replace('Z', '+00:00'))

            # Get payment statistics
            stats = get_payment_statistics(start_date, end_date)

            # Get revenue by period
            revenue_data = get_revenue_by_period(period, start_date, end_date)

            return Response({
                'statistics': stats,
                'revenue_by_period': list(revenue_data),
                'period': period
            })

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class PaymentHistoryView(APIView):

    """
    View for retrieving payment history.

    Provides filtered payment history for users and managers.
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """Get payment history for the current user or all payments for managers."""
        # Check if user is a manager
        is_manager = request.user.user_roles.filter(role__name='manager').exists()

        if is_manager:
            # Managers can see all payments
            queryset = StripePayment.objects.all()
        else:
            # Regular users can only see their own payments
            queryset = StripePayment.objects.filter(order__user=request.user)

        # Apply filters
        status_filter = request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        start_date = request.query_params.get('start_date')
        if start_date:
            from datetime import datetime
            start_date = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            queryset = queryset.filter(created_at__gte=start_date)

        end_date = request.query_params.get('end_date')
        if end_date:
            from datetime import datetime
            end_date = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            queryset = queryset.filter(created_at__lte=end_date)

        # Order by creation date (newest first)
        queryset = queryset.order_by('-created_at')

        # Paginate results
        from rest_framework.pagination import PageNumberPagination
        paginator = PageNumberPagination()
        paginator.page_size = 20
        page = paginator.paginate_queryset(queryset, request)

        if page is not None:
            serializer = StripePaymentSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)

        serializer = StripePaymentSerializer(queryset, many=True)
        return Response(serializer.data)


class PromoCodeView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        request=inline_serializer(name='PromoCodeApplyRequest', fields={
            'code': rf_serializers.CharField(),
            'cart_id': rf_serializers.IntegerField(required=False)
        }),
        responses={200: inline_serializer(name='PromoCodeApplyResponse', fields={
            'valid': rf_serializers.BooleanField(),
            'discount': rf_serializers.DecimalField(max_digits=10, decimal_places=2, required=False),
            'message': rf_serializers.CharField(required=False)
        })}
    )

    def post(self, request):
        code = request.data.get('code')
        cart_id = request.data.get('cart_id')

        if not code:
            return Response({
                'error': 'Promo code is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # In a real app, you would check the promo code against a database
        # For this example, we'll just hardcode some valid promo codes

        valid_promo_codes = {
            'WELCOME10': {'discount_type': 'percentage', 'value': 10},
            'SUMMER20': {'discount_type': 'percentage', 'value': 20},
            'FREESHIP': {'discount_type': 'shipping', 'value': 100},
            'FLAT25': {'discount_type': 'fixed', 'value': 25}
        }

        if code.upper() in valid_promo_codes:
            promo = valid_promo_codes[code.upper()]

            # If a cart ID was provided, apply the discount to the cart
            if cart_id:
                try:
                    cart = Cart.objects.get(id=cart_id, created_by=request.user)
                    # In a real app, you would store the applied promo code on the cart
                    # and calculate the discounted total

                    return Response({
                        'success': True,
                        'discount_type': promo['discount_type'],
                        'discount_value': promo['value'],
                        'message': f"Promo code '{code}' applied successfully"
                    })
                except Cart.DoesNotExist:
                    return Response({
                        'error': 'Cart not found'
                    }, status=status.HTTP_404_NOT_FOUND)
            else:
                return Response({
                    'success': True,
                    'discount_type': promo['discount_type'],
                    'discount_value': promo['value'],
                    'message': f"Promo code '{code}' is valid"
                })
        else:
            return Response({
                'error': 'Invalid promo code'
            }, status=status.HTTP_400_BAD_REQUEST)


# Inventory Management Views
class InventoryManagementViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing product inventory.

    This ViewSet provides CRUD operations for inventory management:
    - List all inventory changes with filtering and search
    - Create new inventory changes (add/subtract stock)
    - Retrieve details of specific inventory changes
    - Generate inventory reports and statistics

    Only managers and admins can access these endpoints.
    """
    queryset = InventoryChange.objects.all().order_by('-timestamp')
    serializer_class = InventoryChangeSerializer
    permission_classes = [IsManager]
    filter_backends = [filters.SearchFilter]
    search_fields = ['product__title', 'notes', 'change_type']

    def get_queryset(self):
        """
        Filter inventory changes based on query parameters.

        Supported filters:
        - product_id: Filter by specific product
        - change_type: Filter by type of inventory change (add, subtract, etc.)
        - start_date: Filter by changes after this date
        - end_date: Filter by changes before this date

        Returns:
            QuerySet of filtered InventoryChange objects
        """
        queryset = super().get_queryset()

        # Filter by product if product_id is provided
        product_id = self.request.query_params.get('product_id')
        if product_id:
            queryset = queryset.filter(product__id=product_id)

        # Filter by change type if provided
        change_type = self.request.query_params.get('change_type')
        if change_type:
            queryset = queryset.filter(change_type=change_type)

        # Filter by date range if provided
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date:
            queryset = queryset.filter(timestamp__gte=start_date)
        if end_date:
            queryset = queryset.filter(timestamp__lte=end_date)

        return queryset

    def perform_create(self, serializer):
        """
        Override create to handle inventory changes properly.

        This method processes inventory change requests by:
        1. Extracting data from the serializer
        2. Finding the product and related order (if any)
        3. Calling the appropriate inventory management method on the product
        4. Handling any validation errors that occur

        The method uses different product methods based on the change_type:
        - 'add': Increases inventory
        - 'subtract': Decreases inventory
        - 'order': Decreases inventory due to an order
        - 'return': Increases inventory due to a return
        - 'adjustment': Manual adjustment with custom reason

        Args:
            serializer: The validated serializer instance

        Returns:
            Response with success message or error details
        """
        # Extract data from serializer
        product_id = serializer.validated_data.get('product_id')
        quantity_change = serializer.validated_data.get('quantity_change')
        change_type = serializer.validated_data.get('change_type')
        notes = serializer.validated_data.get('notes')
        reference_order_id = serializer.validated_data.get('reference_order_id')

        # Get the product and order objects
        product = get_object_or_404(Product, id=product_id)
        reference_order = None

        if reference_order_id:
            reference_order = get_object_or_404(Order, id=reference_order_id)

        # Use the product's inventory management methods based on change type
        try:
            if change_type == 'add':
                product.add_inventory(quantity_change, self.request.user, notes)
            elif change_type == 'subtract':
                product.subtract_inventory(quantity_change, self.request.user, notes)
            elif change_type == 'order' and reference_order:
                product.order_inventory(quantity_change, self.request.user, reference_order, notes)
            elif change_type == 'return' and reference_order:
                product.return_inventory(quantity_change, self.request.user, reference_order, notes)
            elif change_type == 'adjustment':
                product.update_inventory(quantity_change, 'adjustment', self.request.user, notes=notes)
            else:
                return Response(
                    {'error': 'Invalid change type or missing required reference'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            return Response({'success': True}, status=status.HTTP_201_CREATED)
        except ValidationError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """
        Get inventory summary statistics.

        This endpoint provides a dashboard overview of inventory status:
        - Count of products with low stock (less than 10 items)
        - Total value of all inventory in stock
        - Recent inventory changes for activity monitoring

        Returns:
            Response with summary statistics in JSON format
        """
        # Get products with low stock (less than 10 items)
        low_stock = Product.objects.filter(stock_quantity__lt=10).count()

        # Get total inventory value by multiplying each product's price by its quantity
        total_value = Product.objects.annotate(
            value=F('stock_quantity') * F('price')
        ).aggregate(total=Sum('value'))

        # Get recent inventory changes for activity monitoring
        recent_changes = InventoryChange.objects.all().order_by('-timestamp')[:5]

        return Response({
            'low_stock_count': low_stock,
            'total_inventory_value': total_value['total'] or 0,
            'recent_changes': InventoryChangeSerializer(recent_changes, many=True).data
        })


# Supplier Management Views
class SupplierViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing suppliers.

    Provides CRUD operations for supplier management.
    Only managers and admins can access these endpoints.
    """
    queryset = Supplier.objects.all().order_by('name')
    serializer_class = SupplierSerializer
    permission_classes = [IsManager]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name', 'contact_person', 'email']

    def get_queryset(self):
        """Filter suppliers based on query parameters"""
        queryset = super().get_queryset()

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset


# Inventory Batch Management Views
class InventoryBatchViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing inventory batches.

    Provides CRUD operations for batch-level inventory management.
    Only managers and admins can access these endpoints.
    """
    queryset = InventoryBatch.objects.all().order_by('expiry_date', 'received_date')
    serializer_class = InventoryBatchSerializer
    permission_classes = [IsManager]
    filter_backends = [filters.SearchFilter]
    search_fields = ['product__title', 'batch_number', 'supplier__name']

    def get_queryset(self):
        """Filter inventory batches based on query parameters"""
        queryset = super().get_queryset()

        # Filter by product
        product_id = self.request.query_params.get('product_id')
        if product_id:
            queryset = queryset.filter(product__id=product_id)

        # Filter by supplier
        supplier_id = self.request.query_params.get('supplier_id')
        if supplier_id:
            queryset = queryset.filter(supplier__id=supplier_id)

        # Filter by expiry status
        expired = self.request.query_params.get('expired')
        if expired is not None:
            from django.utils import timezone
            if expired.lower() == 'true':
                queryset = queryset.filter(expiry_date__lt=timezone.now().date())
            else:
                queryset = queryset.filter(expiry_date__gte=timezone.now().date())

        # Filter by low stock
        low_stock = self.request.query_params.get('low_stock')
        if low_stock is not None and low_stock.lower() == 'true':
            threshold = int(self.request.query_params.get('threshold', 10))
            queryset = queryset.filter(quantity_remaining__lte=threshold)

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset

    @action(detail=False, methods=['get'])
    def expiring_soon(self, request):
        """Get batches expiring within specified days"""
        days = int(request.query_params.get('days', 30))
        from django.utils import timezone
        cutoff_date = timezone.now().date() + timezone.timedelta(days=days)

        batches = self.get_queryset().filter(
            expiry_date__lte=cutoff_date,
            expiry_date__gte=timezone.now().date(),
            quantity_remaining__gt=0,
            is_active=True
        )

        serializer = self.get_serializer(batches, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def expired(self, request):
        """Get all expired batches with remaining stock"""
        from django.utils import timezone
        batches = self.get_queryset().filter(
            expiry_date__lt=timezone.now().date(),
            quantity_remaining__gt=0,
            is_active=True
        )

        serializer = self.get_serializer(batches, many=True)
        return Response(serializer.data)


# Sales Report Management Views
class SalesReportViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing sales reports.

    Provides CRUD operations for sales reporting and analytics.
    Only managers and admins can access these endpoints.
    """
    queryset = SalesReport.objects.all().order_by('-report_date')
    serializer_class = SalesReportSerializer
    permission_classes = [IsManager]
    filter_backends = [filters.SearchFilter]
    search_fields = ['report_date']

    def get_queryset(self):
        """Filter sales reports based on query parameters"""
        queryset = super().get_queryset()

        # Filter by report type
        report_type = self.request.query_params.get('report_type')
        if report_type:
            queryset = queryset.filter(report_type=report_type)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')

        if start_date:
            queryset = queryset.filter(report_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(report_date__lte=end_date)

        return queryset

    @action(detail=False, methods=['post'])
    def generate_report(self, request):
        """Generate a new sales report for specified date and type"""
        from datetime import datetime

        report_date = request.data.get('report_date')
        report_type = request.data.get('report_type', 'daily')

        if not report_date:
            return Response({'error': 'report_date is required'}, status=400)

        try:
            # Parse the date
            if isinstance(report_date, str):
                report_date = datetime.strptime(report_date, '%Y-%m-%d').date()

            # Check if report already exists
            existing_report = SalesReport.objects.filter(
                report_date=report_date,
                report_type=report_type
            ).first()

            if existing_report:
                # Update existing report
                existing_report.calculate_metrics()
                serializer = self.get_serializer(existing_report)
                return Response({
                    'message': 'Report updated successfully',
                    'report': serializer.data
                })
            else:
                # Create new report
                report = SalesReport.objects.create(
                    report_date=report_date,
                    report_type=report_type
                )
                report.calculate_metrics()

                serializer = self.get_serializer(report)
                return Response({
                    'message': 'Report generated successfully',
                    'report': serializer.data
                }, status=201)

        except ValueError as e:
            return Response({'error': f'Invalid date format: {str(e)}'}, status=400)
        except Exception as e:
            return Response({'error': str(e)}, status=500)

    @action(detail=False, methods=['get'])
    def dashboard_summary(self, request):
        """Get sales dashboard summary with key metrics"""
        from django.db.models import Sum, Avg
        from django.utils import timezone
        from datetime import timedelta

        # Get recent reports for different periods
        today = timezone.now().date()

        # Today's sales
        today_report = SalesReport.objects.filter(
            report_date=today,
            report_type='daily'
        ).first()

        # This week's sales
        week_start = today - timedelta(days=today.weekday())
        week_reports = SalesReport.objects.filter(
            report_date__gte=week_start,
            report_date__lte=today,
            report_type='daily'
        )

        # This month's sales
        month_start = today.replace(day=1)
        month_reports = SalesReport.objects.filter(
            report_date__gte=month_start,
            report_date__lte=today,
            report_type='daily'
        )

        # Calculate aggregated metrics
        week_totals = week_reports.aggregate(
            total_revenue=Sum('total_revenue'),
            total_orders=Sum('total_orders'),
            total_units=Sum('total_units_sold'),
            avg_order_value=Avg('average_order_value')
        )

        month_totals = month_reports.aggregate(
            total_revenue=Sum('total_revenue'),
            total_orders=Sum('total_orders'),
            total_units=Sum('total_units_sold'),
            avg_order_value=Avg('average_order_value')
        )

        return Response({
            'today': {
                'revenue': today_report.total_revenue if today_report else 0,
                'orders': today_report.total_orders if today_report else 0,
                'units_sold': today_report.total_units_sold if today_report else 0,
                'avg_order_value': today_report.average_order_value if today_report else 0
            },
            'this_week': {
                'revenue': week_totals['total_revenue'] or 0,
                'orders': week_totals['total_orders'] or 0,
                'units_sold': week_totals['total_units'] or 0,
                'avg_order_value': week_totals['avg_order_value'] or 0
            },
            'this_month': {
                'revenue': month_totals['total_revenue'] or 0,
                'orders': month_totals['total_orders'] or 0,
                'units_sold': month_totals['total_units'] or 0,
                'avg_order_value': month_totals['avg_order_value'] or 0
            }
        })


# Inventory Alert Management Views
class InventoryAlertViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing inventory alerts and notifications.

    Provides CRUD operations for inventory alerts with filtering and actions.
    Only managers and admins can access these endpoints.
    """
    queryset = InventoryAlert.objects.all().order_by('-created_at')
    serializer_class = InventoryAlertSerializer
    permission_classes = [IsManager]
    filter_backends = [filters.SearchFilter]
    search_fields = ['title', 'message', 'product__title', 'batch__batch_number']

    def get_queryset(self):
        """Filter alerts based on query parameters"""
        queryset = super().get_queryset()

        # Filter by alert type
        alert_type = self.request.query_params.get('alert_type')
        if alert_type:
            queryset = queryset.filter(alert_type=alert_type)

        # Filter by priority
        priority = self.request.query_params.get('priority')
        if priority:
            queryset = queryset.filter(priority=priority)

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        # Filter by acknowledged status
        is_acknowledged = self.request.query_params.get('is_acknowledged')
        if is_acknowledged is not None:
            queryset = queryset.filter(is_acknowledged=is_acknowledged.lower() == 'true')

        # Filter by product
        product_id = self.request.query_params.get('product_id')
        if product_id:
            queryset = queryset.filter(product__id=product_id)

        # Filter by batch
        batch_id = self.request.query_params.get('batch_id')
        if batch_id:
            queryset = queryset.filter(batch__id=batch_id)

        return queryset

    @action(detail=False, methods=['get'])
    def active_alerts(self, request):
        """Get all active, unacknowledged alerts"""
        alerts = self.get_queryset().filter(
            is_active=True,
            is_acknowledged=False
        )

        serializer = self.get_serializer(alerts, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def critical_alerts(self, request):
        """Get all critical priority alerts"""
        alerts = self.get_queryset().filter(
            priority='critical',
            is_active=True,
            is_acknowledged=False
        )

        serializer = self.get_serializer(alerts, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def dashboard_summary(self, request):
        """Get alert dashboard summary with counts by type and priority"""
        queryset = self.get_queryset().filter(is_active=True)

        # Count by alert type
        alert_type_counts = {}
        for alert_type, _ in InventoryAlert.ALERT_TYPES:
            count = queryset.filter(alert_type=alert_type, is_acknowledged=False).count()
            alert_type_counts[alert_type] = count

        # Count by priority
        priority_counts = {}
        for priority, _ in InventoryAlert.PRIORITY_LEVELS:
            count = queryset.filter(priority=priority, is_acknowledged=False).count()
            priority_counts[priority] = count

        # Recent alerts (last 24 hours)
        from django.utils import timezone
        from datetime import timedelta
        recent_cutoff = timezone.now() - timedelta(hours=24)
        recent_alerts = queryset.filter(created_at__gte=recent_cutoff, is_acknowledged=False)

        return Response({
            'total_active_alerts': queryset.filter(is_acknowledged=False).count(),
            'total_acknowledged_alerts': queryset.filter(is_acknowledged=True).count(),
            'alert_type_counts': alert_type_counts,
            'priority_counts': priority_counts,
            'recent_alerts_count': recent_alerts.count(),
            'recent_alerts': InventoryAlertSerializer(recent_alerts[:10], many=True).data
        })

    @action(detail=True, methods=['post'])
    def acknowledge(self, request, pk=None):
        """Acknowledge an alert"""
        alert = self.get_object()

        if alert.is_acknowledged:
            return Response({'error': 'Alert is already acknowledged'}, status=400)

        alert.acknowledge(request.user)

        serializer = self.get_serializer(alert)
        return Response({
            'message': 'Alert acknowledged successfully',
            'alert': serializer.data
        })

    @action(detail=True, methods=['post'])
    def dismiss(self, request, pk=None):
        """Dismiss/deactivate an alert"""
        alert = self.get_object()

        if not alert.is_active:
            return Response({'error': 'Alert is already dismissed'}, status=400)

        alert.dismiss()

        serializer = self.get_serializer(alert)
        return Response({
            'message': 'Alert dismissed successfully',
            'alert': serializer.data
        })

    @action(detail=False, methods=['post'])
    def bulk_acknowledge(self, request):
        """Acknowledge multiple alerts"""
        alert_ids = request.data.get('alert_ids', [])

        if not alert_ids:
            return Response({'error': 'alert_ids is required'}, status=400)

        alerts = self.get_queryset().filter(
            id__in=alert_ids,
            is_active=True,
            is_acknowledged=False
        )

        count = 0
        for alert in alerts:
            alert.acknowledge(request.user)
            count += 1

        return Response({
            'message': f'{count} alerts acknowledged successfully',
            'acknowledged_count': count
        })

    @action(detail=False, methods=['post'])
    def bulk_dismiss(self, request):
        """Dismiss multiple alerts"""
        alert_ids = request.data.get('alert_ids', [])

        if not alert_ids:
            return Response({'error': 'alert_ids is required'}, status=400)

        alerts = self.get_queryset().filter(
            id__in=alert_ids,
            is_active=True
        )

        count = 0
        for alert in alerts:
            alert.dismiss()
            count += 1

        return Response({
            'message': f'{count} alerts dismissed successfully',
            'dismissed_count': count
        })


class PromocodeViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing promocodes.

    Provides CRUD operations for promocodes with proper permissions.
    """
    queryset = Promocode.objects.all()
    serializer_class = PromocodeSerializer
    permission_classes = [IsManagerOrReadOnly]

    def get_queryset(self):
        """Filter promocodes based on user permissions."""
        queryset = Promocode.objects.all()

        # Filter by active status if requested
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def validate_code(self, request, pk=None):
        """Validate a promocode for use."""
        promocode = self.get_object()

        # Check if promocode is active and within valid dates
        from django.utils import timezone
        now = timezone.now()

        if not promocode.is_active:
            return Response({'valid': False, 'message': 'Promocode is not active'})

        if promocode.valid_from and now < promocode.valid_from:
            return Response({'valid': False, 'message': 'Promocode is not yet valid'})

        if promocode.valid_until and now > promocode.valid_until:
            return Response({'valid': False, 'message': 'Promocode has expired'})

        if promocode.usage_limit and promocode.times_used >= promocode.usage_limit:
            return Response({'valid': False, 'message': 'Promocode usage limit reached'})

        return Response({'valid': True, 'promocode': PromocodeSerializer(promocode).data})


class PromoCodeUsageViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing promocode usage history.

    Provides read-only access to promocode usage records.
    """
    queryset = PromoCodeUsage.objects.all()
    serializer_class = PromoCodeUsageSerializer
    permission_classes = [IsManagerOrReadOnly]

    def get_queryset(self):
        """Filter usage records based on user permissions."""
        queryset = PromoCodeUsage.objects.all()

        # If not a manager, only show user's own usage
        if not self.request.user.groups.filter(name='manager').exists():
            queryset = queryset.filter(user=self.request.user)

        # Filter by promocode if requested
        promocode_id = self.request.query_params.get('promocode')
        if promocode_id:
            queryset = queryset.filter(promocode_id=promocode_id)

        return queryset.order_by('-used_at')
