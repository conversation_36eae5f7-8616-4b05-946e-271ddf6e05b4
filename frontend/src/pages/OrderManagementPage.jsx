import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import {
  ArrowPathIcon,
  CheckCircleIcon,
  TruckIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

// Import components
import Layout from '../components/Layout.jsx';

// Import services
import { orderService } from '../services/api';
import { useAuth } from '../contexts/AuthContext.jsx';

const OrderManagementPage = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { currentUser } = useAuth();

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await orderService.getOrders();
      setOrders(response.data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch orders');
      toast.error('Failed to fetch orders');
    } finally {
      setLoading(false);
    }
  };

  const updateOrderStatus = async (orderId, paymentStatus, deliveryStatus) => {
    try {
      setLoading(true);
      await orderService.updateOrderStatus(orderId, { payment_status: paymentStatus, delivery_status: deliveryStatus });
      toast.success('Order status updated successfully');
      fetchOrders();
    } catch (err) {
      setError('Failed to update order status');
      toast.error('Failed to update order status');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status, type) => {
    let color = '';
    let icon = null;

    if (type === 'payment') {
      switch (status) {
        case 'paid':
          color = 'bg-green-100 text-green-800';
          icon = <CheckCircleIcon className="mr-1 h-4 w-4" />;
          break;
        case 'pending':
          color = 'bg-yellow-100 text-yellow-800';
          icon = <ArrowPathIcon className="mr-1 h-4 w-4" />;
          break;
        case 'failed':
          color = 'bg-red-100 text-red-800';
          icon = <XCircleIcon className="mr-1 h-4 w-4" />;
          break;
        case 'refunded':
          color = 'bg-blue-100 text-blue-800';
          icon = <ArrowPathIcon className="mr-1 h-4 w-4" />;
          break;
        default:
          color = 'bg-gray-100 text-gray-800';
      }
    } else {
      switch (status) {
        case 'delivered':
          color = 'bg-green-100 text-green-800';
          icon = <CheckCircleIcon className="mr-1 h-4 w-4" />;
          break;
        case 'shipped':
          color = 'bg-blue-100 text-blue-800';
          icon = <TruckIcon className="mr-1 h-4 w-4" />;
          break;
        case 'processing':
          color = 'bg-yellow-100 text-yellow-800';
          icon = <ArrowPathIcon className="mr-1 h-4 w-4" />;
          break;
        case 'cancelled':
          color = 'bg-red-100 text-red-800';
          icon = <XCircleIcon className="mr-1 h-4 w-4" />;
          break;
        default:
          color = 'bg-gray-100 text-gray-800';
      }
    }

    return (
      <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${color}`}>
        {icon}
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  if (!currentUser || !currentUser.is_staff) {
    return (
      <Layout>
        <div className="mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">Access Denied</h1>
            <p className="mt-4 text-lg text-gray-500">You do not have permission to access this page.</p>
            <Link to="/" className="mt-8 inline-block rounded-md bg-primary-600 px-4 py-2 text-base font-medium text-white hover:bg-primary-700">
              Go back to Home
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="mb-8 sm:flex sm:items-center sm:justify-between">
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">Order Management</h1>
            <button
              onClick={fetchOrders}
              className="mt-3 inline-flex items-center rounded-md bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 sm:mt-0"
            >
              <ArrowPathIcon className="mr-2 -ml-1 h-5 w-5" />
              Refresh
            </button>
          </div>

          {loading ? (
            <div className="flex justify-center py-12">
              <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-primary-600"></div>
            </div>
          ) : error ? (
            <div className="rounded-md bg-red-50 p-4">
              <div className="flex">
                <XCircleIcon className="h-5 w-5 text-red-400" aria-hidden="true" />
                <p className="ml-3 text-sm text-red-700">{error}</p>
              </div>
            </div>
          ) : (
            <div className="mt-8 overflow-hidden rounded-lg bg-white shadow">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Order ID
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Customer
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Date
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Total
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Payment Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Delivery Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 bg-white">
                    {orders.length === 0 ? (
                      <tr>
                        <td colSpan="7" className="px-6 py-4 text-center text-sm text-gray-500">
                          No orders found
                        </td>
                      </tr>
                    ) : (
                      orders.map((order) => (
                        <tr key={order.id}>
                          <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                            {order.id.substring(0, 8)}...
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            {order.user.first_name} {order.user.last_name}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            {new Date(order.created_at).toLocaleDateString()}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            ${order.total_price.toFixed(2)}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            {getStatusBadge(order.payment_status, 'payment')}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            {getStatusBadge(order.delivery_status, 'delivery')}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            <div className="flex space-x-2">
                              <select
                                className="rounded-md border-gray-300 text-sm focus:border-primary-500 focus:ring-primary-500"
                                value={order.payment_status}
                                onChange={(e) => updateOrderStatus(order.id, e.target.value, order.delivery_status)}
                              >
                                <option value="pending">Pending</option>
                                <option value="paid">Paid</option>
                                <option value="failed">Failed</option>
                                <option value="refunded">Refunded</option>
                              </select>
                              <select
                                className="rounded-md border-gray-300 text-sm focus:border-primary-500 focus:ring-primary-500"
                                value={order.delivery_status}
                                onChange={(e) => updateOrderStatus(order.id, order.payment_status, e.target.value)}
                              >
                                <option value="processing">Processing</option>
                                <option value="shipped">Shipped</option>
                                <option value="delivered">Delivered</option>
                                <option value="cancelled">Cancelled</option>
                              </select>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          )}
      </div>
    </Layout>
  );
};

export default OrderManagementPage;
