import React, { useEffect, useMemo, useState } from 'react';
import Layout from '../components/Layout.jsx';
import toast from 'react-hot-toast';
import { inventoryService, fetchProducts } from '../services/api';

const InventoryAdjustmentsPage = () => {
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState([]);
  const [changes, setChanges] = useState([]);
  const [search, setSearch] = useState('');
  const [form, setForm] = useState({ product_id: '', change_type: 'add', quantity_change: 1, notes: '' });

  const filteredProducts = useMemo(() => {
    if (!search) return products;
    return products.filter((p) => (p.title || '').toLowerCase().includes(search.toLowerCase()));
  }, [products, search]);

  const load = async () => {
    setLoading(true);
    try {
      const [prodRes, chgRes] = await Promise.all([
        fetchProducts(),
        inventoryService.listChanges({ page_size: 20 })
      ]);
      setProducts(prodRes.data?.results || prodRes.data || []);
      setChanges(chgRes.data?.results || chgRes.data || []);
    } catch (e) {
      console.error(e);
      toast.error('Failed to load inventory data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => { load(); }, []);

  const submit = async (e) => {
    e.preventDefault();
    try {
      if (!form.product_id) return toast.error('Select a product');
      const payload = {
        product_id: form.product_id,
        change_type: form.change_type,
        quantity_change: Number(form.quantity_change),
        notes: form.notes || undefined,
      };
      await inventoryService.createChange(payload);
      toast.success('Inventory updated');
      setForm({ ...form, quantity_change: 1, notes: '' });
      await load();
    } catch (e) {
      console.error(e);
      toast.error(e?.response?.data?.error || 'Adjustment failed');
    }
  };

  return (
    <Layout>
      <div className="container mx-auto py-10 px-4 space-y-8">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Inventory Adjustments</h1>
        </div>

        <form onSubmit={submit} className="bg-white shadow rounded-lg border border-gray-100 p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm text-gray-600 mb-1">Search product</label>
              <input value={search} onChange={(e)=>setSearch(e.target.value)} className="w-full border rounded px-3 py-2" placeholder="Type to filter list..." />
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm text-gray-600 mb-1">Product</label>
              <select value={form.product_id} onChange={(e)=>setForm(f=>({...f, product_id: e.target.value}))} className="w-full border rounded px-3 py-2">
                <option value="">Select a product</option>
                {filteredProducts.map(p => (
                  <option key={p.id} value={p.id}>{p.title}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm text-gray-600 mb-1">Change type</label>
              <select value={form.change_type} onChange={(e)=>setForm(f=>({...f, change_type: e.target.value}))} className="w-full border rounded px-3 py-2">
                <option value="add">Add</option>
                <option value="subtract">Subtract</option>
                <option value="adjustment">Adjustment</option>
              </select>
            </div>
            <div>
              <label className="block text-sm text-gray-600 mb-1">Quantity</label>
              <input type="number" min="1" value={form.quantity_change} onChange={(e)=>setForm(f=>({...f, quantity_change: e.target.value}))} className="w-full border rounded px-3 py-2" />
            </div>
            <div className="md:col-span-3">
              <label className="block text-sm text-gray-600 mb-1">Notes (optional)</label>
              <input value={form.notes} onChange={(e)=>setForm(f=>({...f, notes: e.target.value}))} className="w-full border rounded px-3 py-2" placeholder="Reason, supplier, etc." />
            </div>
          </div>
          <div className="flex justify-end">
            <button disabled={loading} className="px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50">Submit</button>
          </div>
        </form>

        <div className="bg-white rounded-lg shadow border border-gray-100">
          <div className="px-6 py-4 border-b flex items-center justify-between">
            <h2 className="text-lg font-semibold">Recent Changes</h2>
            <button onClick={load} className="text-sm text-blue-600 hover:underline">Refresh</button>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="text-left text-sm text-gray-500">
                  <th className="px-6 py-3">Product</th>
                  <th className="px-6 py-3">Type</th>
                  <th className="px-6 py-3">Delta</th>
                  <th className="px-6 py-3">Prev → New</th>
                  <th className="px-6 py-3">When</th>
                </tr>
              </thead>
              <tbody>
                {changes.length ? changes.map((chg)=> (
                  <tr key={chg.id} className="border-t">
                    <td className="px-6 py-3">{chg.product?.title || chg.product_title || '—'}</td>
                    <td className="px-6 py-3">{chg.change_type}</td>
                    <td className="px-6 py-3">{chg.quantity_change}</td>
                    <td className="px-6 py-3">{chg.previous_quantity} → {chg.new_quantity}</td>
                    <td className="px-6 py-3">{new Date(chg.timestamp).toLocaleString()}</td>
                  </tr>
                )) : (
                  <tr><td className="px-6 py-6" colSpan={5}>No changes found</td></tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default InventoryAdjustmentsPage;

