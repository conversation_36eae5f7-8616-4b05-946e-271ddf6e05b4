/**
 * Centralized error handling utilities for the eCommerce application
 * 
 * This module provides consistent error handling patterns and user feedback
 * across the entire frontend application.
 */
import toast from 'react-hot-toast';

/**
 * Extract error message from various error response formats
 * @param {Error|Object} error - The error object from API call
 * @param {string} defaultMessage - Default message if no specific error found
 * @returns {string} User-friendly error message
 */
export const getErrorMessage = (error, defaultMessage = 'An unexpected error occurred') => {
  // Handle network errors
  if (!error.response) {
    return 'Network error. Please check your connection and try again.';
  }

  // Handle different response formats
  const { data, status } = error.response;

  // Handle specific HTTP status codes
  switch (status) {
    case 400:
      return data?.message || data?.error || 'Invalid request. Please check your input.';
    case 401:
      return 'Authentication required. Please log in.';
    case 403:
      return 'Access denied. You do not have permission to perform this action.';
    case 404:
      return 'The requested resource was not found.';
    case 409:
      return data?.message || data?.error || 'Conflict. The resource already exists.';
    case 422:
      // Handle validation errors
      if (data?.errors) {
        const validationErrors = Object.values(data.errors).flat();
        return validationErrors.join(', ');
      }
      return data?.message || data?.error || 'Validation failed. Please check your input.';
    case 429:
      return 'Too many requests. Please wait a moment and try again.';
    case 500:
      return 'Server error. Please try again later.';
    case 503:
      return 'Service temporarily unavailable. Please try again later.';
    default:
      return data?.message || data?.error || defaultMessage;
  }
};

/**
 * Handle API errors with consistent user feedback
 * @param {Error} error - The error object from API call
 * @param {Object} options - Configuration options
 * @param {string} options.defaultMessage - Default error message
 * @param {boolean} options.showToast - Whether to show toast notification (default: true)
 * @param {Function} options.onError - Custom error handler function
 * @returns {string} The error message
 */
export const handleApiError = (error, options = {}) => {
  const {
    defaultMessage = 'An unexpected error occurred',
    showToast = true,
    onError
  } = options;

  const errorMessage = getErrorMessage(error, defaultMessage);

  // Show toast notification if enabled
  if (showToast) {
    toast.error(errorMessage);
  }

  // Call custom error handler if provided
  if (onError && typeof onError === 'function') {
    onError(error, errorMessage);
  }

  // Log error for debugging
  console.error('API Error:', error);

  return errorMessage;
};

/**
 * Wrapper for async operations with standardized error handling
 * @param {Function} asyncOperation - The async function to execute
 * @param {Object} options - Configuration options
 * @param {string} options.loadingMessage - Message to show while loading
 * @param {string} options.successMessage - Message to show on success
 * @param {string} options.errorMessage - Default error message
 * @param {boolean} options.showSuccessToast - Whether to show success toast (default: false)
 * @param {boolean} options.showErrorToast - Whether to show error toast (default: true)
 * @returns {Promise<{success: boolean, data?: any, error?: string}>}
 */
export const withErrorHandling = async (asyncOperation, options = {}) => {
  const {
    loadingMessage,
    successMessage,
    errorMessage = 'Operation failed',
    showSuccessToast = false,
    showErrorToast = true
  } = options;

  try {
    // Show loading message if provided
    if (loadingMessage) {
      toast.loading(loadingMessage);
    }

    // Execute the async operation
    const result = await asyncOperation();

    // Dismiss loading toast
    if (loadingMessage) {
      toast.dismiss();
    }

    // Show success message if provided
    if (successMessage && showSuccessToast) {
      toast.success(successMessage);
    }

    return { success: true, data: result };
  } catch (error) {
    // Dismiss loading toast
    if (loadingMessage) {
      toast.dismiss();
    }

    // Handle the error
    const errorMsg = handleApiError(error, {
      defaultMessage: errorMessage,
      showToast: showErrorToast
    });

    return { success: false, error: errorMsg };
  }
};

/**
 * Validation error handler for form submissions
 * @param {Object} errors - Validation errors object
 * @param {Function} setFieldError - Function to set field-specific errors
 */
export const handleValidationErrors = (errors, setFieldError) => {
  if (errors && typeof errors === 'object') {
    Object.entries(errors).forEach(([field, messages]) => {
      const errorMessage = Array.isArray(messages) ? messages[0] : messages;
      if (setFieldError) {
        setFieldError(field, errorMessage);
      }
    });
  }
};

/**
 * Custom hook for managing error state
 * @returns {Object} Error state management utilities
 */
export const useErrorHandler = () => {
  const [error, setError] = React.useState(null);

  const handleError = (err, options = {}) => {
    const errorMessage = handleApiError(err, options);
    setError(errorMessage);
    return errorMessage;
  };

  const clearError = () => setError(null);

  return {
    error,
    setError,
    handleError,
    clearError
  };
};

export default {
  getErrorMessage,
  handleApiError,
  withErrorHandling,
  handleValidationErrors,
  useErrorHandler
};
