import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ShoppingCartIcon } from '@heroicons/react/24/outline';
import { useCart } from '../../contexts/CartContext.jsx';
import InteractiveCart from '../InteractiveCart/InteractiveCart.jsx';

const FloatingCartButton = () => {
  const [isCartOpen, setIsCartOpen] = useState(false);
  const { cartSummary, isEmpty, cart } = useCart();

  // Don't show the button if cart is empty (defensive: support both function and boolean)
  const empty = typeof isEmpty === 'function'
    ? isEmpty()
    : (cartSummary?.itemCount ?? (cart?.lines?.length || cart?.items?.length || 0)) === 0;
  if (empty) return null;

  return (
    <>
      {/* Floating Cart Button */}
      <motion.div
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0, opacity: 0 }}
        className="fixed bottom-6 right-6 z-40"
      >
        <motion.button
          onClick={() => setIsCartOpen(true)}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="relative bg-primary-600 text-white p-4 rounded-full shadow-lg hover:bg-primary-700 transition-colors"
        >
          <ShoppingCartIcon className="h-6 w-6" />
          
          {/* Item Count Badge */}
          <AnimatePresence>
            {cartSummary.itemCount > 0 && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0 }}
                className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-medium"
              >
                {cartSummary.itemCount > 99 ? '99+' : cartSummary.itemCount}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Price Badge */}
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded whitespace-nowrap"
          >
            ${cartSummary.totalPrice}
          </motion.div>
        </motion.button>
      </motion.div>

      {/* Interactive Cart Sidebar */}
      <InteractiveCart 
        isOpen={isCartOpen} 
        onClose={() => setIsCartOpen(false)} 
      />
    </>
  );
};

export default FloatingCartButton;
