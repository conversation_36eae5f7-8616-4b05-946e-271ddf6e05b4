/**
 * Analytics Testing Suite
 * 
 * This file contains functions to test the analytics implementation
 * including Google Analytics tracking and backend API endpoints.
 */

import { 
  trackEvent, 
  trackProductImpression, 
  trackProductView, 
  trackAddToCart, 
  trackCheckoutStart, 
  trackPurchase, 
  trackSearch 
} from './components/Analytics/GoogleAnalytics';

import {
  trackUserBehavior,
  getUserAnalytics,
  getGeneralAnalytics
} from './services/api';

/**
 * Test Google Analytics tracking functions
 */
export const testGoogleAnalytics = () => {
  console.log('🧪 Testing Google Analytics Implementation...');
  
  // Test basic event tracking
  console.log('📊 Testing basic event tracking...');
  trackEvent('test_event', {
    category: 'test',
    action: 'click',
    label: 'test_button'
  });

  // Test product impression tracking
  console.log('👁️ Testing product impression tracking...');
  const testProduct = {
    id: 'test-product-1',
    title: 'Test Product',
    name: 'Test Product',
    price: 29.99,
    category: 'Test Category',
    brand: 'Test Brand'
  };
  
  trackProductImpression(testProduct, 'test_list', 1);

  // Test product view tracking
  console.log('🔍 Testing product view tracking...');
  trackProductView(testProduct, 'test_source');

  // Test add to cart tracking
  console.log('🛒 Testing add to cart tracking...');
  trackAddToCart(testProduct, 2, 'test_page');

  // Test checkout start tracking
  console.log('💳 Testing checkout start tracking...');
  const testCartItems = [
    {
      product: testProduct,
      quantity: 2,
      price: 29.99
    }
  ];
  trackCheckoutStart(testCartItems, 59.98);

  // Test purchase tracking
  console.log('✅ Testing purchase tracking...');
  const testOrder = {
    id: 'test-order-123',
    total_price: 59.98,
    items: testCartItems,
    shipping_cost: 5.00,
    tax_amount: 4.80
  };
  trackPurchase(testOrder);

  // Test search tracking
  console.log('🔎 Testing search tracking...');
  trackSearch('test search query', 15);

  console.log('✅ Google Analytics tests completed!');
};

/**
 * Test backend analytics API endpoints
 */
export const testBackendAnalytics = async () => {
  console.log('🧪 Testing Backend Analytics API...');
  
  try {
    // Test user behavior tracking
    console.log('📝 Testing user behavior tracking...');
    await trackUserBehavior({
      userId: 'test-user-id',
      action: 'test_action',
      productId: 'test-product-id',
      metadata: {
        test: true,
        timestamp: new Date().toISOString()
      }
    });
    console.log('✅ User behavior tracking test passed');

    // Test user analytics retrieval
    console.log('📊 Testing user analytics retrieval...');
    try {
      const userAnalytics = await getUserAnalytics('test-user-id', '?range=week');
      console.log('✅ User analytics retrieval test passed', userAnalytics.data);
    } catch (error) {
      console.log('⚠️ User analytics test skipped (requires authentication)');
    }

    // Test general analytics retrieval
    console.log('📈 Testing general analytics retrieval...');
    try {
      const generalAnalytics = await getGeneralAnalytics('?range=week');
      console.log('✅ General analytics retrieval test passed', generalAnalytics.data);
    } catch (error) {
      console.log('⚠️ General analytics test skipped (requires admin access)');
    }

  } catch (error) {
    console.error('❌ Backend analytics test failed:', error);
  }

  console.log('✅ Backend Analytics API tests completed!');
};

/**
 * Test analytics context integration
 */
export const testAnalyticsContext = (analyticsContext) => {
  console.log('🧪 Testing Analytics Context Integration...');
  
  if (!analyticsContext) {
    console.error('❌ Analytics context not provided');
    return;
  }

  const {
    trackAction,
    trackProductImpressions,
    trackProductDetailView,
    trackAddToCartAction,
    trackCheckoutStartAction,
    trackPurchaseAction,
    trackSearchAction
  } = analyticsContext;

  // Test basic action tracking
  console.log('🎯 Testing basic action tracking...');
  if (trackAction) {
    trackAction('test_context_action', { test: true });
    console.log('✅ Basic action tracking test passed');
  }

  // Test product impressions tracking
  console.log('👁️ Testing product impressions tracking...');
  if (trackProductImpressions) {
    const testProducts = [
      { id: '1', title: 'Product 1', price: 10 },
      { id: '2', title: 'Product 2', price: 20 }
    ];
    trackProductImpressions(testProducts, 'test_list');
    console.log('✅ Product impressions tracking test passed');
  }

  // Test product detail view tracking
  console.log('🔍 Testing product detail view tracking...');
  if (trackProductDetailView) {
    trackProductDetailView({ id: '1', title: 'Test Product', price: 10 }, 'test');
    console.log('✅ Product detail view tracking test passed');
  }

  // Test add to cart action tracking
  console.log('🛒 Testing add to cart action tracking...');
  if (trackAddToCartAction) {
    trackAddToCartAction({ id: '1', title: 'Test Product', price: 10 }, 1, 'test');
    console.log('✅ Add to cart action tracking test passed');
  }

  // Test checkout start action tracking
  console.log('💳 Testing checkout start action tracking...');
  if (trackCheckoutStartAction) {
    trackCheckoutStartAction([{ product: { id: '1' }, quantity: 1, price: 10 }], 10);
    console.log('✅ Checkout start action tracking test passed');
  }

  // Test purchase action tracking
  console.log('✅ Testing purchase action tracking...');
  if (trackPurchaseAction) {
    trackPurchaseAction({ id: 'test-order', total_price: 10, items: [] });
    console.log('✅ Purchase action tracking test passed');
  }

  // Test search action tracking
  console.log('🔎 Testing search action tracking...');
  if (trackSearchAction) {
    trackSearchAction('test query', 5);
    console.log('✅ Search action tracking test passed');
  }

  console.log('✅ Analytics Context integration tests completed!');
};

/**
 * Test Google Analytics configuration
 */
export const testGoogleAnalyticsConfig = () => {
  console.log('🧪 Testing Google Analytics Configuration...');
  
  // Check if Google Analytics is loaded
  if (typeof window !== 'undefined' && window.gtag) {
    console.log('✅ Google Analytics (gtag) is loaded');
    
    // Check if dataLayer exists
    if (window.dataLayer) {
      console.log('✅ Google Analytics dataLayer is available');
      console.log('📊 DataLayer entries:', window.dataLayer.length);
    } else {
      console.warn('⚠️ Google Analytics dataLayer not found');
    }
    
    // Test basic gtag functionality
    try {
      window.gtag('event', 'test_config_event', {
        event_category: 'test',
        event_label: 'configuration_test'
      });
      console.log('✅ Google Analytics gtag function is working');
    } catch (error) {
      console.error('❌ Google Analytics gtag function error:', error);
    }
    
  } else {
    console.warn('⚠️ Google Analytics (gtag) not loaded');
  }
  
  // Check environment configuration
  const gaTrackingId = process.env.REACT_APP_GA_TRACKING_ID;
  if (gaTrackingId && gaTrackingId !== 'G-XXXXXXXXXX') {
    console.log('✅ Google Analytics Tracking ID is configured');
  } else {
    console.warn('⚠️ Google Analytics Tracking ID not properly configured');
  }
  
  console.log('✅ Google Analytics configuration tests completed!');
};

/**
 * Run all analytics tests
 */
export const runAllAnalyticsTests = async (analyticsContext = null) => {
  console.log('🚀 Starting Complete Analytics Test Suite...');
  console.log('================================================');
  
  // Test Google Analytics configuration
  testGoogleAnalyticsConfig();
  console.log('');
  
  // Test Google Analytics tracking
  testGoogleAnalytics();
  console.log('');
  
  // Test backend analytics
  await testBackendAnalytics();
  console.log('');
  
  // Test analytics context if provided
  if (analyticsContext) {
    testAnalyticsContext(analyticsContext);
    console.log('');
  }
  
  console.log('================================================');
  console.log('🎉 Analytics Test Suite Completed!');
  console.log('Check the console output above for test results.');
  console.log('Also check your Google Analytics dashboard for tracked events.');
};

// Export individual test functions
const analyticsTests = {
  testGoogleAnalytics,
  testBackendAnalytics,
  testAnalyticsContext,
  testGoogleAnalyticsConfig,
  runAllAnalyticsTests
};
export default analyticsTests;
