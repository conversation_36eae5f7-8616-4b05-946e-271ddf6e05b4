# eCommerce Platform Cleanup and Implementation Plan

## Issues Identified

### Frontend Issues
1. **Duplicate AuthProvider import** in App.jsx (line 15-16)
2. **Unused Saleor integration** - The app has Saleor GraphQL setup but uses Django REST API
3. **Unused imports** throughout components (identified in ESLint warnings)
4. **Missing CartProvider** in App.jsx routing structure
5. **Redundant services** - Multiple cart services (Saleor + Django)
6. **Missing features** - Some components are incomplete
7. **Static HTML files** in public directory that are not needed

### Backend Issues
1. **Complete API implementation** - Some endpoints may need enhancement
2. **Missing Stripe webhook handling** - Needs proper implementation
3. **Typesense integration** - May need configuration
4. **Missing inventory management features**
5. **Promocode system** - Needs full implementation

## Cleanup Tasks

### Frontend Cleanup
1. Remove Saleor-related code and dependencies
2. Fix duplicate imports in App.jsx
3. Remove unused imports from all components
4. Remove static HTML preview files
5. Clean up unused services
6. Fix CartProvider integration
7. Remove redundant components

### Backend Cleanup
1. Ensure all API endpoints are properly implemented
2. Complete Stripe integration
3. Implement missing inventory management features
4. Complete promocode system
5. Add proper error handling

## Implementation Tasks

### Missing Features to Implement
1. **Inventory Management System**
   - Stock tracking
   - Low stock alerts
   - Inventory adjustments

2. **Promocode System**
   - Code validation
   - Usage tracking
   - Discount application

3. **Search Functionality**
   - Typesense integration
   - Advanced filtering

4. **Payment Processing**
   - Complete Stripe integration
   - Webhook handling

5. **User Management**
   - Role-based permissions
   - Manager dashboard

## Priority Order
1. Frontend cleanup (remove Saleor, fix imports)
2. Backend API completion
3. Inventory management
4. Promocode system
5. Search functionality
6. Payment processing enhancements
