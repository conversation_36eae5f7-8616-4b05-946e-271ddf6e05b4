# eCommerce Analytics Implementation

This document describes the comprehensive analytics implementation for the eCommerce platform, including Google Analytics integration and custom backend tracking.

## Features Implemented

### 🎯 Google Analytics Integration

#### Individual User Tracking
- **User ID Tracking**: Each logged-in user is tracked with their unique ID
- **Enhanced eCommerce Events**: Complete funnel tracking from impression to purchase
- **Custom Parameters**: Additional metadata for better insights
- **Session Management**: Automatic session ID generation and tracking

#### Tracked Events
1. **Product Impressions**: When products are displayed in lists
2. **Product Detail Views**: Individual product page visits
3. **Add to Cart**: Product additions to shopping cart
4. **Checkout Start**: Beginning of checkout process
5. **Purchase Completion**: Successful order completion
6. **Search Events**: Search queries and result counts
7. **Page Views**: Enhanced page view tracking with user context

### 📊 Backend Analytics System

#### User Behavior Tracking
- **Comprehensive Action Logging**: All user interactions stored in database
- **Metadata Storage**: Rich context data for each action
- **IP and User Agent Tracking**: Technical details for analysis
- **Session Correlation**: Link actions to user sessions

#### Analytics API Endpoints
- **General Analytics** (`/api/analytics/general/`): Site-wide metrics for managers
- **User Analytics** (`/api/analytics/user/{id}/`): Individual user behavior analysis
- **Behavior Tracking** (`/api/track/`): Real-time action logging

### 🎛️ Management Dashboard

#### Overview Metrics
- **Unique Users**: Count of distinct visitors
- **Page Views**: Total page view count
- **Product Views**: Product detail page visits
- **Cart Additions**: Add-to-cart event count
- **Purchases**: Completed transaction count
- **Conversion Rate**: Purchase/visitor ratio

#### Top Products Analysis
- **Most Viewed Products**: Products with highest view counts
- **Most Added to Cart**: Products frequently added to cart
- **Most Purchased**: Best-selling products

#### Geographic Analytics
- **Users by Region**: Geographic distribution of users
- **IP-based Location**: Automatic location detection

#### Real-time Features
- **Live Updates**: Auto-refresh every 30 seconds
- **Last Updated Timestamp**: Data freshness indicator
- **Manual Refresh**: On-demand data updates

### 👤 Individual User Analytics

#### Personal Activity Tracking
- **Activity Summary**: Personal metrics overview
- **Most Viewed Products**: User's product preferences
- **Recent Activities**: Chronological action history
- **Daily Breakdown**: Activity patterns over time

#### Privacy & Permissions
- **Self-Access**: Users can view their own analytics
- **Manager Access**: Staff can view any user's analytics
- **Permission Checks**: Secure access control

## Technical Implementation

### Frontend Components

#### Analytics Context (`AnalyticsContext.jsx`)
```javascript
// Provides analytics functions throughout the app
const { 
  trackProductImpressions,
  trackProductDetailView,
  trackAddToCartAction,
  trackCheckoutStartAction,
  trackPurchaseAction,
  trackSearchAction 
} = useAnalytics();
```

#### Google Analytics Integration (`GoogleAnalytics.jsx`)
- Enhanced eCommerce tracking
- User ID configuration
- Custom event parameters
- Session management

#### Analytics Dashboard (`AnalyticsDashboardPage.jsx`)
- Management overview
- Real-time updates
- Time range filtering
- Visual metrics display

#### User Analytics Page (`UserAnalyticsPage.jsx`)
- Individual user insights
- Personal activity history
- Behavior patterns
- Privacy-compliant access

### Backend Models

#### UserBehavior Model
```python
class UserBehavior(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, null=True)
    metadata = models.JSONField(default=dict)
    action_time = models.DateTimeField(auto_now_add=True)
    session_id = models.CharField(max_length=100)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    referrer = models.URLField()
    page = models.CharField(max_length=255)
```

### API Endpoints

#### Analytics Endpoints
- `GET /api/analytics/general/` - General site analytics (admin/manager only)
- `GET /api/analytics/user/{id}/` - User-specific analytics
- `POST /api/track/` - Track user behavior

#### Query Parameters
- `range`: Time range (day, week, month, year)
- Example: `/api/analytics/general/?range=week`

## Configuration

### Environment Variables
```bash
# Google Analytics
REACT_APP_GA_TRACKING_ID=G-XXXXXXXXXX

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:8000/api
```

### Google Analytics Setup
1. Create Google Analytics 4 property
2. Get Measurement ID (G-XXXXXXXXXX)
3. Update environment variable
4. Enable Enhanced eCommerce in GA4

## Usage Examples

### Track Product Impression
```javascript
// Automatically tracked when ProductCard is viewed
<ProductCard 
  product={product} 
  listName="search_results" 
  position={index} 
/>
```

### Track Add to Cart
```javascript
const { trackAddToCartAction } = useAnalytics();

const handleAddToCart = () => {
  trackAddToCartAction(product, quantity, 'product_page');
};
```

### Track Purchase
```javascript
const { trackPurchaseAction } = useAnalytics();

const handleOrderComplete = (orderData) => {
  trackPurchaseAction(orderData);
};
```

## Testing

### Analytics Test Panel
- Available in analytics dashboard for staff users
- Tests all tracking functions
- Validates Google Analytics integration
- Checks backend API connectivity

### Test Functions
```javascript
import { runAllAnalyticsTests } from './test-analytics';

// Run comprehensive test suite
await runAllAnalyticsTests(analyticsContext);
```

## Data Privacy

### Compliance Features
- **User Consent**: Respect user privacy preferences
- **Data Minimization**: Only collect necessary data
- **Access Control**: Secure analytics access
- **Anonymization**: Option to anonymize user data

### GDPR Considerations
- Users can request their analytics data
- Data retention policies configurable
- Right to be forgotten support
- Transparent data collection

## Performance

### Optimization Features
- **Async Tracking**: Non-blocking analytics calls
- **Error Handling**: Graceful failure handling
- **Batch Processing**: Efficient data collection
- **Caching**: Optimized API responses

### Monitoring
- **Error Logging**: Track analytics failures
- **Performance Metrics**: Monitor tracking performance
- **Data Quality**: Validate tracking accuracy

## Future Enhancements

### Planned Features
- **A/B Testing**: Experiment tracking
- **Cohort Analysis**: User group analysis
- **Funnel Analysis**: Conversion path tracking
- **Custom Dashboards**: Personalized analytics views
- **Export Functionality**: Data export capabilities
- **Advanced Filtering**: Complex query support

### Integration Opportunities
- **Email Marketing**: Behavior-based campaigns
- **Recommendation Engine**: Analytics-driven suggestions
- **Inventory Management**: Demand forecasting
- **Customer Support**: Behavior-informed support

## Support

### Troubleshooting
1. Check browser console for tracking errors
2. Verify Google Analytics configuration
3. Test API endpoints with authentication
4. Use analytics test panel for validation

### Common Issues
- **Missing Events**: Check user authentication
- **Permission Errors**: Verify user roles
- **Data Delays**: Allow time for processing
- **Configuration Issues**: Validate environment variables

For technical support, check the analytics test panel and browser console for detailed error information.
