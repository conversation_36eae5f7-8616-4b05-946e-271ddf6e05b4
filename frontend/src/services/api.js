import axios from 'axios';

// Base API URL, can be set from environment variables
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api';

// Axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle 401s by refreshing access token (with rotation)
let isRefreshing = false;
let refreshSubscribers = [];

function subscribeTokenRefresh(cb) {
  refreshSubscribers.push(cb);
}
function onRefreshed(newToken) {
  refreshSubscribers.forEach((cb) => cb(newToken));
  refreshSubscribers = [];
}

api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config || {};
    const status = error?.response?.status;

    // Network or CORS errors
    if (!error.response) {
      return Promise.reject(error);
    }

    // Avoid infinite loop on refresh endpoint itself
    if (originalRequest.url && originalRequest.url.includes('/auth/refresh/')) {
      return Promise.reject(error);
    }

    if (status === 401 && !originalRequest._retry) {
      const storedRefresh = localStorage.getItem('refreshToken');
      if (!storedRefresh) {
        // No refresh token -> logout
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        return Promise.reject(error);
      }

      originalRequest._retry = true;

      if (isRefreshing) {
        // Queue the request until refresh is done
        return new Promise((resolve) => {
          subscribeTokenRefresh((newToken) => {
            originalRequest.headers = originalRequest.headers || {};
            originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
            resolve(api(originalRequest));
          });
        });
      }

      isRefreshing = true;
      try {
        const { data } = await api.post('/auth/refresh/', { refresh: storedRefresh });
        const newAccess = data?.access;
        const newRefresh = data?.refresh; // present when rotation is enabled

        if (newAccess) {
          localStorage.setItem('token', newAccess);
          api.defaults.headers.common['Authorization'] = `Bearer ${newAccess}`;
        }
        if (newRefresh) {
          localStorage.setItem('refreshToken', newRefresh);
        }

        isRefreshing = false;
        onRefreshed(newAccess);

        // Retry the original request with new token
        originalRequest.headers = originalRequest.headers || {};
        originalRequest.headers['Authorization'] = `Bearer ${newAccess}`;
        return api(originalRequest);
      } catch (refreshErr) {
        isRefreshing = false;
        refreshSubscribers = [];
        // Clear tokens and redirect to login
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        if (typeof window !== 'undefined') {
          try { window.dispatchEvent(new Event('auth:logout')); } catch (_) {}
          window.location.assign('/login');
        }
        return Promise.reject(refreshErr);
      }
    }

    return Promise.reject(error);
  }
);


// Auth Service
export const authService = {
  login: (credentials) => api.post('/auth/login/', credentials),
  register: (userData) => api.post('/auth/register/', userData),
  logout: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    return Promise.resolve();
  },
  getCurrentUser: () => api.get('/users/me/'),
  updateProfile: (userData) => api.put('/users/me/', userData),
  refreshToken: (refreshToken) => api.post('/auth/refresh/', { refresh: refreshToken }),
  forgotPassword: (email) => api.post('/auth/forgot-password/', { email }),
  resetPassword: (token, newPassword) => api.post('/auth/reset-password/', { token, new_password: newPassword }),
  googleLogin: (idToken) => api.post('/auth/google/', { id_token: idToken }),
};


// Backward-compatible named exports for auth helpers
export const resetPassword = (token, newPassword) => authService.resetPassword(token, newPassword);
export const forgotPassword = (email) => authService.forgotPassword(email);

// Order Service
export const orderService = {
  getOrders: () => api.get('/admin/orders/'),
  updateOrderStatus: (orderId, status) => api.put(`/orders/${orderId}/`, status),
  getUserOrders: (userId) => api.get(`/orders/?user=${userId}`),
  getOrderDetails: (orderId) => api.get(`/orders/${orderId}/`),
  processRefund: (orderId) => api.post(`/orders/${orderId}/refund/`),
  placeOrder: (orderData) => api.post('/orders/', orderData),
};

/** Products */
export const fetchFeaturedProducts = async () => {
  // Use the home endpoint which returns featured products
  const response = await api.get('/home/');
  return { data: { results: response.data.featured_products } };
};
export const fetchCategories = async () => api.get('/categories/');
export const fetchOffers = async () => api.get('/offers/');
export const fetchRecommendations = async (userId) => api.get('/recommendations/');
export const fetchProducts = async () => api.get('/products/');
export const fetchFilteredProducts = async (filters) => api.get(`/products/?${filters}`);
export const fetchSortedProducts = async (sortBy) => api.get(`/products/?ordering=${sortBy}`);
export const fetchProductDetails = async (productId) => api.get(`/products/${productId}/`);
export const fetchProductReviews = async (productId) => api.get(`/reviews/?product=${productId}`);
export const submitReview = async (review) => api.post('/reviews/', review);
export const fetchRelatedProducts = async (productId) => {
  // Since the related products endpoint is not working, we'll fetch all products
  // and filter them by category (simulating related products)
  const response = await api.get('/products/');
  return response;
};
export const fetchUserBehavior = async (userId) => api.get(`/analytics/user/${userId}/`);
export const getUserAnalytics = async (userId, queryParams = '') => api.get(`/analytics/user/${userId}/${queryParams}`);
export const getGeneralAnalytics = async (queryParams = '') => api.get(`/analytics/general/${queryParams}`);
export const trackUserAction = async (userId, action, productId = null, metadata = {}) => api.post('/track/', {
  user_id: userId,
  action,
  product_id: productId,
  metadata
});
export const trackUserBehavior = async (data) => api.post('/track/', {
  user_id: data.userId,
  action: data.action,
  product_id: data.productId,
  metadata: data.metadata
});

/** Cart */
export const fetchCartItems = async () => api.get('/carts/');

export const fetchProductOffers = async (productId) => api.get(`/offers/?product=${productId}`);

/** Orders and Payments */
export const placeOrder = async (orderData) => api.post('/orders/', orderData);
export const processPayment = async (paymentData) => api.post('/payment/', paymentData);
export const fetchShippingOptions = async () => api.get('/shipping_options/');
export const applyPromoCode = async (promoCode, cartId) => api.post('/apply_promo/', { code: promoCode, cart_id: cartId });

/** User Profile */
export const fetchUserProfile = async (userId) => api.get(`/users/${userId}/`);
export const updateUserProfile = async (userId, userProfile) => api.put(`/users/${userId}/`, userProfile);


/** Admin */
export const fetchAdminProducts = async () => api.get('/products/');
export const fetchAdminOrders = async () => api.get('/admin/orders/');
export const fetchAdminUsers = async () => api.get('/users/');
export const addAdminProduct = async (product) => api.post('/products/', product);
export const updateAdminProduct = async (productId, product) => api.put(`/products/${productId}/`, product);
export const deleteAdminProduct = async (productId) => api.delete(`/products/${productId}/`);
export const fetchModerationQueue = async () => api.get('/moderation/');
export const assignAdminRole = async (userId, role) => api.post('/roles/', { user: userId, role });

/** Admin Orders */


/** Authentication */


/** Admin Moderation */
export const takeModerationAction = async (action) => api.post('/moderation/moderate/', action);

/** Cart Service */
export const cartService = {
  getCart: () => fetchCartItems(),
  createCart: () => api.post('/carts/'),
  addToCart: (cartId, productId, quantity = 1) => api.post(`/carts/${cartId}/add_item/`, { product_id: productId, quantity }),
  removeFromCart: (cartId, itemId) => api.post(`/carts/${cartId}/remove_item/`, { item_id: itemId }),
  updateCartItem: (cartId, itemId, quantity) => api.post(`/carts/${cartId}/update_item/`, { item_id: itemId, quantity }),
  checkout: (cartId) => api.post(`/carts/${cartId}/checkout/`),
  clearCart: async () => {
    const { data } = await fetchCartItems();
    const carts = Array.isArray(data) ? data : data?.results || [];
    const activeCart = carts[0];
    if (!activeCart?.items?.length) return [];
    const ops = activeCart.items.map((it) => cartService.removeFromCart(activeCart.id, it.id));
    return Promise.all(ops);
  },
  applyPromoCode: (promoCode, cartId) => applyPromoCode(promoCode, cartId)
};



/** Inventory Management */
export const inventoryService = {
  // Inventory summary dashboard
  getSummary: () => api.get('/inventory/summary/'),
  // Inventory changes
  listChanges: (params = {}) => api.get('/inventory/', { params }),
  createChange: (data) => api.post('/inventory/', data),
  // Inventory alerts
  listAlerts: (params = {}) => api.get('/inventory-alerts/', { params }),
  listActiveAlerts: () => api.get('/inventory-alerts/active_alerts/'),
  getAlertDashboardSummary: () => api.get('/inventory-alerts/dashboard_summary/'),
  acknowledgeAlert: (id) => api.post(`/inventory-alerts/${id}/acknowledge/`),
  dismissAlert: (id) => api.post(`/inventory-alerts/${id}/dismiss/`),
  bulkAcknowledge: (ids) => api.post('/inventory-alerts/bulk_acknowledge/', { alert_ids: ids }),
  bulkDismiss: (ids) => api.post('/inventory-alerts/bulk_dismiss/', { alert_ids: ids }),
  // Batches
  listBatches: (params = {}) => api.get('/inventory-batches/', { params }),
  getBatchesExpiringSoon: (days = 30) => api.get('/inventory-batches/expiring_soon/', { params: { days } }),
  getBatchesExpired: () => api.get('/inventory-batches/expired/'),
  // Suppliers
  listSuppliers: (params = {}) => api.get('/suppliers/', { params }),
  createSupplier: (data) => api.post('/suppliers/', data),
  updateSupplier: (id, data) => api.put(`/suppliers/${id}/`, data),
  deleteSupplier: (id) => api.delete(`/suppliers/${id}/`),
};



/** Sales Management */
export const salesService = {
  getDashboardSummary: () => api.get('/sales-reports/dashboard_summary/'),
  listReports: (params = {}) => api.get('/sales-reports/', { params }),
  generateReport: (data) => api.post('/sales-reports/generate_report/', data),
  getRevenueAnalytics: (period = 'monthly', start_date, end_date) => api.get('/payments/analytics/', {
    params: { period, start_date, end_date }
  }),
};

/** Analytics (general) */
export const analyticsService = {
  getGeneral: (params = {}) => api.get('/analytics/general/', { params }),
};

// Aggregated service objects for clearer imports
export const productService = {
  fetchFeaturedProducts,
  fetchCategories,
  fetchOffers,
  fetchRecommendations,
  fetchProducts,
  fetchFilteredProducts,
  fetchSortedProducts,
  fetchProductDetails,
  fetchProductReviews,
  submitReview,
  fetchRelatedProducts,
  fetchProductOffers,
};

export const userService = {
  fetchUserProfile,
  updateUserProfile,
  fetchUserBehavior,
  getUserAnalytics,
  trackUserAction,
  trackUserBehavior,
  getCurrentUser: authService.getCurrentUser,
};

export const adminService = {
  fetchAdminProducts,
  fetchAdminOrders,
  fetchAdminUsers,
  addAdminProduct,
  updateAdminProduct,
  deleteAdminProduct,
  fetchModerationQueue,
  takeModerationAction,
  assignAdminRole,
};

export const paymentService = {
  processPayment,
  fetchShippingOptions,
};

