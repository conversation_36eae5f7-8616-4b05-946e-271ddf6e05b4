import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CheckIcon, 
  ChevronRightIcon, 
  ChevronLeftIcon,
  ExclamationTriangleIcon 
} from '@heroicons/react/24/outline';
import { useCart } from '../../contexts/CartContext.jsx';
import toast from 'react-hot-toast';

// Step components
import ShippingStep from './steps/ShippingStep.jsx';
import BillingStep from './steps/BillingStep.jsx';
import ShippingMethodStep from './steps/ShippingMethodStep.jsx';
import PaymentStep from './steps/PaymentStep.jsx';
import ReviewStep from './steps/ReviewStep.jsx';

const CHECKOUT_STEPS = [
  { id: 'shipping', title: 'Shipping Address', component: ShippingStep },
  { id: 'billing', title: 'Billing Address', component: BillingStep },
  { id: 'shipping-method', title: 'Shipping Method', component: ShippingMethodStep },
  { id: 'payment', title: 'Payment', component: PaymentStep },
  { id: 'review', title: 'Review Order', component: ReviewStep }
];

const InteractiveCheckout = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [stepData, setStepData] = useState({});
  const [stepValidation, setStepValidation] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [checkout, setCheckout] = useState(null);
  
  const { cart, isEmpty } = useCart();

  const computeEmpty = () => (
    typeof isEmpty === 'function'
      ? isEmpty()
      : (cart?.lines?.length || cart?.items?.length || 0) === 0
  );

  useEffect(() => {
    if (!computeEmpty()) {
      loadCheckout();
    }
    // Depend on cart and isEmpty reference (if function) to retrigger safely
  }, [cart, isEmpty]);

  const loadCheckout = async () => {
    try {
      setIsLoading(true);
      // TODO: Implement checkout loading with Django API
      console.log('Loading checkout...');
    } catch (error) {
      console.error('Error loading checkout:', error);
      toast.error('Failed to load checkout');
    } finally {
      setIsLoading(false);
    }
  };

  const validateCurrentStep = async () => {
    const step = CHECKOUT_STEPS[currentStep];
    try {
      // TODO: Implement step validation with Django API
      const validation = { isValid: true, errors: [] };
      setStepValidation(prev => ({
        ...prev,
        [step.id]: validation
      }));
      return validation.isValid;
    } catch (error) {
      console.error('Validation error:', error);
      return false;
    }
  };

  const handleStepData = (stepId, data) => {
    setStepData(prev => ({
      ...prev,
      [stepId]: data
    }));
  };

  const nextStep = async () => {
    const isValid = await validateCurrentStep();
    if (isValid && currentStep < CHECKOUT_STEPS.length - 1) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const goToStep = async (stepIndex) => {
    // Validate all previous steps
    for (let i = 0; i < stepIndex; i++) {
      const step = CHECKOUT_STEPS[i];
      // TODO: Implement step validation with Django API
      const validation = { isValid: true };
      if (!validation.isValid) {
        toast.error(`Please complete ${step.title} first`);
        return;
      }
    }
    setCurrentStep(stepIndex);
  };

  const completeCheckout = async () => {
    try {
      setIsLoading(true);
      // TODO: Implement checkout completion with Django API
      toast.success('Order placed successfully!');
      // Redirect to order confirmation
      window.location.href = `/order-confirmation/123`;
    } catch (error) {
      console.error('Checkout error:', error);
      toast.error('Failed to complete checkout');
    } finally {
      setIsLoading(false);
    }
  };

  if (computeEmpty()) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">🛒</div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Your cart is empty</h2>
        <p className="text-gray-600 mb-6">Add some products to proceed with checkout</p>
        <a 
          href="/products" 
          className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
        >
          Continue Shopping
        </a>
      </div>
    );
  }

  const CurrentStepComponent = CHECKOUT_STEPS[currentStep].component;

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {CHECKOUT_STEPS.map((step, index) => {
            const isCompleted = index < currentStep;
            const isCurrent = index === currentStep;
            const isValid = stepValidation[step.id]?.isValid;
            
            return (
              <div key={step.id} className="flex items-center">
                <motion.button
                  onClick={() => goToStep(index)}
                  disabled={index > currentStep}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className={`relative flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all ${
                    isCompleted
                      ? 'bg-green-500 border-green-500 text-white'
                      : isCurrent
                      ? 'bg-primary-600 border-primary-600 text-white'
                      : isValid === false
                      ? 'bg-red-500 border-red-500 text-white'
                      : 'bg-gray-200 border-gray-300 text-gray-500'
                  } ${index <= currentStep ? 'cursor-pointer' : 'cursor-not-allowed'}`}
                >
                  {isCompleted ? (
                    <CheckIcon className="h-5 w-5" />
                  ) : isValid === false ? (
                    <ExclamationTriangleIcon className="h-5 w-5" />
                  ) : (
                    <span className="text-sm font-medium">{index + 1}</span>
                  )}
                </motion.button>
                
                <div className="ml-3 text-left">
                  <p className={`text-sm font-medium ${
                    isCurrent ? 'text-primary-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                  }`}>
                    {step.title}
                  </p>
                </div>
                
                {index < CHECKOUT_STEPS.length - 1 && (
                  <ChevronRightIcon className="h-5 w-5 text-gray-400 mx-4" />
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Step Content */}
      <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              {CHECKOUT_STEPS[currentStep].title}
            </h2>
            
            <CurrentStepComponent
              data={stepData[CHECKOUT_STEPS[currentStep].id] || {}}
              onDataChange={(data) => handleStepData(CHECKOUT_STEPS[currentStep].id, data)}
              validation={stepValidation[CHECKOUT_STEPS[currentStep].id]}
              checkout={checkout}
              onCheckoutUpdate={setCheckout}
            />
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Navigation */}
      <div className="flex justify-between items-center">
        <button
          onClick={prevStep}
          disabled={currentStep === 0}
          className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeftIcon className="h-5 w-5 mr-1" />
          Previous
        </button>

        <div className="flex space-x-4">
          {currentStep === CHECKOUT_STEPS.length - 1 ? (
            <motion.button
              onClick={completeCheckout}
              disabled={isLoading}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="bg-green-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" />
                  Processing...
                </div>
              ) : (
                'Complete Order'
              )}
            </motion.button>
          ) : (
            <motion.button
              onClick={nextStep}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="bg-primary-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center"
            >
              Next
              <ChevronRightIcon className="h-5 w-5 ml-1" />
            </motion.button>
          )}
        </div>
      </div>
    </div>
  );
};

export default InteractiveCheckout;
