# Generated by Django 4.2.7 on 2025-07-12 07:03

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0006_order_stripe_payment_intent_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Promocode',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('code', models.CharField(help_text='Unique promocode', max_length=50, unique=True)),
                ('description', models.TextField(blank=True, help_text='Description of the promocode', null=True)),
                ('discount_type', models.CharField(choices=[('percentage', 'Percentage'), ('fixed', 'Fixed Amount')], default='percentage', max_length=20)),
                ('discount_value', models.DecimalField(decimal_places=2, help_text='Discount amount or percentage', max_digits=10)),
                ('valid_from', models.DateTimeField(help_text='Start date and time for promocode validity')),
                ('valid_until', models.DateTimeField(help_text='End date and time for promocode validity')),
                ('usage_limit', models.IntegerField(default=1, help_text='Maximum number of times this code can be used (0 for unlimited)')),
                ('times_used', models.IntegerField(default=0, help_text='Number of times this code has been used')),
                ('applicable_to', models.CharField(choices=[('all', 'All Products'), ('category', 'Specific Categories'), ('product', 'Specific Products')], default='all', max_length=20)),
                ('minimum_order_amount', models.DecimalField(decimal_places=2, default=0, help_text='Minimum order amount to apply this promocode', max_digits=10)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this promocode is active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('applicable_categories', models.ManyToManyField(blank=True, help_text='Categories this promocode applies to', related_name='promocodes', to='api.category')),
                ('applicable_products', models.ManyToManyField(blank=True, help_text='Products this promocode applies to', related_name='promocodes', to='api.product')),
                ('created_by', models.ForeignKey(help_text='User who created this promocode', on_delete=django.db.models.deletion.CASCADE, related_name='created_promocodes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='order',
            name='promocode_discount',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Discount amount from promocode', max_digits=10),
        ),
        migrations.AddField(
            model_name='order',
            name='subtotal',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Order subtotal before discounts', max_digits=10),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='order',
            name='total_price',
            field=models.DecimalField(decimal_places=2, help_text='Final order total after all discounts', max_digits=10),
        ),
        migrations.AddField(
            model_name='order',
            name='applied_promocode',
            field=models.ForeignKey(blank=True, help_text='Promocode applied to this order', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='orders', to='api.promocode'),
        ),
        migrations.CreateModel(
            name='PromoCodeUsage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('discount_amount', models.DecimalField(decimal_places=2, help_text='Actual discount amount applied', max_digits=10)),
                ('order_amount_before_discount', models.DecimalField(decimal_places=2, help_text='Order total before promocode discount', max_digits=10)),
                ('order_amount_after_discount', models.DecimalField(decimal_places=2, help_text='Order total after promocode discount', max_digits=10)),
                ('used_at', models.DateTimeField(auto_now_add=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='IP address from which promocode was used', null=True)),
                ('user_agent', models.TextField(blank=True, help_text='User agent string', null=True)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='promocode_usage', to='api.order')),
                ('promocode', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='usage_history', to='api.promocode')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='promocode_usage', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-used_at'],
                'indexes': [models.Index(fields=['promocode', 'user'], name='api_promoco_promoco_6ec226_idx'), models.Index(fields=['used_at'], name='api_promoco_used_at_85d4d4_idx'), models.Index(fields=['order'], name='api_promoco_order_i_5be152_idx')],
                'unique_together': {('promocode', 'order')},
            },
        ),
        migrations.AddIndex(
            model_name='promocode',
            index=models.Index(fields=['code'], name='api_promoco_code_c16048_idx'),
        ),
        migrations.AddIndex(
            model_name='promocode',
            index=models.Index(fields=['valid_from', 'valid_until'], name='api_promoco_valid_f_38d9c6_idx'),
        ),
        migrations.AddIndex(
            model_name='promocode',
            index=models.Index(fields=['is_active'], name='api_promoco_is_acti_76b719_idx'),
        ),
    ]
