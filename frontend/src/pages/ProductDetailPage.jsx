import React, { useState, useEffect } from 'react';
import { Link, useParams, useLocation } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { StarIcon, ShoppingCartIcon, HeartIcon, ShareIcon, TruckIcon, ShieldCheckIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';

// Import components
import Layout from '../components/Layout.jsx';
import ProductCard from '../components/ProductCard.jsx';

// Import API services
import { productService } from '../services/api';
import { useAnalytics } from '../contexts/AnalyticsContext.jsx';

const ProductDetailPage = ({ userId }) => {
  const { productId } = useParams();
  const location = useLocation();
  const [quantity, setQuantity] = useState(1);
  const [selectedImage, setSelectedImage] = useState(0);
  const [product, setProduct] = useState(null);
  const [relatedProducts, setRelatedProducts] = useState([]);
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { trackProductDetailView, trackAddToCartAction } = useAnalytics();

  // Fetch product details from API
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch product details
        const productResponse = await productService.fetchProductDetails(productId);

        // Transform product data
        const productData = {
          id: productResponse.data.id,
          name: productResponse.data.title,
          price: parseFloat(productResponse.data.price),
          rating: 4.5, // Default rating since we don't have this in the API yet
          reviewCount: 10, // Default review count
          description: productResponse.data.description,
          features: productResponse.data.description.split('. ').filter(item => item.length > 0),
          specifications: {
            'Category': productResponse.data.category.name,
            'Price': `$${productResponse.data.price}`,
            'Stock': productResponse.data.stock_quantity,
            'Tags': productResponse.data.tags ? productResponse.data.tags.join(', ') : 'N/A'
          },
          images: productResponse.data.images ||
            (productResponse.data.picture ? [productResponse.data.picture] : []),
          inStock: productResponse.data.stock_quantity > 0,
          discount: productResponse.data.discount_type !== null,
          discountPercentage: productResponse.data.discount_value > 0 ? parseFloat(productResponse.data.discount_value) : 0,
          category: productResponse.data.category.name
        };

        setProduct(productData);

        // Track product detail view
        const source = new URLSearchParams(location.search).get('from') || 'direct';
        trackProductDetailView(productData, source);

        // Fetch related products
        const relatedResponse = await productService.fetchRelatedProducts(productId);

        // Transform related products data
        const relatedProductsData = relatedResponse.data.results.map(product => ({
          id: product.id,
          name: product.title,
          price: parseFloat(product.price),
          rating: 4.5, // Default rating
          reviewCount: 10, // Default review count
          imageSrc: product.images && product.images.length > 0 ? product.images[0] : product.picture,
          images: product.images || (product.picture ? [product.picture] : []),
          discount: product.discount_type !== null,
          discountPercentage: product.discount_value > 0 ? parseFloat(product.discount_value) : 0,
          category: product.category.name
        }));

        setRelatedProducts(relatedProductsData);

        // Fetch product reviews
        const reviewsResponse = await productService.fetchProductReviews(productId);

        // Transform reviews data or use default if no reviews
        const reviewsData = reviewsResponse.data.results.length > 0
          ? reviewsResponse.data.results.map(review => ({
              id: review.id,
              author: review.user.name || 'Anonymous',
              date: new Date(review.created_at).toLocaleDateString(),
              rating: review.rating,
              title: 'Product Review',
              content: review.comment
            }))
          : [
              {
                id: 1,
                author: 'John D.',
                date: '2 weeks ago',
                rating: 5,
                title: 'Great product!',
                content: 'This is an excellent product. Highly recommended!'
              }
            ];

        setReviews(reviewsData);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching product data:', err);
        setError('Failed to load product details. Please try again later.');
        setLoading(false);
        toast.error('Failed to load product details. Please try again later.');
      }
    };

    fetchData();
  }, [productId, location.search, trackProductDetailView]);

  // Calculate discounted price
  const discountedPrice = product && product.discount
    ? product.price - (product.price * product.discountPercentage) / 100
    : product ? product.price : 0;

  const handleQuantityChange = (newQuantity) => {
    if (newQuantity >= 1 && newQuantity <= 10) {
      setQuantity(newQuantity);
    }
  };

  const handleAddToCart = () => {
    if (product) {
      // Track add to cart action
      trackAddToCartAction(product, quantity, 'product_detail_page');
    }
    toast.success(`${quantity} ${quantity > 1 ? 'items' : 'item'} added to cart!`);
  };

  const handleAddToWishlist = () => {
    toast.success('Added to wishlist!');
  };

  const handleShare = () => {
    toast.success('Share link copied to clipboard!');
  };

  return (
    <Layout userId={userId}>

      {/* Main Content */}
      <main className="flex-grow">
        {/* Breadcrumbs */}
        <div className="bg-white border-b">
          <div className="mx-auto max-w-7xl px-4 py-3 sm:px-6 lg:px-8">
            <nav className="flex text-sm text-gray-500">
              <Link to="/" className="hover:text-gray-700">Home</Link>
              <span className="mx-2">/</span>
              <Link to="/products" className="hover:text-gray-700">Products</Link>
              {!loading && !error && product && (
                <>
                  <span className="mx-2">/</span>
                  <Link
                    to={`/products?category=${product.category.toLowerCase()}`}
                    className="hover:text-gray-700"
                  >
                    {product.category}
                  </Link>
                  <span className="mx-2">/</span>
                  <span className="text-gray-900">{product.name}</span>
                </>
              )}
            </nav>
          </div>
        </div>

        {/* Product Detail */}
        <section className="py-12">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {loading ? (
              <div className="flex justify-center py-24">
                <div className="h-16 w-16 animate-spin rounded-full border-b-2 border-t-2 border-primary-600"></div>
              </div>
            ) : error ? (
              <div className="rounded-lg bg-red-50 p-8 text-center">
                <h2 className="text-xl font-semibold mb-2">Error</h2>
                <p className="text-red-800 mb-4">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="bg-primary-600 text-white py-2 px-4 rounded hover:bg-primary-700 inline-block"
                >
                  Try Again
                </button>
              </div>
            ) : product ? (
              <div className="grid grid-cols-1 gap-12 lg:grid-cols-2">
                {/* Product Gallery */}
                <div className="animate-fade-in">
                  <div className="overflow-hidden rounded-xl bg-white p-4 shadow-soft">
                    <div className="relative aspect-square overflow-hidden rounded-lg bg-gray-200">
                      {product.images && product.images.length > 0 && product.images[selectedImage] ? (
                        <img
                          src={product.images[selectedImage]}
                          alt={product.name}
                          className="h-full w-full object-cover object-center"
                        />
                      ) : (
                        <div className="flex h-full w-full items-center justify-center bg-gray-200 text-gray-500">
                          No Image
                        </div>
                      )}
                      {product.discount && (
                        <div className="absolute left-4 top-4 rounded-md bg-red-500 px-2 py-1 text-sm font-bold text-white">
                          {product.discountPercentage}% OFF
                        </div>
                      )}
                    </div>
                    {product.images && product.images.length > 0 && (
                      <div className="mt-4 grid grid-cols-4 gap-4">
                        {product.images.map((image, index) => (
                          <button
                            key={index}
                            className={`overflow-hidden rounded-md border-2 ${
                              selectedImage === index ? 'border-primary-500' : 'border-transparent'
                            }`}
                            onClick={() => setSelectedImage(index)}
                          >
                            {image ? (
                              <img
                                src={image}
                                alt={`${product.name} - View ${index + 1}`}
                                className="h-24 w-full object-cover object-center"
                              />
                            ) : (
                              <div className="flex h-24 w-full items-center justify-center bg-gray-200 text-gray-500">
                                {index + 1}
                              </div>
                            )}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

              {/* Product Info */}
              <div className="animate-slide-in-right">
                <div className="space-y-6">
                  {/* Product Title and Rating */}
                  <div>
                    <h1 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">{product.name}</h1>
                    <div className="mt-2 flex items-center">
                      <div className="flex text-yellow-400">
                        {[...Array(5)].map((_, i) => (
                          i < Math.floor(product.rating) ? (
                            <StarSolidIcon key={i} className="h-5 w-5" />
                          ) : (
                            <StarIcon key={i} className="h-5 w-5" />
                          )
                        ))}
                      </div>
                      <span className="ml-2 text-sm text-gray-600">{product.rating} ({product.reviewCount} reviews)</span>
                    </div>
                  </div>

                  {/* Price */}
                  <div className="border-t border-b border-gray-200 py-4">
                    {product.discount ? (
                      <div className="flex items-center">
                        <span className="text-3xl font-bold text-gray-900">${discountedPrice.toFixed(2)}</span>
                        <span className="ml-3 text-lg text-gray-500 line-through">${product.price.toFixed(2)}</span>
                        <span className="ml-3 rounded-md bg-green-100 px-2 py-1 text-sm font-medium text-green-800">
                          Save ${(product.price - discountedPrice).toFixed(2)}
                        </span>
                      </div>
                    ) : (
                      <span className="text-3xl font-bold text-gray-900">${product.price.toFixed(2)}</span>
                    )}
                    <p className="mt-2 text-sm text-gray-500">All prices include taxes.</p>
                  </div>

                  {/* Description */}
                  <div>
                    <h2 className="text-lg font-medium text-gray-900">Description</h2>
                    <p className="mt-2 text-gray-600">{product.description}</p>
                  </div>

                  {/* Features */}
                  <div>
                    <h2 className="text-lg font-medium text-gray-900">Key Features</h2>
                    <ul className="mt-2 list-inside list-disc space-y-1 text-gray-600">
                      {product.features.map((feature, index) => (
                        <li key={index}>{feature}</li>
                      ))}
                    </ul>
                  </div>

                  {/* Availability */}
                  <div className="flex items-center">
                    <div className={`mr-2 h-3 w-3 rounded-full ${product.inStock ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <span className="font-medium">{product.inStock ? 'In Stock' : 'Out of Stock'}</span>
                  </div>

                  {/* Quantity */}
                  <div>
                    <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">
                      Quantity
                    </label>
                    <div className="mt-1 flex max-w-[150px] rounded-md shadow-sm">
                      <button
                        type="button"
                        className="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3 py-2 text-gray-500 hover:bg-gray-100"
                        onClick={() => handleQuantityChange(quantity - 1)}
                        disabled={quantity <= 1}
                      >
                        -
                      </button>
                      <input
                        type="number"
                        id="quantity"
                        className="block w-full border-gray-300 text-center focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        value={quantity}
                        onChange={(e) => handleQuantityChange(parseInt(e.target.value) || 1)}
                        min="1"
                        max="10"
                      />
                      <button
                        type="button"
                        className="inline-flex items-center rounded-r-md border border-l-0 border-gray-300 bg-gray-50 px-3 py-2 text-gray-500 hover:bg-gray-100"
                        onClick={() => handleQuantityChange(quantity + 1)}
                        disabled={quantity >= 10}
                      >
                        +
                      </button>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col space-y-3 sm:flex-row sm:space-x-4 sm:space-y-0">
                    <button
                      type="button"
                      className="inline-flex items-center justify-center rounded-md bg-primary-600 px-8 py-3 text-base font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                      onClick={handleAddToCart}
                    >
                      <ShoppingCartIcon className="mr-2 -ml-1 h-5 w-5" aria-hidden="true" />
                      Add to Cart
                    </button>
                    <button
                      type="button"
                      className="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-3 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                      onClick={handleAddToWishlist}
                    >
                      <HeartIcon className="mr-2 -ml-1 h-5 w-5" aria-hidden="true" />
                      Wishlist
                    </button>
                    <button
                      type="button"
                      className="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-3 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                      onClick={handleShare}
                    >
                      <ShareIcon className="mr-2 -ml-1 h-5 w-5" aria-hidden="true" />
                      Share
                    </button>
                  </div>

                  {/* Additional Info */}
                  <div className="grid grid-cols-1 gap-4 rounded-lg bg-gray-50 p-4 sm:grid-cols-3">
                    <div className="flex items-center">
                      <TruckIcon className="mr-2 h-5 w-5 text-primary-600" />
                      <span className="text-sm">Free shipping over $50</span>
                    </div>
                    <div className="flex items-center">
                      <ShieldCheckIcon className="mr-2 h-5 w-5 text-primary-600" />
                      <span className="text-sm">2-year warranty</span>
                    </div>
                    <div className="flex items-center">
                      <ArrowPathIcon className="mr-2 h-5 w-5 text-primary-600" />
                      <span className="text-sm">30-day returns</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            ) : null}

            {/* Specifications */}
            {!loading && !error && product && (
              <div className="mt-16 animate-slide-up">
                <h2 className="text-2xl font-bold tracking-tight text-gray-900">Specifications</h2>
                <div className="mt-6 overflow-hidden rounded-lg border border-gray-200">
                  <table className="min-w-full divide-y divide-gray-200">
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {Object.entries(product.specifications).map(([key, value]) => (
                        <tr key={key}>
                          <td className="whitespace-nowrap py-4 pl-6 pr-3 text-sm font-medium text-gray-900">{key}</td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{value}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Reviews */}
            {!loading && !error && product && (
              <div className="mt-16 animate-slide-up">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold tracking-tight text-gray-900">Customer Reviews</h2>
                  <button
                    onClick={() => toast.success('Review feature coming soon!')}
                    className="rounded-md bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                  >
                    Write a Review
                  </button>
                </div>
                <div className="mt-6 space-y-6">
                  {reviews.length > 0 ? (
                    reviews.map((review) => (
                      <div key={review.id} className="rounded-lg bg-white p-6 shadow-soft">
                        <div className="flex items-start justify-between">
                          <div>
                            <div className="flex items-center">
                              <div className="flex text-yellow-400">
                                {[...Array(5)].map((_, i) => (
                                  <StarSolidIcon key={i} className={`h-5 w-5 ${i < review.rating ? 'text-yellow-400' : 'text-gray-300'}`} />
                                ))}
                              </div>
                              <h3 className="ml-2 text-lg font-semibold text-gray-900">{review.title}</h3>
                            </div>
                            <p className="mt-1 text-sm text-gray-500">
                              by {review.author} • {review.date}
                            </p>
                          </div>
                        </div>
                        <p className="mt-4 text-gray-600">{review.content}</p>
                      </div>
                    ))
                  ) : (
                    <div className="rounded-lg bg-white p-6 shadow-soft text-center">
                      <p className="text-gray-500">No reviews yet. Be the first to review this product!</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Related Products */}
            {!loading && !error && product && (
              <div className="mt-16 animate-slide-up">
                <h2 className="text-2xl font-bold tracking-tight text-gray-900">Related Products</h2>
                {relatedProducts.length > 0 ? (
                  <div className="mt-6 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                    {relatedProducts.map((relatedProduct) => (
                      <ProductCard key={relatedProduct.id} product={relatedProduct} />
                    ))}
                  </div>
                ) : (
                  <p className="mt-4 text-gray-500">No related products found.</p>
                )}
              </div>
            )}
          </div>
        </section>
      </main>
    </Layout>
  );
};

export default ProductDetailPage;
