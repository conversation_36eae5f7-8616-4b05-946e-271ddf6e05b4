
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ProductCard from '../ProductCard.jsx';
import { productService } from '../../services/api';
import toast from 'react-hot-toast';

const ProductGrid = ({
  products: externalProducts,
  useSaleor = false,
  filters = {},
  sortBy = null,
  searchQuery = '',
  itemsPerPage = 12,
  showLoadMore = true
}) => {
  const [localProducts, setLocalProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [cursor, setCursor] = useState(null);

  // TODO: Implement with Django API

  // Use external products or fetch from API
  useEffect(() => {
    if (externalProducts) {
      setLocalProducts(externalProducts);
    } else {
      fetchLegacyProducts();
    }
  }, [externalProducts, filters, searchQuery]);

  const fetchLegacyProducts = async () => {
    try {
      setLoading(true);
      const response = await productService.getProducts();
      setLocalProducts(response.data || []);
    } catch (error) {
      console.error('Error fetching products:', error);
      toast.error('Failed to load products');
    } finally {
      setLoading(false);
    }
  };

  const loadMoreProducts = async () => {
    // TODO: Implement load more with Django API
    console.log('Load more products...');
  };

  // Get products from local state
  const products = localProducts;
  const isLoading = loading;
  const hasMoreItems = hasMore;

  return (
    <div className="space-y-6">
      {/* Products Grid */}
      <motion.div
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        layout
      >
        <AnimatePresence>
          {products.map((product, index) => (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              layout
            >
              <ProductCard
                product={product}
                variant="interactive"
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </motion.div>

      {/* Loading State */}
      {isLoading && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[...Array(8)].map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-gray-300 h-64 rounded-lg mb-4"></div>
              <div className="bg-gray-300 h-4 rounded mb-2"></div>
              <div className="bg-gray-300 h-4 rounded w-3/4 mb-2"></div>
              <div className="bg-gray-300 h-6 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      )}

      {/* Load More Button */}
      {showLoadMore && hasMoreItems && !isLoading && products.length > 0 && (
        <div className="flex justify-center">
          <motion.button
            onClick={loadMoreProducts}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          >
            Load More Products
          </motion.button>
        </div>
      )}

      {/* Empty State */}
      {!isLoading && products.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🛍️</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
          <p className="text-gray-500">
            {searchQuery
              ? `No products match "${searchQuery}"`
              : 'No products available at the moment'
            }
          </p>
        </div>
      )}
    </div>
  );
};

export default ProductGrid;
