import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import Layout from '../components/Layout.jsx';
import toast from 'react-hot-toast';
import { inventoryService } from '../services/api';

const StatCard = ({ label, value }) => (
  <div className="p-6 rounded-lg bg-white shadow border border-gray-100">
    <div className="text-sm text-gray-500">{label}</div>
    <div className="mt-2 text-2xl font-semibold">{value}</div>
  </div>
);

const InventoryDashboardPage = () => {
  const [loading, setLoading] = useState(true);
  const [summary, setSummary] = useState({ low_stock_count: 0, total_inventory_value: 0, recent_changes: [] });

  useEffect(() => {
    const load = async () => {
      try {
        const { data } = await inventoryService.getSummary();
        setSummary(data);
      } catch (e) {
        console.error(e);
        toast.error('Failed to load inventory summary');
      } finally {
        setLoading(false);
      }
    };
    load();
  }, []);

  return (
    <Layout>
      <div className="container mx-auto py-10 px-4 space-y-8">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Inventory Dashboard</h1>
          <div className="flex gap-3">
            <Link to="/admin/inventory/changes" className="px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700">Make Adjustment</Link>
            <Link to="/admin/inventory/alerts" className="px-4 py-2 rounded bg-amber-600 text-white hover:bg-amber-700">View Alerts</Link>
            <Link to="/admin/inventory/batches" className="px-4 py-2 rounded bg-teal-600 text-white hover:bg-teal-700">Batches</Link>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <StatCard label="Low stock products" value={summary.low_stock_count} />
          <StatCard label="Total inventory value" value={`$${Number(summary?.total_inventory_value || 0).toFixed(2)}`} />
          <StatCard label="Recent changes" value={summary?.recent_changes?.length || 0} />
        </div>

        <div className="bg-white rounded-lg shadow border border-gray-100">
          <div className="px-6 py-4 border-b">
            <h2 className="text-lg font-semibold">Recent Inventory Changes</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="text-left text-sm text-gray-500">
                  <th className="px-6 py-3">Product</th>
                  <th className="px-6 py-3">Change</th>
                  <th className="px-6 py-3">Type</th>
                  <th className="px-6 py-3">Previous → New</th>
                  <th className="px-6 py-3">When</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr><td className="px-6 py-6" colSpan={5}>Loading...</td></tr>
                ) : summary?.recent_changes?.length ? (
                  summary.recent_changes.map((chg) => (
                    <tr key={chg.id} className="border-t">
                      <td className="px-6 py-3">{chg.product?.title || chg.product_title || '—'}</td>
                      <td className="px-6 py-3">{chg.quantity_change}</td>
                      <td className="px-6 py-3">{chg.change_type}</td>
                      <td className="px-6 py-3">{chg.previous_quantity} → {chg.new_quantity}</td>
                      <td className="px-6 py-3">{new Date(chg.timestamp).toLocaleString()}</td>
                    </tr>
                  ))
                ) : (
                  <tr><td className="px-6 py-6" colSpan={5}>No recent changes</td></tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default InventoryDashboardPage;

