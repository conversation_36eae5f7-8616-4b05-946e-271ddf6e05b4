"""
URL configuration for the eCommerce API.

This module defines all API endpoints and their corresponding views.
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from rest_framework_simplejwt.views import TokenRefreshView

from .views import (
    # Authentication views
    RegisterView, LoginView, ForgotPasswordView, ResetPasswordView, CSRFTokenView, GoogleAuthView,

    # User management views
    UserViewSet, RoleViewSet,

    # Product management views
    CategoryViewSet, ProductViewSet, ProductImageViewSet, ProductModerationLogViewSet,

    # Shopping views
    CartViewSet, OrderViewSet, AdminOrderViewSet, ReviewViewSet,
    OfferViewSet,

    # Analytics views
    UserBehaviorView, GeneralAnalyticsView, UserAnalyticsView,

    # Recommendations
    RecommendationView,

    # Page views
    HomePageView, SearchView,

    # Checkout process views
    ShippingOptionsView, PaymentProcessingView, EnhancedPaymentProcessingView,
    PromoCodeView, StripeWebhookView,

    # Enhanced Stripe payment views
    StripeCustomerViewSet, StripePaymentMethodViewSet, StripeRefundViewSet,
    PaymentAnalyticsView, PaymentHistoryView,

    # Inventory management views
    InventoryManagementViewSet, SupplierViewSet, InventoryBatchViewSet, InventoryAlertViewSet,

    # Sales management views
    SalesReportViewSet,

    # Promocode management views
    PromocodeViewSet, PromoCodeUsageViewSet
)

router = DefaultRouter()

# User management endpoints
router.register(r'users', UserViewSet)
router.register(r'roles', RoleViewSet)

# Product management endpoints
router.register(r'categories', CategoryViewSet)
router.register(r'products', ProductViewSet)
router.register(r'product-images', ProductImageViewSet)
router.register(r'moderation', ProductModerationLogViewSet)

# Shopping endpoints
router.register(r'carts', CartViewSet, basename='cart')
router.register(r'orders', OrderViewSet, basename='order')
router.register(r'reviews', ReviewViewSet, basename='review')
router.register(r'offers', OfferViewSet)

# Admin endpoints
router.register(r'admin/orders', AdminOrderViewSet)

# Inventory management endpoints
router.register(r'inventory', InventoryManagementViewSet, basename='inventory')
router.register(r'suppliers', SupplierViewSet)
router.register(r'inventory-batches', InventoryBatchViewSet)
router.register(r'inventory-alerts', InventoryAlertViewSet)

# Sales management endpoints
router.register(r'sales-reports', SalesReportViewSet)

# Promocode management endpoints
router.register(r'promocodes', PromocodeViewSet)
router.register(r'promocode-usage', PromoCodeUsageViewSet)

# Enhanced Stripe payment endpoints
router.register(r'stripe/customers', StripeCustomerViewSet, basename='stripe-customer')
router.register(r'stripe/payment-methods', StripePaymentMethodViewSet, basename='stripe-payment-method')
router.register(r'stripe/refunds', StripeRefundViewSet, basename='stripe-refund')

urlpatterns = [
    path('', include(router.urls)),

    # Authentication endpoints
    path('auth/register/', RegisterView.as_view(), name='register'),
    path('auth/login/', LoginView.as_view(), name='login'),
    path('auth/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('auth/forgot-password/', ForgotPasswordView.as_view(), name='forgot_password'),
    path('auth/reset-password/', ResetPasswordView.as_view(), name='reset_password'),
    path('auth/csrf-token/', CSRFTokenView.as_view(), name='csrf_token'),
    path('auth/google/', GoogleAuthView.as_view(), name='google_auth'),

    # User behavior tracking
    path('track/', UserBehaviorView.as_view(), name='track_behavior'),

    # Analytics endpoints
    path('analytics/general/', GeneralAnalyticsView.as_view(), name='general_analytics'),
    path('analytics/user/<uuid:user_id>/', UserAnalyticsView.as_view(), name='user_analytics'),
    path('analytics/user/', UserAnalyticsView.as_view(), name='current_user_analytics'),

    # Home page data
    path('home/', HomePageView.as_view(), name='home_page'),

    # Search
    path('search/', SearchView.as_view(), name='search'),

    # Checkout related endpoints
    path('shipping_options/', ShippingOptionsView.as_view(), name='shipping_options'),
    path('payment/', PaymentProcessingView.as_view(), name='payment_processing'),
    path('payment/enhanced/', EnhancedPaymentProcessingView.as_view(), name='enhanced_payment_processing'),
    path('apply_promo/', PromoCodeView.as_view(), name='apply_promo'),
    path('webhook/stripe/', StripeWebhookView.as_view(), name='stripe_webhook'),

    # Enhanced payment endpoints
    # Recommendations
    path('recommendations/', RecommendationView.as_view(), name='recommendations'),

    path('payments/analytics/', PaymentAnalyticsView.as_view(), name='payment_analytics'),
    path('payments/history/', PaymentHistoryView.as_view(), name='payment_history'),
]


# API schema & interactive docs (Swagger with drf-yasg)
try:
    from drf_yasg.views import get_schema_view
    from drf_yasg import openapi
    from rest_framework import permissions

    schema_view = get_schema_view(
        openapi.Info(
            title="Ecommerce API",
            default_version='v1',
            description="API documentation for the ecommerce platform",
            contact=openapi.Contact(email="<EMAIL>"),
            license=openapi.License(name="Proprietary"),
        ),
        public=True,
        permission_classes=(permissions.AllowAny,),
    )

    urlpatterns += [
        path('docs/', schema_view.with_ui('swagger', cache_timeout=0), name='swagger-ui'),
        path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='redoc'),
        path('schema.json', schema_view.without_ui(cache_timeout=0), name='schema-json'),
    ]
except Exception as e:
    # drf-yasg not installed; skip docs routes silently
    pass
