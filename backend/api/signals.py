"""
Django signals for the eCommerce platform.

This module defines signal handlers to keep Typesense in sync with Django models.
"""
import logging
import os
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.conf import settings
from .models import Product, CartItem, Order
from .typesense_utils import index_product, delete_product, create_product_schema
from .tasks import schedule_reco_rebuild

logger = logging.getLogger(__name__)

# Check if Typesense is enabled
TYPESENSE_ENABLED = os.environ.get('TYPESENSE_ENABLED', 'False').lower() == 'true'

# Create Typesense schema when the module is loaded (if enabled)
if TYPESENSE_ENABLED:
    try:
        create_product_schema()
        logger.info("Typesense schema created successfully")
    except Exception as e:
        logger.error(f"Failed to create Typesense schema on startup: {str(e)}")
        logger.warning("Typesense integration will be disabled")
        TYPESENSE_ENABLED = False
else:
    logger.info("Typesense integration is disabled")

@receiver(post_save, sender=Product)
def product_saved_handler(sender, instance, created, **kwargs):
    """
    Signal handler for when a Product is saved.

    This handler indexes the product in Typesense whenever a product is created or updated.
    Only runs if Typesense integration is enabled.

    Args:
        sender: The model class (Product)
        instance: The actual instance being saved
        created: Boolean indicating if this is a new instance
    """
    if not TYPESENSE_ENABLED:
        return

    try:
        logger.info(f"{'Creating' if created else 'Updating'} product in Typesense: {instance.id}")
        index_product(instance)
    except Exception as e:
        logger.error(f"Error in product_saved_handler: {str(e)}")

@receiver(post_delete, sender=Product)
def product_deleted_handler(sender, instance, **kwargs):
    """
    Signal handler for when a Product is deleted.

    This handler removes the product from Typesense whenever a product is deleted from Django.
    Only runs if Typesense integration is enabled.

    Args:
        sender: The model class (Product)
        instance: The actual instance being deleted
    """
    if not TYPESENSE_ENABLED:
        return

    try:
        logger.info(f"Deleting product from Typesense: {instance.id}")
        delete_product(instance.id)
    except Exception as e:
        logger.error(f"Error in product_deleted_handler: {str(e)}")

# --- Additional signals for recommendation rebuilds (debounced) ---
@receiver(post_save, sender=CartItem)
def cartitem_saved_handler(sender, instance: CartItem, created, **kwargs):
    try:
        user_id = getattr(getattr(instance, 'cart', None), 'created_by_id', None)
        if user_id:
            schedule_reco_rebuild(user_id, event='cart_signal')
    except Exception:
        logger.exception("cartitem_saved_handler failed (non-fatal)")


@receiver(post_delete, sender=CartItem)
def cartitem_deleted_handler(sender, instance: CartItem, **kwargs):
    try:
        user_id = getattr(getattr(instance, 'cart', None), 'created_by_id', None)
        if user_id:
            schedule_reco_rebuild(user_id, event='cart_signal')
    except Exception:
        logger.exception("cartitem_deleted_handler failed (non-fatal)")


@receiver(post_save, sender=Order)
def order_saved_handler(sender, instance: Order, created, **kwargs):
    try:
        # Trigger on newly created orders or when payment becomes paid
        if created or getattr(instance, 'payment_status', None) == 'paid':
            if getattr(instance, 'user_id', None):
                evt = 'order_created' if created else 'order_paid'
                schedule_reco_rebuild(instance.user_id, event=evt)
    except Exception:
        logger.exception("order_saved_handler failed (non-fatal)")

