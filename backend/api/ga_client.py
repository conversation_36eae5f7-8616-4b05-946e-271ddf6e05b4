from __future__ import annotations
"""
Lightweight Google Analytics (GA4) client wrapper with safe fallbacks.

This module is designed to be optional: if GA libraries or credentials are not
present, the functions return empty datasets instead of raising errors.

To enable GA integration:
- Set GA_ENABLED=true in environment
- Provide GA4_PROPERTY_ID and GA_CREDENTIALS_FILE (path to service account JSON)
- Install google-analytics-data (GA4 Data API client)

Until then, recommendation tasks will rely on in-app tracking (UserBehavior)
plus cart/order data.
"""
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Iterable, Optional

from django.conf import settings

logger = logging.getLogger("api")


class GADataClient:
    """Minimal GA4 client with defensive behavior when GA is disabled/unavailable."""

    def __init__(self) -> None:
        self.enabled = getattr(settings, "GA_ENABLED", False)
        self.property_id = getattr(settings, "GA4_PROPERTY_ID", None)
        self.credentials_file = getattr(settings, "GA_CREDENTIALS_FILE", None)

        self._client = None
        if self.enabled and self.property_id and self.credentials_file:
            try:
                # Deferred import to avoid hard dependency when GA not in use
                from google.analytics.data_v1beta import BetaAnalyticsDataClient
                from google.oauth2 import service_account

                credentials = service_account.Credentials.from_service_account_file(
                    self.credentials_file
                )
                self._client = BetaAnalyticsDataClient(credentials=credentials)
            except Exception as e:  # pragma: no cover - optional path
                logger.warning(
                    f"GADataClient init failed (GA disabled at runtime): {e}. Falling back to empty data."
                )
                self.enabled = False
                self._client = None
        else:
            # Not configured; operate in disabled mode
            self.enabled = False

    def fetch_user_product_views(self, user_identifier: str, days: int = 30) -> Dict[str, int]:
        """
        Return mapping of product_id (or slug) -> view_count for a given user.

        user_identifier: an identifier you map between GA and your app. Commonly this
                         can be user.id or a hashed ID stored as GA user_id.
        Note: Actual implementation depends on how your GA events include product_id.
        This function is intentionally conservative and returns empty if GA is not
        configured or if any error occurs.
        """
        if not (self.enabled and self._client and self.property_id):
            return {}

        try:
            # GA4 Data API example (pseudo, depends on your GA events schema):
            # This is a placeholder and may need adjustment to your GA setup.
            from google.analytics.data_v1beta.types import DateRange, Dimension, Metric, RunReportRequest

            end_date = datetime.utcnow().date()
            start_date = end_date - timedelta(days=days)

            request = RunReportRequest(
                property=f"properties/{self.property_id}",
                dimensions=[
                    Dimension(name="customUserId"),  # requires mapping in GA
                    Dimension(name="customEvent:product_id"),
                ],
                metrics=[Metric(name="eventCount")],
                date_ranges=[DateRange(start_date=str(start_date), end_date=str(end_date))],
                dimension_filter={
                    "filter": {
                        "field_name": "customUserId",
                        "string_filter": {"match_type": 1, "value": str(user_identifier)},
                    }
                },
            )
            response = self._client.run_report(request)

            views: Dict[str, int] = {}
            for row in response.rows:  # type: ignore[attr-defined]
                product_id = row.dimension_values[1].value  # product_id from event param
                count = int(row.metric_values[0].value)
                views[product_id] = views.get(product_id, 0) + count
            return views
        except Exception as e:  # pragma: no cover - remote API/optional
            logger.info(f"GA fetch_user_product_views failed: {e}. Returning empty.")
            return {}


def get_user_ga_product_signals(user, days: int = 30) -> Dict[str, int]:
    """
    Facade used by tasks: returns product_id/slug -> view_count from GA if configured,
    otherwise returns empty dict. Callers should combine this with in-app signals.
    """
    client = GADataClient()
    # Map user to GA user identifier; commonly user.id as string
    user_identifier = str(getattr(user, "id", None))
    if not user_identifier:
        return {}
    return client.fetch_user_product_views(user_identifier=user_identifier, days=days)

