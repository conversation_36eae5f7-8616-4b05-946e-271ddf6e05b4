import React, { useEffect, useState, useCallback } from 'react';
import Layout from '../components/Layout.jsx';
import toast from 'react-hot-toast';
import { salesService } from '../services/api';

const SalesReportsPage = () => {
  const today = new Date();
  const toISODate = (d) => d.toISOString().slice(0, 10);
  const defaultStart = new Date(); defaultStart.setDate(today.getDate() - 30);

  const [reportType, setReportType] = useState('daily');
  const [startDate, setStartDate] = useState(toISODate(defaultStart));
  const [endDate, setEndDate] = useState(toISODate(today));
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(false);

  const load = useCallback(async () => {
    setLoading(true);
    try {
      const params = { report_type: reportType, start_date: startDate, end_date: endDate };
      const { data } = await salesService.listReports(params);
      const items = Array.isArray(data?.results) ? data.results : (Array.isArray(data) ? data : []);
      setReports(items);
    } catch (e) {
      console.error(e);
      toast.error('Failed to load sales reports');
    } finally {
      setLoading(false);
    }
  }, [reportType, startDate, endDate]);

  useEffect(() => { load(); }, [load]);

  const generate = async (e) => {
    e.preventDefault();
    try {
      const genDate = e.target.report_date.value;
      const genType = e.target.gen_report_type.value;
      await salesService.generateReport({ report_date: genDate, report_type: genType });
      toast.success('Report generated');
      await load();
    } catch (e) {
      console.error(e);
      toast.error('Failed to generate report');
    }
  };

  const fmt = (n) => `$${Number(n || 0).toFixed(2)}`;

  return (
    <Layout>
      <div className="container mx-auto py-10 px-4 space-y-8">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Sales Reports</h1>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow border border-gray-100 p-4">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-end">
            <div>
              <label className="block text-sm text-gray-600 mb-1">Report Type</label>
              <select value={reportType} onChange={(e)=>setReportType(e.target.value)} className="w-full border rounded px-3 py-2">
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
                <option value="yearly">Yearly</option>
              </select>
            </div>
            <div>
              <label className="block text-sm text-gray-600 mb-1">Start Date</label>
              <input type="date" value={startDate} onChange={e=>setStartDate(e.target.value)} className="w-full border rounded px-3 py-2" />
            </div>
            <div>
              <label className="block text-sm text-gray-600 mb-1">End Date</label>
              <input type="date" value={endDate} onChange={e=>setEndDate(e.target.value)} className="w-full border rounded px-3 py-2" />
            </div>
            <div>
              <button onClick={load} className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Load Reports</button>
            </div>
            <form onSubmit={generate} className="flex items-end gap-2">
              <div>
                <label className="block text-sm text-gray-600 mb-1">Generate (Date)</label>
                <input type="date" name="report_date" defaultValue={endDate} className="border rounded px-3 py-2" />
              </div>
              <div>
                <label className="block text-sm text-gray-600 mb-1">Type</label>
                <select name="gen_report_type" defaultValue={reportType} className="border rounded px-3 py-2">
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                  <option value="yearly">Yearly</option>
                </select>
              </div>
              <button type="submit" className="px-4 py-2 bg-emerald-600 text-white rounded hover:bg-emerald-700">Generate</button>
            </form>
          </div>
        </div>

        {/* Results */}
        <div className="bg-white rounded-lg shadow border border-gray-100">
          <div className="px-6 py-4 border-b flex items-center justify-between">
            <h2 className="text-lg font-semibold">Results</h2>
            {loading ? <span className="text-sm text-gray-500">Loading...</span> : null}
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="text-left text-sm text-gray-500">
                  <th className="px-6 py-3">Date</th>
                  <th className="px-6 py-3">Type</th>
                  <th className="px-6 py-3">Orders</th>
                  <th className="px-6 py-3">Revenue</th>
                  <th className="px-6 py-3">Units</th>
                  <th className="px-6 py-3">Avg Order Value</th>
                  <th className="px-6 py-3">Top Product</th>
                </tr>
              </thead>
              <tbody>
                {reports.length ? reports.map((r) => (
                  <tr key={r.id} className="border-t">
                    <td className="px-6 py-3">{r.report_date}</td>
                    <td className="px-6 py-3">{r.report_type}</td>
                    <td className="px-6 py-3">{r.total_orders}</td>
                    <td className="px-6 py-3">{fmt(r.total_revenue)}</td>
                    <td className="px-6 py-3">{r.total_units_sold}</td>
                    <td className="px-6 py-3">{fmt(r.average_order_value)}</td>
                    <td className="px-6 py-3">{r.top_selling_product_title || '-'}</td>
                  </tr>
                )) : (
                  <tr><td className="px-6 py-6" colSpan={7}>No reports found</td></tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default SalesReportsPage;

