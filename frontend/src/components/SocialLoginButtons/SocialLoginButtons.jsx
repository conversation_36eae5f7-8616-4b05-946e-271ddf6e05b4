import React from 'react';


const SocialLoginButtons = () => {
  const handleSocialLogin = async (provider) => {
    try {
      if (provider === 'google') {
        console.warn('Use Google Sign-In to obtain an idToken, then call authService.googleLogin(idToken).');
      } else {
        console.warn(`Provider ${provider} is not supported yet.`);
      }
    } catch (error) {
      console.error('Social login error:', error);
    }
  };

  return (
    <div className="space-y-4">
      <button
        onClick={() => handleSocialLogin('google')}
        className="btn-outline w-full"
      >
        Login with Google
      </button>
      
      <button
        onClick={() => handleSocialLogin('facebook')}
        className="btn-outline w-full"
      >
        Login with Facebook
      </button>
    </div>
  );
};

export default SocialLoginButtons;
