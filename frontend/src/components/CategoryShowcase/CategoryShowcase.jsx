import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  ArrowRightIcon,
  SparklesIcon,
  ArrowTrendingUpIcon,
  ShoppingBagIcon
} from '@heroicons/react/24/outline';

const CategoryShowcase = ({ categories = [], title = "Shop by Category" }) => {
  const [hoveredCategory, setHoveredCategory] = useState(null);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100
      }
    }
  };

  if (categories.length === 0) {
    return (
      <div className="bg-gray-50 rounded-2xl p-8 text-center">
        <div className="w-16 h-16 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
          <ShoppingBagIcon className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No categories available</h3>
        <p className="text-gray-600">Categories will appear here soon!</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-500 to-purple-600 p-6 text-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <motion.div
              animate={{ 
                rotate: [0, 10, -10, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{ 
                duration: 2, 
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <SparklesIcon className="w-8 h-8" />
            </motion.div>
            <div>
              <h2 className="text-2xl font-bold">{title}</h2>
              <p className="text-white/80">Discover amazing products in every category</p>
            </div>
          </div>
          
          <Link
            to="/products"
            className="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-all duration-300 backdrop-blur-sm flex items-center space-x-2"
          >
            <span>View All</span>
            <ArrowRightIcon className="w-4 h-4" />
          </Link>
        </div>
      </div>

      {/* Categories Grid */}
      <div className="p-6">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4"
        >
          {categories.map((category, index) => (
            <motion.div
              key={category.id}
              variants={itemVariants}
              onHoverStart={() => setHoveredCategory(category.id)}
              onHoverEnd={() => setHoveredCategory(null)}
              className="group"
            >
              <Link
                to={`/products?category=${category.slug}`}
                className="block"
              >
                <motion.div
                  whileHover={{ y: -5 }}
                  whileTap={{ scale: 0.95 }}
                  className="relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 text-center transition-all duration-300 hover:shadow-lg border border-gray-200 hover:border-indigo-300"
                >
                  {/* Background Pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute inset-0 bg-gradient-to-br from-black/5 to-black/10" />
                  </div>

                  {/* Category Icon */}
                  <motion.div
                    animate={hoveredCategory === category.id ? {
                      scale: [1, 1.2, 1],
                      rotate: [0, 10, -10, 0]
                    } : {}}
                    transition={{ duration: 0.5 }}
                    className="relative z-10 mb-4"
                  >
                    <div className="w-16 h-16 mx-auto bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center text-2xl group-hover:from-indigo-200 group-hover:to-purple-200 transition-all duration-300">
                      {category.icon || '📦'}
                    </div>
                  </motion.div>

                  {/* Category Name */}
                  <h3 className="relative z-10 font-semibold text-gray-900 mb-2 group-hover:text-indigo-600 transition-colors">
                    {category.name}
                  </h3>

                  {/* Product Count */}
                  <p className="relative z-10 text-sm text-gray-500 mb-3">
                    {category.product_count || 0} products
                  </p>

                  {/* Trending Badge */}
                  <AnimatePresence>
                    {category.is_trending && (
                      <motion.div
                        initial={{ scale: 0, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0, opacity: 0 }}
                        className="absolute top-2 right-2 bg-orange-100 text-orange-600 px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1"
                      >
                        <ArrowTrendingUpIcon className="w-3 h-3" />
                        <span>Hot</span>
                      </motion.div>
                    )}
                  </AnimatePresence>

                  {/* Hover Effect */}
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={hoveredCategory === category.id ? { scale: 1 } : { scale: 0 }}
                    className="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-purple-500/10 rounded-xl"
                  />

                  {/* Action Arrow */}
                  <motion.div
                    initial={{ x: -10, opacity: 0 }}
                    animate={hoveredCategory === category.id ? { x: 0, opacity: 1 } : { x: -10, opacity: 0 }}
                    className="absolute bottom-3 right-3 text-indigo-500"
                  >
                    <ArrowRightIcon className="w-4 h-4" />
                  </motion.div>
                </motion.div>
              </Link>
            </motion.div>
          ))}
        </motion.div>

        {/* Featured Categories */}
        {categories.some(cat => cat.is_featured) && (
          <div className="mt-8 pt-6 border-t border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <SparklesIcon className="w-5 h-5 mr-2 text-yellow-500" />
              Featured Categories
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {categories
                .filter(cat => cat.is_featured)
                .slice(0, 3)
                .map((category, index) => (
                  <motion.div
                    key={category.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Link
                      to={`/products?category=${category.slug}`}
                      className="block bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-4 hover:from-indigo-100 hover:to-purple-100 transition-all duration-300 border border-indigo-200"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center text-white text-lg">
                          {category.icon || '⭐'}
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900">{category.name}</h4>
                          <p className="text-sm text-gray-600">{category.product_count || 0} products</p>
                        </div>
                        <ArrowRightIcon className="w-5 h-5 text-indigo-500" />
                      </div>
                    </Link>
                  </motion.div>
                ))}
            </div>
          </div>
        )}

        {/* Stats */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center space-x-4">
              <span>{categories.length} categories</span>
              <span>•</span>
              <span>{categories.reduce((sum, cat) => sum + (cat.product_count || 0), 0)} total products</span>
            </div>
            <div className="flex items-center space-x-2">
              <span>Updated daily</span>
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CategoryShowcase;
