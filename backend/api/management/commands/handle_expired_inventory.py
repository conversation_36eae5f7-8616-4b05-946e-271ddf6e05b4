"""
Management command to handle expired inventory batches.

This command can be run daily via cron job to:
1. Identify expired inventory batches
2. Remove expired stock from inventory
3. Generate reports on expired items
4. Send notifications about expiring items
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from api.models import InventoryBatch, InventoryChange, Product, InventoryAlert
from django.contrib.auth import get_user_model

User = get_user_model()


class Command(BaseCommand):
    help = 'Handle expired inventory batches and generate expiry reports'

    def add_arguments(self, parser):
        parser.add_argument(
            '--remove-expired',
            action='store_true',
            help='Remove expired inventory from stock',
        )
        parser.add_argument(
            '--warning-days',
            type=int,
            default=7,
            help='Days before expiry to show warnings (default: 7)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        today = timezone.now().date()
        warning_date = today + timedelta(days=options['warning_days'])

        self.stdout.write(f'Checking inventory expiry for {today}...')
        self.stdout.write('')

        # Find expired batches
        expired_batches = InventoryBatch.objects.filter(
            expiry_date__lt=today,
            quantity_remaining__gt=0,
            is_active=True
        )

        # Find batches expiring soon
        expiring_soon_batches = InventoryBatch.objects.filter(
            expiry_date__gte=today,
            expiry_date__lte=warning_date,
            quantity_remaining__gt=0,
            is_active=True
        )

        # Report expired batches
        if expired_batches.exists():
            self.stdout.write(
                self.style.ERROR(f'Found {expired_batches.count()} expired batches:')
            )
            
            total_expired_value = 0
            for batch in expired_batches:
                expired_value = batch.quantity_remaining * batch.cost_per_unit
                total_expired_value += expired_value
                
                self.stdout.write(
                    f'  - {batch.product.title} (Batch: {batch.batch_number})'
                )
                self.stdout.write(
                    f'    Expired: {batch.expiry_date}, Remaining: {batch.quantity_remaining} units'
                )
                self.stdout.write(
                    f'    Value: ${expired_value:.2f}'
                )
                self.stdout.write('')

            self.stdout.write(
                self.style.ERROR(f'Total expired inventory value: ${total_expired_value:.2f}')
            )
            self.stdout.write('')

            # Remove expired inventory if requested
            if options['remove_expired']:
                if options['dry_run']:
                    self.stdout.write(
                        self.style.WARNING('DRY RUN: Would remove expired inventory')
                    )
                else:
                    self.remove_expired_inventory(expired_batches)

        else:
            self.stdout.write(
                self.style.SUCCESS('No expired batches found!')
            )

        # Report batches expiring soon and create alerts
        if expiring_soon_batches.exists():
            self.stdout.write(
                self.style.WARNING(
                    f'Found {expiring_soon_batches.count()} batches expiring within {options["warning_days"]} days:'
                )
            )

            for batch in expiring_soon_batches:
                days_until_expiry = (batch.expiry_date - today).days
                self.stdout.write(
                    f'  - {batch.product.title} (Batch: {batch.batch_number})'
                )
                self.stdout.write(
                    f'    Expires in {days_until_expiry} days ({batch.expiry_date})'
                )
                self.stdout.write(
                    f'    Remaining: {batch.quantity_remaining} units'
                )

                # Create expiry alert
                if not options['dry_run']:
                    alert = InventoryAlert.create_expiry_alert(batch, days_until_expiry)
                    self.stdout.write(
                        f'    Created alert: {alert.title}'
                    )
                else:
                    self.stdout.write(
                        f'    DRY RUN: Would create expiry alert'
                    )

                self.stdout.write('')
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'No batches expiring within {options["warning_days"]} days!'
                )
            )

        # Create alerts for expired batches
        if expired_batches.exists() and not options['dry_run']:
            self.stdout.write('Creating alerts for expired batches...')
            for batch in expired_batches:
                days_until_expiry = (batch.expiry_date - today).days
                alert = InventoryAlert.create_expiry_alert(batch, days_until_expiry)
                self.stdout.write(f'  Created alert: {alert.title}')

        # Generate summary statistics
        self.generate_summary_stats()

    def remove_expired_inventory(self, expired_batches):
        """Remove expired inventory and create inventory change records"""
        try:
            # Get or create a system user for automated operations
            system_user, created = User.objects.get_or_create(
                username='system',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'System',
                    'last_name': 'User',
                    'is_active': False
                }
            )

            removed_count = 0
            total_removed_value = 0

            for batch in expired_batches:
                if batch.quantity_remaining > 0:
                    # Calculate removed value
                    removed_value = batch.quantity_remaining * batch.cost_per_unit
                    total_removed_value += removed_value

                    # Create inventory change record
                    InventoryChange.objects.create(
                        product=batch.product,
                        batch=batch,
                        quantity_change=-batch.quantity_remaining,
                        change_type='expiry',
                        previous_quantity=batch.quantity_remaining,
                        new_quantity=0,
                        changed_by=system_user,
                        notes=f'Automatic removal of expired inventory. Batch expired on {batch.expiry_date}'
                    )

                    # Update product stock quantity
                    batch.product.stock_quantity -= batch.quantity_remaining
                    batch.product.save(update_fields=['stock_quantity', 'updated_at'])

                    # Mark batch as empty
                    batch.quantity_remaining = 0
                    batch.save(update_fields=['quantity_remaining'])

                    removed_count += 1

                    self.stdout.write(
                        f'Removed {batch.quantity_remaining} units from {batch.product.title} '
                        f'(Batch: {batch.batch_number})'
                    )

            self.stdout.write('')
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully removed {removed_count} expired batches '
                    f'with total value of ${total_removed_value:.2f}'
                )
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error removing expired inventory: {str(e)}')
            )

    def generate_summary_stats(self):
        """Generate summary statistics about inventory"""
        self.stdout.write('')
        self.stdout.write('=== INVENTORY SUMMARY ===')

        # Total active batches
        active_batches = InventoryBatch.objects.filter(
            is_active=True,
            quantity_remaining__gt=0
        )
        
        total_batches = active_batches.count()
        total_inventory_value = sum(
            batch.quantity_remaining * batch.cost_per_unit 
            for batch in active_batches
        )

        self.stdout.write(f'Total active batches: {total_batches}')
        self.stdout.write(f'Total inventory value: ${total_inventory_value:.2f}')

        # Low stock products
        low_stock_products = Product.objects.filter(stock_quantity__lt=10)
        self.stdout.write(f'Products with low stock (<10 units): {low_stock_products.count()}')

        # Products with no inventory
        no_stock_products = Product.objects.filter(stock_quantity=0)
        self.stdout.write(f'Products out of stock: {no_stock_products.count()}')

        self.stdout.write('')
        self.stdout.write('Inventory check completed!')
