import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import Layout from '../components/Layout.jsx';
import { productService, adminService } from '../services/api';

const ProductModerationPage = () => {
  const [pendingProducts, setPendingProducts] = useState([]);
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      // Products awaiting moderation: inactive products
      const productsRes = await productService.fetchFilteredProducts('is_active=false');
      setPendingProducts(productsRes.data?.results || productsRes.data || []);
      // Recent moderation logs (optional)
      try {
        const logsRes = await adminService.fetchModerationQueue();
        setLogs(logsRes.data?.results || logsRes.data || []);
      } catch (_) {}
    } catch (e) {
      console.error(e);
      setError('Failed to load moderation data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const onAction = async (productId, action) => {
    try {
      setLoading(true);
      await adminService.takeModerationAction({ product_id: productId, action });
      await loadData();
    } catch (e) {
      console.error(e);
      setError('Action failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      {/* Main Content */}
      <div className="container mx-auto py-12 px-4">
        <h1 className="text-3xl font-bold mb-8">Product Moderation</h1>

        {error && (
          <div className="mb-4 rounded bg-red-50 p-3 text-red-700">{error}</div>
        )}

        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Pending Products</h2>
          {loading && <div className="text-gray-500">Loading...</div>}
          {!loading && pendingProducts.length === 0 && (
            <div className="text-gray-500">No pending products to review.</div>
          )}
          {!loading && pendingProducts.length > 0 && (
            <table className="min-w-full bg-white border border-gray-200">
              <thead>
                <tr>
                  <th className="py-2 px-4 border">Title</th>
                  <th className="py-2 px-4 border">Category</th>
                  <th className="py-2 px-4 border">Price</th>
                  <th className="py-2 px-4 border">Actions</th>
                </tr>
              </thead>
              <tbody>
                {pendingProducts.map((p) => (
                  <tr key={p.id}>
                    <td className="py-2 px-4 border">{p.title}</td>
                    <td className="py-2 px-4 border">{p.category?.name || p.category || '-'}</td>
                    <td className="py-2 px-4 border">{p.price}</td>
                    <td className="py-2 px-4 border">
                      <button
                        className="text-green-600 hover:underline"
                        onClick={() => onAction(p.id, 'approve')}
                      >
                        Approve
                      </button>
                      <button
                        className="ml-4 text-red-600 hover:underline"
                        onClick={() => onAction(p.id, 'reject')}
                      >
                        Reject
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Recent Moderation Activity</h2>
          {logs.length === 0 ? (
            <div className="text-gray-500">No recent moderation activity.</div>
          ) : (
            <ul className="divide-y divide-gray-200">
              {logs.slice(0, 10).map((log) => (
                <li key={log.id} className="py-2 text-sm text-gray-700">
                  <span className="font-medium">{log.action}</span> — {log.product?.title || log.product} by {log.moderator?.email || log.moderator || 'unknown'}
                </li>
              ))}
            </ul>
          )}
        </div>

        <div className="mt-8">
          <Link to="/admin" className="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 inline-block">
            Back to Admin Dashboard
          </Link>
        </div>
      </div>
    </Layout>
  );
};

export default ProductModerationPage;
