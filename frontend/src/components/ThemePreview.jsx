/**
 * Theme Preview Component
 * 
 * Shows a preview of how different UI elements look with the current theme
 * Useful for testing theme changes before applying them
 */
import React from 'react';

import {
  ShoppingBagIcon,
  StarIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

const ThemePreview = () => {
  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-primary-700 mb-2">Theme Preview</h1>
        <p className="text-secondary-600">See how your eCommerce platform looks with the current theme</p>
      </div>

      {/* Navigation Bar Preview */}
      <div className="bg-white shadow-md rounded-lg p-4">
        <h2 className="text-xl font-semibold text-secondary-800 mb-4">Navigation Bar</h2>
        <div className="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg">
          <div className="text-2xl font-bold text-primary-700">MyEcommerce</div>
          <div className="flex space-x-6">
            <span className="text-secondary-600 hover:text-primary-600 cursor-pointer">Home</span>
            <span className="text-secondary-600 hover:text-primary-600 cursor-pointer">Products</span>
            <span className="text-secondary-600 hover:text-primary-600 cursor-pointer">Contact</span>
          </div>
          <div className="flex items-center space-x-4">
            <div className="relative">
              <ShoppingBagIcon className="w-6 h-6 text-secondary-600" />
              <span className="absolute -top-2 -right-2 bg-primary-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
            </div>
          </div>
        </div>
      </div>

      {/* Buttons Preview */}
      <div className="bg-white shadow-md rounded-lg p-6">
        <h2 className="text-xl font-semibold text-secondary-800 mb-4">Buttons</h2>
        <div className="flex flex-wrap gap-4">
          <button className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors">
            Primary Button
          </button>
          <button className="bg-secondary-600 text-white px-6 py-2 rounded-lg hover:bg-secondary-700 transition-colors">
            Secondary Button
          </button>
          <button className="bg-accent-600 text-white px-6 py-2 rounded-lg hover:bg-accent-700 transition-colors">
            Accent Button
          </button>
          <button className="border-2 border-primary-600 text-primary-600 px-6 py-2 rounded-lg hover:bg-primary-50 transition-colors">
            Outline Button
          </button>
        </div>
      </div>

      {/* Product Card Preview */}
      <div className="bg-white shadow-md rounded-lg p-6">
        <h2 className="text-xl font-semibold text-secondary-800 mb-4">Product Card</h2>
        <div className="max-w-sm bg-white border border-gray-200 rounded-lg shadow-soft overflow-hidden">
          <div className="h-48 bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center">
            <span className="text-primary-600 text-lg font-medium">Product Image</span>
          </div>
          <div className="p-4">
            <h3 className="text-lg font-semibold text-secondary-800 mb-2">Sample Product</h3>
            <div className="flex items-center mb-2">
              <div className="flex text-accent-500">
                {[...Array(5)].map((_, i) => (
                  <StarIconSolid key={i} className="w-4 h-4" />
                ))}
              </div>
              <span className="text-sm text-secondary-600 ml-2">(24 reviews)</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-2xl font-bold text-primary-700">$99.99</span>
              <button className="bg-primary-600 text-white p-2 rounded-lg hover:bg-primary-700 transition-colors">
                <ShoppingBagIcon className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Form Elements Preview */}
      <div className="bg-white shadow-md rounded-lg p-6">
        <h2 className="text-xl font-semibold text-secondary-800 mb-4">Form Elements</h2>
        <div className="space-y-4 max-w-md">
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">Email Address</label>
            <input 
              type="email" 
              placeholder="Enter your email"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">Category</label>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
              <option>Electronics</option>
              <option>Clothing</option>
              <option>Home & Garden</option>
            </select>
          </div>
          <div className="flex items-center">
            <input 
              type="checkbox" 
              className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
            />
            <label className="ml-2 text-sm text-secondary-700">Subscribe to newsletter</label>
          </div>
        </div>
      </div>

      {/* Alert Messages Preview */}
      <div className="bg-white shadow-md rounded-lg p-6">
        <h2 className="text-xl font-semibold text-secondary-800 mb-4">Alert Messages</h2>
        <div className="space-y-4">
          <div className="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg flex items-center">
            <CheckIcon className="w-5 h-5 mr-2" />
            Success! Your order has been placed successfully.
          </div>
          <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg flex items-center">
            <XMarkIcon className="w-5 h-5 mr-2" />
            Error! Please check your payment information.
          </div>
          <div className="bg-primary-50 border border-primary-200 text-primary-800 px-4 py-3 rounded-lg flex items-center">
            <StarIcon className="w-5 h-5 mr-2" />
            Info: New products have been added to your wishlist.
          </div>
        </div>
      </div>

      {/* Color Palette Display */}
      <div className="bg-white shadow-md rounded-lg p-6">
        <h2 className="text-xl font-semibold text-secondary-800 mb-4">Current Color Palette</h2>
        <div className="grid grid-cols-3 gap-6">
          <div>
            <h3 className="font-medium text-secondary-700 mb-2">Primary Colors</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary-500 rounded border"></div>
                <span className="text-sm text-secondary-600">Primary 500</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary-600 rounded border"></div>
                <span className="text-sm text-secondary-600">Primary 600</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary-700 rounded border"></div>
                <span className="text-sm text-secondary-600">Primary 700</span>
              </div>
            </div>
          </div>
          <div>
            <h3 className="font-medium text-secondary-700 mb-2">Secondary Colors</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-secondary-500 rounded border"></div>
                <span className="text-sm text-secondary-600">Secondary 500</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-secondary-600 rounded border"></div>
                <span className="text-sm text-secondary-600">Secondary 600</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-secondary-700 rounded border"></div>
                <span className="text-sm text-secondary-600">Secondary 700</span>
              </div>
            </div>
          </div>
          <div>
            <h3 className="font-medium text-secondary-700 mb-2">Accent Colors</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-accent-500 rounded border"></div>
                <span className="text-sm text-secondary-600">Accent 500</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-accent-600 rounded border"></div>
                <span className="text-sm text-secondary-600">Accent 600</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-accent-700 rounded border"></div>
                <span className="text-sm text-secondary-600">Accent 700</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThemePreview;
