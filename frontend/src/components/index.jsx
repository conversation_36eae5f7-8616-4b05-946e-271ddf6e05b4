// Layout components
export { default as Layout } from './Layout.jsx';
export { default as Navbar } from './Navbar.jsx';
export { default as Footer } from './Footer.jsx';
export { default as Hero } from './Hero.jsx';

// Product components
export { default as ProductCard } from './ProductCard.jsx';

// Authentication components
export { default as PrivateRoute } from './PrivateRoute.jsx';

/**
 * This index file exports all components from a single location.
 * This makes importing components easier and more consistent.
 *
 * Example usage:
 * import { Layout, Navbar, ProductCard } from '../components';
 *
 * Instead of:
 * import Layout from '../components/Layout.jsx';
 * import Navbar from '../components/Navbar.jsx';
 * import ProductCard from '../components/ProductCard.jsx';
 */
