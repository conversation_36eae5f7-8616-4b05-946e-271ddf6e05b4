import React, { createContext, useState, useEffect, useContext, useMemo, useCallback } from 'react';
import { cartService } from '../services/api';
import { useAuth } from './AuthContext.jsx';

const CartContext = createContext();

export const useCart = () => useContext(CartContext);

export const CartProvider = ({ children }) => {
  const [cart, setCart] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { currentUser } = useAuth();

  useEffect(() => {
    if (currentUser) {
      fetchCart();
    } else {
      setCart(null);
    }
  }, [currentUser]);

  const fetchCart = async () => {
    try {
      setLoading(true);
      const response = await cartService.getCart();
      const data = response.data;
      const carts = Array.isArray(data) ? data : (data?.results || []);
      setCart(carts.length > 0 ? carts[0] : null);
      setError(null);
    } catch (err) {
      setError('Failed to fetch cart');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = async (productId, quantity = 1) => {
    try {
      setLoading(true);

      // If no cart exists, create one first
      if (!cart) {
        const cartResponse = await cartService.getCart();
        if (cartResponse.data.length === 0) {
          // Create a new cart
          const newCartResponse = await cartService.createCart();
          setCart(newCartResponse.data);

          // Add item to the new cart
          const response = await cartService.addToCart(newCartResponse.data.id, productId, quantity);
          setCart(response.data);
        } else {
          setCart(cartResponse.data[0]);

          // Add item to the existing cart
          const response = await cartService.addToCart(cartResponse.data[0].id, productId, quantity);
          setCart(response.data);
        }
      } else {
        // Add item to the existing cart
        const response = await cartService.addToCart(cart.id, productId, quantity);
        setCart(response.data);
      }

      setError(null);
    } catch (err) {
      setError('Failed to add item to cart');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const removeFromCart = async (itemId) => {
    if (!cart) return;

    try {
      setLoading(true);
      const response = await cartService.removeFromCart(cart.id, itemId);
      setCart(response.data);
      setError(null);
    } catch (err) {
      setError('Failed to remove item from cart');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const updateCartItem = async (itemId, quantity) => {
    if (!cart) return;

    try {
      setLoading(true);
      const response = await cartService.updateCartItem(cart.id, itemId, quantity);
      setCart(response.data);
      setError(null);
    } catch (err) {
      setError('Failed to update cart item');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const checkout = async () => {
    if (!cart) return;

    try {
      setLoading(true);
      const response = await cartService.checkout(cart.id);
      setCart(null);
      setError(null);
      return response.data;
    } catch (err) {
      setError('Checkout failed');
      console.error(err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Derived helpers and summaries

  const isEmpty = useCallback(() => {
    const lines = cart?.lines || cart?.items || [];
    return !lines || lines.length === 0;
  }, [cart]);

  const cartSummary = useMemo(() => {
    const lines = cart?.lines || cart?.items || [];
    const itemCount = lines.reduce((sum, line) => sum + (line?.quantity || 0), 0);
    const total = lines.reduce((sum, line) => {
      const amount =
        (line?.totalPrice?.gross?.amount ??
         line?.total_price?.gross?.amount ??
         line?.total ??
         ((line?.unitPrice?.gross?.amount ?? line?.price ?? 0) * (line?.quantity ?? 1)));
      return sum + (Number(amount) || 0);
    }, 0);
    return {
      itemCount,
      totalPrice: Number(total).toFixed(2),
    };
  }, [cart]);

  const isUpdating = loading;
  const updateQuantity = updateCartItem;

  const clearCart = async () => {
    try {
      if (!cart) return;
      setLoading(true);
      await cartService.clearCart();
      await fetchCart();
      setError(null);
    } catch (err) {
      setError('Failed to clear cart');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const value = {
    cart,
    loading,
    error,
    fetchCart,
    addToCart,
    removeFromCart,
    updateCartItem,
    updateQuantity,
    clearCart,
    isEmpty,
    cartSummary,
    isUpdating,
    checkout,
  };

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
};

export default CartContext;
