import React, { useEffect, useState } from 'react';
import { fetchRecommendations } from '../../services/api'; // Fetch from API
import ProductCard from '../ProductCard.jsx';

const UserRecommendations = ({ userId }) => {
  const [recommendations, setRecommendations] = useState([]);

  useEffect(() => {
    const getRecommendations = async () => {
      const response = await fetchRecommendations(userId);
      setRecommendations(response.data);
    };
    getRecommendations();
  }, [userId]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
      {recommendations.map((product) => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  );
};

export default UserRecommendations;
