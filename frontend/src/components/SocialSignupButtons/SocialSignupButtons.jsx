import React from 'react';


const SocialSignupButtons = () => {
  const handleSocialSignup = async (provider) => {
    try {
      if (provider === 'google') {
        console.warn('Use Google Sign-Up flow to obtain an idToken, then call authService.googleLogin(idToken).');
      } else {
        console.warn(`Provider ${provider} is not supported yet.`);
      }
    } catch (error) {
      console.error('Social signup error:', error);
    }
  };

  return (
    <div className="space-y-4">
      <button
        onClick={() => handleSocialSignup('google')}
        className="btn-outline w-full"
      >
        Sign Up with Google
      </button>
      
      <button
        onClick={() => handleSocialSignup('facebook')}
        className="btn-outline w-full"
      >
        Sign Up with Facebook
      </button>
    </div>
  );
};

export default SocialSignupButtons;
