import React from 'react';
import { Link } from 'react-router-dom';
import Layout from '../components/Layout.jsx';

const CartPage = () => {
  // Mock cart items
  const cartItems = [
    {
      id: 1,
      name: 'Premium Wireless Headphones',
      price: 199.99,
      quantity: 1
    },
    {
      id: 3,
      name: 'Bluetooth Speaker',
      price: 129.99,
      quantity: 2
    }
  ];

  // Calculate totals
  const subtotal = cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  const shipping = 9.99;
  const tax = subtotal * 0.08;
  const total = subtotal + shipping + tax;

  return (
    <Layout>
      {/* Cart Content */}
      <div className="container mx-auto py-12 px-4">
        <h1 className="text-3xl font-bold mb-8">Your Cart</h1>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Cart items list */}
          <div className="lg:col-span-2">
            {cartItems.length > 0 ? (
              cartItems.map(item => (
                <div key={item.id} className="bg-white rounded-lg shadow-md p-6 mb-4 flex flex-col md:flex-row items-center">
                  <div className="w-24 h-24 bg-gray-300 rounded-md flex-shrink-0"></div>
                  <div className="md:ml-6 flex-grow mt-4 md:mt-0">
                    <h3 className="text-lg font-semibold">{item.name}</h3>
                    <p className="text-gray-600">${item.price.toFixed(2)}</p>
                  </div>
                  <div className="flex items-center mt-4 md:mt-0">
                    <button className="bg-gray-200 px-3 py-1 rounded-l">-</button>
                    <span className="bg-white px-4 py-1 border-t border-b">{item.quantity}</span>
                    <button className="bg-gray-200 px-3 py-1 rounded-r">+</button>
                    <button className="ml-4 text-red-500 hover:text-red-700">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <div className="bg-white rounded-lg shadow-md p-8 text-center">
                <h2 className="text-xl font-semibold mb-2">Your cart is empty</h2>
                <p className="text-gray-600 mb-4">Looks like you haven't added any products to your cart yet.</p>
                <Link to="/products" className="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 inline-block">
                  Start Shopping
                </Link>
              </div>
            )}
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Order Summary</h2>
              <div className="border-t border-b py-4">
                <div className="flex justify-between mb-2">
                  <span>Subtotal</span>
                  <span>${subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between mb-2">
                  <span>Shipping</span>
                  <span>${shipping.toFixed(2)}</span>
                </div>
                <div className="flex justify-between mb-2">
                  <span>Tax</span>
                  <span>${tax.toFixed(2)}</span>
                </div>
              </div>
              <div className="flex justify-between font-semibold text-lg mt-4">
                <span>Total</span>
                <span>${total.toFixed(2)}</span>
              </div>

              {/* Promo Code */}
              <div className="mt-6">
                <label className="block text-gray-700 mb-2">Promo Code</label>
                <div className="flex">
                  <input type="text" className="border rounded-l px-4 py-2 w-full" placeholder="Enter code" />
                  <button className="bg-gray-200 text-gray-700 px-4 py-2 rounded-r">Apply</button>
                </div>
              </div>

              {/* Checkout Button */}
              <Link to="/checkout" className="bg-blue-500 text-white py-3 px-6 rounded-lg mt-6 block text-center hover:bg-blue-600">
                Proceed to Checkout
              </Link>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default CartPage;
