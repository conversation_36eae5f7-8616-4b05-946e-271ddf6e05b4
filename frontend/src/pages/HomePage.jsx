import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

import toast from 'react-hot-toast';


// Import components
import Layout from '../components/Layout.jsx';
import Hero from '../components/Hero.jsx';

import ProductShowcase from '../components/ProductShowcase/ProductShowcase.jsx';
import CategoryShowcase from '../components/CategoryShowcase/CategoryShowcase.jsx';
import FeaturesSection from '../components/FeaturesSection/FeaturesSection.jsx';

// Import API services
import { productService } from '../services/api';
import { useAnalytics } from '../contexts/AnalyticsContext.jsx';

const HomePage = ({ userId }) => {
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { trackProductImpressions } = useAnalytics();

  // Fetch data from API
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch products
        const productsResponse = await productService.fetchProducts();

        // Map products to the format expected by ProductCard
        const mappedProducts = productsResponse.data.results.map(product => ({
          id: product.id,
          name: product.title,
          price: parseFloat(product.price),
          rating: 4.5, // Default rating since we don't have this in the API yet
          reviewCount: 10, // Default review count
          imageSrc: product.picture,
          discount: product.discount_type !== null,
          discountPercentage: product.discount_value > 0 ? parseFloat(product.discount_value) : 0,
          category: product.category.name
        }));
        setProducts(mappedProducts);

        // Fetch categories
        const categoriesResponse = await productService.fetchCategories();

        // Map categories with icons
        const categoryIcons = {
          'Electronics': '🖥️',
          'Clothing': '👕',
          'Home & Kitchen': '🏠',
          'Books': '📚',
          'Sports & Outdoors': '⚽'
        };

        const mappedCategories = categoriesResponse.data.results.map(category => ({
          id: category.id,
          name: category.name,
          icon: categoryIcons[category.name] || '📦',
          href: `/products?category=${category.slug}`
        }));
        setCategories(mappedCategories);



        setLoading(false);

        // Track product impressions for featured products
        if (mappedProducts.length > 0) {
          trackProductImpressions(mappedProducts.slice(0, 8), 'home_featured');
        }

      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again later.');
        setLoading(false);
        toast.error('Failed to load data. Please try again later.');
      }
    };

    fetchData();
  }, [trackProductImpressions]);

  return (
    <Layout userId={userId}>
      {/* Main Content */}
        {/* Hero Section */}
        <Hero />

        {/* Enhanced Features Section */}
        <FeaturesSection />

        {/* Enhanced Featured Products */}
        <section className="py-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {!loading && !error && (
              <ProductShowcase
                title="Featured Products"
                products={products.slice(0, 8)}
                variant="featured"
                showViewAll={true}
              />
            )}
            {loading && (
              <div className="flex justify-center py-12">
                <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-primary-600"></div>
              </div>
            )}
            {error && (
              <div className="rounded-lg bg-red-50 p-4 text-center text-red-800">
                {error}
              </div>
            )}
          </div>
        </section>

        {/* Enhanced Categories */}
        <section className="py-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {!loading && !error && (
              <CategoryShowcase
                categories={categories}
                title="Shop by Category"
              />
            )}
            {loading && (
              <div className="flex justify-center py-12">
                <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-primary-600"></div>
              </div>
            )}
            {error && (
              <div className="rounded-lg bg-red-50 p-4 text-center text-red-800">
                {error}
              </div>
            )}
          </div>
        </section>

        {/* Special Offers - Sale Products */}
        <section className="py-16 bg-gray-50">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {!loading && !error && (
              <ProductShowcase
                title="Special Offers"
                products={products.filter(p => p.discount).slice(0, 8)}
                variant="sale"
                showViewAll={true}
              />
            )}
            {loading && (
              <div className="flex justify-center py-12">
                <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-primary-600"></div>
              </div>
            )}
            {error && (
              <div className="rounded-lg bg-red-50 p-4 text-center text-red-800">
                {error}
              </div>
            )}
          </div>
        </section>

        {/* Trending Products */}
        <section className="py-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {!loading && !error && (
              <ProductShowcase
                title="Trending Now"
                products={products.slice(8, 16)}
                variant="trending"
                showViewAll={true}
              />
            )}
          </div>
        </section>

        {/* Newsletter */}
        <section className="bg-white py-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="rounded-2xl bg-primary-700 px-6 py-12 sm:px-12 sm:py-16">
              <div className="mx-auto max-w-3xl text-center">
                <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                  Stay updated with our newsletter
                </h2>
                <p className="mx-auto mt-4 max-w-xl text-lg text-primary-100">
                  Get the latest product updates, offers, and news delivered straight to your inbox.
                </p>
                <form className="mt-8 sm:mx-auto sm:max-w-md">
                  <div className="flex">
                    <label htmlFor="email-address" className="sr-only">
                      Email address
                    </label>
                    <input
                      id="email-address"
                      name="email"
                      type="email"
                      autoComplete="email"
                      required
                      className="w-full min-w-0 rounded-l-md border-0 px-4 py-3 text-base text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                      placeholder="Enter your email"
                    />
                    <button
                      type="submit"
                      className="flex-shrink-0 rounded-r-md bg-primary-900 px-6 py-3 text-base font-medium text-white shadow-sm hover:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                    >
                      Subscribe
                    </button>
                  </div>
                  <p className="mt-3 text-sm text-primary-200">
                    We care about your data. Read our{' '}
                    <Link to="/privacy" className="font-medium text-white underline">
                      Privacy Policy
                    </Link>
                    .
                  </p>
                </form>
              </div>
            </div>
          </div>
        </section>
    </Layout>
  );
};

export default HomePage;
