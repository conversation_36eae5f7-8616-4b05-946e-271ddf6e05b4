import React, { useState } from 'react';
import { useAnalytics } from '../contexts/AnalyticsContext.jsx';
import { runAllAnalyticsTests } from '../test-analytics';

const AnalyticsTestPanel = () => {
  const [testResults, setTestResults] = useState('');
  const [isRunning, setIsRunning] = useState(false);
  const analyticsContext = useAnalytics();

  const runTests = async () => {
    setIsRunning(true);
    setTestResults('Running analytics tests...\n');
    
    // Capture console output
    const originalLog = console.log;
    const originalError = console.error;
    const originalWarn = console.warn;
    
    let output = '';
    
    console.log = (...args) => {
      output += args.join(' ') + '\n';
      originalLog(...args);
    };
    
    console.error = (...args) => {
      output += 'ERROR: ' + args.join(' ') + '\n';
      originalError(...args);
    };
    
    console.warn = (...args) => {
      output += 'WARNING: ' + args.join(' ') + '\n';
      originalWarn(...args);
    };
    
    try {
      await runAllAnalyticsTests(analyticsContext);
    } catch (error) {
      output += `\nTest execution error: ${error.message}\n`;
    }
    
    // Restore console
    console.log = originalLog;
    console.error = originalError;
    console.warn = originalWarn;
    
    setTestResults(output);
    setIsRunning(false);
  };

  const testProductImpression = () => {
    const testProduct = {
      id: 'test-product-123',
      title: 'Test Product for Analytics',
      name: 'Test Product for Analytics',
      price: 49.99,
      category: 'Test Category'
    };
    
    analyticsContext.trackProductImpressions([testProduct], 'test_panel');
    setTestResults(prev => prev + '\n✅ Product impression tracked for test product\n');
  };

  const testAddToCart = () => {
    const testProduct = {
      id: 'test-product-456',
      title: 'Test Cart Product',
      name: 'Test Cart Product',
      price: 29.99,
      category: 'Test Category'
    };
    
    analyticsContext.trackAddToCartAction(testProduct, 2, 'test_panel');
    setTestResults(prev => prev + '\n🛒 Add to cart tracked for test product (quantity: 2)\n');
  };

  const testSearch = () => {
    analyticsContext.trackSearchAction('test analytics search', 10);
    setTestResults(prev => prev + '\n🔍 Search tracked: "test analytics search" with 10 results\n');
  };

  const testPurchase = () => {
    const testOrder = {
      id: 'test-order-' + Date.now(),
      total_price: 99.98,
      items: [
        { product: { id: '1', title: 'Product 1' }, quantity: 1, price: 49.99 },
        { product: { id: '2', title: 'Product 2' }, quantity: 1, price: 49.99 }
      ],
      payment_method: 'test',
      shipping_cost: 0,
      tax_amount: 0
    };
    
    analyticsContext.trackPurchaseAction(testOrder);
    setTestResults(prev => prev + '\n💰 Purchase tracked for test order: ' + testOrder.id + '\n');
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold text-gray-900 mb-4">Analytics Testing Panel</h2>
      <p className="text-gray-600 mb-6">
        Use this panel to test the analytics implementation. Check your browser console and 
        Google Analytics dashboard for tracked events.
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-800">Individual Tests</h3>
          
          <button
            onClick={testProductImpression}
            className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
          >
            Test Product Impression
          </button>
          
          <button
            onClick={testAddToCart}
            className="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors"
          >
            Test Add to Cart
          </button>
          
          <button
            onClick={testSearch}
            className="w-full bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 transition-colors"
          >
            Test Search Tracking
          </button>
          
          <button
            onClick={testPurchase}
            className="w-full bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600 transition-colors"
          >
            Test Purchase Tracking
          </button>
        </div>
        
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-800">Complete Test Suite</h3>
          
          <button
            onClick={runTests}
            disabled={isRunning}
            className={`w-full px-4 py-2 rounded transition-colors ${
              isRunning 
                ? 'bg-gray-400 text-gray-200 cursor-not-allowed' 
                : 'bg-red-500 text-white hover:bg-red-600'
            }`}
          >
            {isRunning ? 'Running Tests...' : 'Run All Analytics Tests'}
          </button>
          
          <div className="text-sm text-gray-600">
            <p>This will test:</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Google Analytics configuration</li>
              <li>Event tracking functions</li>
              <li>Backend API endpoints</li>
              <li>Analytics context integration</li>
            </ul>
          </div>
        </div>
      </div>
      
      <div className="border-t pt-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-3">Test Results</h3>
        <div className="bg-gray-100 p-4 rounded-lg">
          <pre className="text-sm text-gray-800 whitespace-pre-wrap max-h-96 overflow-y-auto">
            {testResults || 'No tests run yet. Click a button above to start testing.'}
          </pre>
        </div>
      </div>
      
      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-semibold text-blue-800 mb-2">How to Verify Analytics:</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Check browser console for tracking confirmations</li>
          <li>• Open Google Analytics Real-time reports to see events</li>
          <li>• Visit /analytics dashboard to see backend data</li>
          <li>• Check network tab for API calls to /track/ endpoint</li>
        </ul>
      </div>
    </div>
  );
};

export default AnalyticsTestPanel;
