import React, { useState, useEffect, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { StarIcon, ShoppingCartIcon, HeartIcon, EyeIcon } from '@heroicons/react/24/solid';
import { HeartIcon as HeartOutlineIcon } from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import toast from 'react-hot-toast';

import { useCart } from '../contexts/CartContext.jsx';
import { useAnalytics } from '../contexts/AnalyticsContext.jsx';

const ProductCard = ({ product, isFavorite = false, variant = 'default', listName = 'product_list', position = 0 }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const { addToCart } = useCart();
  const { trackProductImpressions, trackAddToCartAction } = useAnalytics();


  // Handle both Saleor and legacy product formats
  const productData = useMemo(() => ({
    id: product.id,
    name: product.name || product.title,
    price: product.pricing?.priceRange?.start?.gross?.amount || product.price,
    currency: product.pricing?.priceRange?.start?.gross?.currency || 'USD',
    rating: product.rating || 0,
    reviewCount: product.reviewCount || 0,
    images: product.images || (product.imageSrc ? [{ url: product.imageSrc, alt: product.name }] : []),
    category: product.category,
    slug: product.slug,
    isAvailable: product.isAvailable !== false,
    variants: product.variants || []
  }), [product]);

  // Use intersection observer for lazy loading
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const displayImage = productData.images[currentImageIndex] || productData.images[0];
  const hasMultipleImages = productData.images.length > 1;

  // Track product impression when it comes into view
  useEffect(() => {
    if (inView && listName && position >= 0) {
      trackProductImpressions([productData], listName);
    }
  }, [inView, productData, listName, position, trackProductImpressions]);

  const handleAddToCart = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    try {
      if (!productData.isAvailable) {
        toast.error('Product is currently unavailable');
        return;
      }

      // Add visual feedback
      const button = e.currentTarget;
      button.classList.add('animate-pulse');
      setTimeout(() => button.classList.remove('animate-pulse'), 600);

      // Use the first available variant or create a default one
      const variantId = productData.variants[0]?.id || productData.id;

      await addToCart(variantId, 1);

      // Track add to cart action
      trackAddToCartAction(productData, 1, listName || 'product_card');

      toast.success(`${productData.name} added to cart!`, {
        icon: '🛒',
        duration: 2000,
      });
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error('Failed to add item to cart');
    }
  };

  const handleToggleFavorite = (e) => {
    e.preventDefault();
    e.stopPropagation();
    // TODO: Implement Saleor wishlist functionality
    toast.success(
      isFavorite ? `${productData.name} removed from favorites!` : `${productData.name} added to favorites!`,
      { icon: isFavorite ? '💔' : '❤️' }
    );
  };

  const handleImageHover = (index) => {
    if (hasMultipleImages) {
      setCurrentImageIndex(index);
    }
  };

  const handleQuickView = (e) => {
    e.preventDefault();
    e.stopPropagation();
    // TODO: Implement quick view modal
    toast.success('Quick view coming soon!');
  };

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5 }}
      className="group relative overflow-hidden rounded-lg bg-white shadow-soft transition-all duration-300 hover:shadow-hover"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Availability Badge */}
      {!productData.isAvailable && (
        <div className="absolute left-0 top-0 z-10 rounded-br-lg bg-red-500 px-2 py-1 text-xs font-bold text-white">
          OUT OF STOCK
        </div>
      )}

      {/* Category Badge */}
      {productData.category && (
        <div className="absolute right-0 top-0 z-10 rounded-bl-lg bg-gray-800/70 px-2 py-1 text-xs font-medium text-white">
          {productData.category.name}
        </div>
      )}

      {/* Interactive Action Buttons */}
      <AnimatePresence>
        {isHovered && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute right-2 top-12 z-20 flex flex-col gap-2"
          >
            <button
              onClick={handleToggleFavorite}
              className="rounded-full bg-white p-2 shadow-lg transition-colors hover:bg-red-50"
            >
              {isFavorite ? (
                <HeartIcon className="h-5 w-5 text-red-500" />
              ) : (
                <HeartOutlineIcon className="h-5 w-5 text-gray-600" />
              )}
            </button>
            <button
              onClick={handleQuickView}
              className="rounded-full bg-white p-2 shadow-lg transition-colors hover:bg-blue-50"
            >
              <EyeIcon className="h-5 w-5 text-gray-600" />
            </button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Product Image with Interactive Features */}
      <Link to={`/products/${productData.slug || productData.id}`} className="block overflow-hidden">
        <div className="relative h-64 overflow-hidden bg-gray-200">
          {displayImage ? (
            <>
              <motion.img
                src={displayImage.url || displayImage}
                alt={displayImage.alt || productData.name}
                className="h-full w-full object-cover object-center transition-transform duration-500 group-hover:scale-110"
                onLoad={() => setImageLoaded(true)}
                initial={{ opacity: 0 }}
                animate={{ opacity: imageLoaded ? 1 : 0 }}
                transition={{ duration: 0.3 }}
              />

              {/* Image Navigation Dots for Multiple Images */}
              {hasMultipleImages && isHovered && (
                <div className="absolute bottom-2 left-1/2 flex -translate-x-1/2 gap-1">
                  {productData.images.slice(0, 4).map((_, index) => (
                    <button
                      key={index}
                      onClick={(e) => {
                        e.preventDefault();
                        handleImageHover(index);
                      }}
                      className={`h-2 w-2 rounded-full transition-colors ${
                        index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                      }`}
                    />
                  ))}
                </div>
              )}
            </>
          ) : (
            <div className="flex h-full items-center justify-center bg-gray-200 text-gray-500">
              <div className="text-center">
                <div className="text-4xl mb-2">📷</div>
                <div className="text-sm">No Image</div>
              </div>
            </div>
          )}

          {/* Loading Skeleton */}
          {!imageLoaded && displayImage && (
            <div className="absolute inset-0 animate-pulse bg-gray-300" />
          )}
        </div>
      </Link>

      {/* Product Info */}
      <div className="p-4">
        <Link to={`/products/${productData.slug || productData.id}`} className="block">
          <h3 className="mb-1 text-lg font-semibold text-gray-900 transition-colors duration-300 group-hover:text-primary-600 line-clamp-2">
            {productData.name}
          </h3>
        </Link>

        {/* Category */}
        {productData.category && (
          <p className="mb-1 text-xs text-gray-500 uppercase tracking-wide">
            {productData.category.name}
          </p>
        )}

        {/* Rating */}
        {productData.rating > 0 && (
          <div className="mb-2 flex items-center">
            <div className="flex text-yellow-400">
              {[...Array(5)].map((_, i) => (
                <StarIcon
                  key={i}
                  className={`h-4 w-4 ${i < Math.floor(productData.rating) ? 'text-yellow-400' : 'text-gray-300'}`}
                />
              ))}
            </div>
            <span className="ml-1 text-xs text-gray-500">({productData.reviewCount})</span>
          </div>
        )}

        {/* Price */}
        <div className="mb-3 flex items-center">
          <span className="text-lg font-bold text-gray-900">
            {productData.currency === 'USD' ? '$' : productData.currency + ' '}
            {productData.price?.toFixed(2)}
          </span>

          {/* Availability Status */}
          <span className={`ml-2 text-xs px-2 py-1 rounded-full ${
            productData.isAvailable
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'
          }`}>
            {productData.isAvailable ? 'In Stock' : 'Out of Stock'}
          </span>
        </div>

        {/* Interactive Actions */}
        <div className="flex items-center justify-between">
          <motion.button
            onClick={handleAddToCart}
            disabled={!productData.isAvailable}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className={`flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-all focus:outline-none focus:ring-2 focus:ring-offset-2 ${
              productData.isAvailable
                ? 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            <ShoppingCartIcon className="mr-1 h-4 w-4" />
            {productData.isAvailable ? 'Add to Cart' : 'Unavailable'}
          </motion.button>

          <motion.button
            onClick={handleToggleFavorite}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="rounded-full p-2 text-gray-400 transition-colors hover:text-red-500 focus:outline-none"
          >
            {isFavorite ? (
              <HeartIcon className="h-5 w-5 text-red-500" />
            ) : (
              <HeartOutlineIcon className="h-5 w-5" />
            )}
          </motion.button>
        </div>

        {/* Variants Preview (if available) */}
        {productData.variants && productData.variants.length > 1 && (
          <div className="mt-2 flex items-center gap-1">
            <span className="text-xs text-gray-500">
              {productData.variants.length} variants
            </span>
            <div className="flex gap-1">
              {productData.variants.slice(0, 3).map((variant, index) => (
                <div
                  key={variant.id}
                  className="h-3 w-3 rounded-full bg-gray-300 border border-gray-400"
                  title={variant.name}
                />
              ))}
              {productData.variants.length > 3 && (
                <span className="text-xs text-gray-500">+{productData.variants.length - 3}</span>
              )}
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default ProductCard;
