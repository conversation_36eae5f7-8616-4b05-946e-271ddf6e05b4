# eCommerce Platform - Technical Implementation Guide

## 🚀 Quick Start Guide

### Prerequisites
- Python 3.8+
- MySQL 8.0+
- Node.js 16+ (for frontend)
- Stripe Account (for payments)
- Typesense Server (for search)

### Backend Setup

1. **Clone and Setup Environment**
```bash
cd backend
pipenv install
pipenv shell
```

2. **Configure Environment Variables**
```bash
# Create .env file
cp .env.example .env

# Configure required variables
DATABASE_NAME=ecommerce_db
DATABASE_USER=your_db_user
DATABASE_PASSWORD=your_db_password
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
TYPESENSE_API_KEY=your_typesense_key
```

3. **Database Setup**
```bash
# Create database
mysql -u root -p -e "CREATE DATABASE ecommerce_db;"

# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser
```

4. **Start Development Server**
```bash
python manage.py runserver 8001
```

## 🔧 API Usage Examples

### Authentication

#### User Registration
```bash
curl -X POST http://localhost:8001/api/auth/register/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "securepassword123",
    "first_name": "Test",
    "last_name": "User"
  }'
```

#### User Login
```bash
curl -X POST http://localhost:8001/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "securepassword123"
  }'
```

Response:
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": "uuid-here",
    "username": "testuser",
    "email": "<EMAIL>"
  }
}
```

### Product Management

#### Create Product (Manager/Admin only)
```bash
curl -X POST http://localhost:8001/api/products/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Premium Laptop",
    "description": "High-performance laptop for professionals",
    "price": "1299.99",
    "category": "category-uuid",
    "stock_quantity": 50,
    "sku": "LAPTOP-001"
  }'
```

#### Get Products
```bash
curl -X GET http://localhost:8001/api/products/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

#### Search Products
```bash
curl -X GET "http://localhost:8001/api/search/?q=laptop&category=electronics" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Shopping Cart

#### Add Item to Cart
```bash
curl -X POST http://localhost:8001/api/carts/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "product": "product-uuid",
    "quantity": 2
  }'
```

#### Get Cart Items
```bash
curl -X GET http://localhost:8001/api/carts/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Order Management

#### Create Order
```bash
curl -X POST http://localhost:8001/api/orders/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "shipping_address": {
      "street": "123 Main St",
      "city": "New York",
      "state": "NY",
      "zip_code": "10001",
      "country": "US"
    },
    "payment_method": "stripe"
  }'
```

### Payment Processing

#### Basic Payment
```bash
curl -X POST http://localhost:8001/api/payment/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "order-uuid",
    "payment_method_id": "pm_1234567890",
    "amount": 1299.99,
    "currency": "usd"
  }'
```

#### Enhanced Payment with Saved Payment Method
```bash
curl -X POST http://localhost:8001/api/payment/enhanced/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "order-uuid",
    "payment_method_id": "pm_1234567890",
    "save_payment_method": true,
    "set_as_default": true
  }'
```

### Stripe Customer Management

#### Create Stripe Customer
```bash
curl -X POST http://localhost:8001/api/stripe/customers/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "John Doe",
    "phone": "+1234567890"
  }'
```

#### Attach Payment Method
```bash
curl -X POST http://localhost:8001/api/stripe/customers/customer-uuid/attach_payment_method/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "payment_method_id": "pm_1234567890",
    "set_as_default": true
  }'
```

### Refund Processing

#### Create Refund (Manager/Admin only)
```bash
curl -X POST http://localhost:8001/api/stripe/refunds/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "payment": "payment-uuid",
    "amount": 100.00,
    "reason": "requested_by_customer"
  }'
```

### Analytics

#### Get Payment Analytics (Manager/Admin only)
```bash
curl -X GET "http://localhost:8001/api/payments/analytics/?period=monthly&start_date=2024-01-01" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

#### Get User Analytics
```bash
curl -X GET http://localhost:8001/api/analytics/user/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 🔒 Security Implementation

### JWT Token Usage
```javascript
// Frontend example
const token = localStorage.getItem('access_token');
const response = await fetch('/api/products/', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});
```

### Token Refresh
```javascript
// Refresh token when expired
const refreshToken = localStorage.getItem('refresh_token');
const response = await fetch('/api/auth/refresh/', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    refresh: refreshToken
  })
});
```

### Role-Based Access
```python
# Backend permission example
from rest_framework.permissions import IsAuthenticated
from .permissions import IsManagerOrAdmin

class ProductViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    
    def get_permissions(self):
        if self.action in ['create', 'update', 'destroy']:
            permission_classes = [IsManagerOrAdmin]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]
```

## 📊 Database Operations

### Custom Queries
```python
# Get products with low stock
from api.models import Product

low_stock_products = Product.objects.filter(
    stock_quantity__lt=10
).select_related('category')

# Get user's order history
user_orders = Order.objects.filter(
    user=request.user
).prefetch_related('order_lines__product')

# Get payment analytics
from django.db.models import Sum, Count
from api.models import StripePayment

payment_stats = StripePayment.objects.aggregate(
    total_revenue=Sum('amount'),
    total_payments=Count('id'),
    successful_payments=Count('id', filter=Q(status='succeeded'))
)
```

### Inventory Management
```python
# Update inventory
from api.models import InventoryChange

def update_inventory(product, quantity_change, change_type, user):
    InventoryChange.objects.create(
        product=product,
        quantity_change=quantity_change,
        change_type=change_type,
        created_by=user,
        notes=f"Inventory {change_type} by {user.username}"
    )
    
    # Update product stock
    product.stock_quantity += quantity_change
    product.save()
```

## 🎯 Frontend Integration Examples

### React Component Example
```jsx
// ProductList.jsx
import React, { useState, useEffect } from 'react';
import { apiClient } from '../services/api';

const ProductList = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await apiClient.get('/products/');
        setProducts(response.data.results);
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  if (loading) return <div>Loading...</div>;

  return (
    <div className="product-grid">
      {products.map(product => (
        <div key={product.id} className="product-card">
          <h3>{product.name}</h3>
          <p>${product.price}</p>
          <button onClick={() => addToCart(product.id)}>
            Add to Cart
          </button>
        </div>
      ))}
    </div>
  );
};
```

### API Service
```javascript
// api.js
import axios from 'axios';

const API_BASE_URL = 'http://localhost:8001/api';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Handle token refresh
      const refreshToken = localStorage.getItem('refresh_token');
      if (refreshToken) {
        try {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh/`, {
            refresh: refreshToken
          });
          localStorage.setItem('access_token', response.data.access);
          return apiClient.request(error.config);
        } catch (refreshError) {
          // Redirect to login
          window.location.href = '/login';
        }
      }
    }
    return Promise.reject(error);
  }
);

export { apiClient };
```

## 🔧 Configuration Examples

### Django Settings
```python
# settings.py key configurations
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': os.getenv('DATABASE_NAME'),
        'USER': os.getenv('DATABASE_USER'),
        'PASSWORD': os.getenv('DATABASE_PASSWORD'),
        'HOST': os.getenv('DATABASE_HOST', 'localhost'),
        'PORT': os.getenv('DATABASE_PORT', '3306'),
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',
        },
    }
}

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY = os.getenv('STRIPE_PUBLISHABLE_KEY')
STRIPE_SECRET_KEY = os.getenv('STRIPE_SECRET_KEY')
STRIPE_WEBHOOK_SECRET = os.getenv('STRIPE_WEBHOOK_SECRET')

# JWT Configuration
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=60),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
}
```

This technical guide provides practical examples for implementing and using the eCommerce platform's features. Each section includes working code examples that can be directly used in your implementation.
