"""
Django settings for ecommerce_backend project.
"""

import os
from pathlib import Path
from datetime import timed<PERSON>ta
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get('SECRET_KEY', 'django-insecure-your-secret-key-here')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get('DEBUG', 'True') == 'True'

ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', 'localhost,127.0.0.1').split(',')

# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Third-party apps
    'rest_framework',
    'corsheaders',
    'rest_framework_simplejwt',
    'rest_framework_simplejwt.token_blacklist',

    # Local apps
    'api',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'api.middleware.RequestSizeMiddleware',
    'api.middleware.SecurityMiddleware',
    'api.middleware.RateLimitMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'api.middleware.CSRFExemptMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'api.middleware.APILoggingMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'ecommerce_backend.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'ecommerce_backend.wsgi.application'

# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': os.environ.get('DATABASE_NAME', 'ecommerce_db'),
        'USER': os.environ.get('DATABASE_USER', 'ecommerce_user'),
        'PASSWORD': os.environ.get('DATABASE_PASSWORD', 'ecommerce_password'),
        'HOST': os.environ.get('DATABASE_HOST', 'localhost'),
        'PORT': os.environ.get('DATABASE_PORT', '3306'),
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',
        },
    }
}

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'static')

MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Custom User model
AUTH_USER_MODEL = 'api.User'

# REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
    'EXCEPTION_HANDLER': 'api.error_handlers.custom_exception_handler',
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle'
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '100/hour',
        'user': '1000/hour',
        'login': '5/min',
        'register': '3/min',
        'password_reset': '3/hour',
        'order': '10/min',
        'payment': '5/min',
    }
}

# API Schema/Docs (drf-yasg - Swagger)
# Configure conditionally so development doesn't break if the package isn't installed yet.
try:
    import drf_yasg  # type: ignore
    # Enable the app
    INSTALLED_APPS.append('drf_yasg')

    # Swagger settings
    SWAGGER_SETTINGS = {
        'SECURITY_DEFINITIONS': {
            'Bearer': {
                'type': 'apiKey',
                'name': 'Authorization',
                'in': 'header',
            }
        },
        'USE_SESSION_AUTH': False,
        'PERSIST_AUTH': True,
        'REFETCH_SCHEMA_ON_LOGOUT': True,
    }
except Exception:
    # drf-yasg not installed; schema/docs routes will be skipped
    pass


# JWT Settings (tighter lifetimes + refresh rotation)
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=15),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,  # requires token_blacklist app
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',
}

# CORS settings
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = os.environ.get('CORS_ALLOWED_ORIGINS', 'http://localhost:3000,http://127.0.0.1:3000').split(',')
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]
CORS_EXPOSE_HEADERS = [
    'x-csrftoken',
]

# CSRF settings
CSRF_COOKIE_SECURE = os.environ.get('CSRF_COOKIE_SECURE', 'False').lower() == 'true'
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Lax'
CSRF_TRUSTED_ORIGINS = os.environ.get('CSRF_TRUSTED_ORIGINS', 'http://localhost:3000,http://127.0.0.1:3000').split(',')
CSRF_COOKIE_NAME = 'csrftoken'

# Session settings
SESSION_COOKIE_SECURE = os.environ.get('SESSION_COOKIE_SECURE', 'False').lower() == 'true'
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'
SESSION_COOKIE_AGE = 86400  # 24 hours

# Security settings
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'
SECURE_HSTS_SECONDS = 31536000 if os.environ.get('SECURE_SSL_REDIRECT', 'False').lower() == 'true' else 0
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_SSL_REDIRECT = os.environ.get('SECURE_SSL_REDIRECT', 'False').lower() == 'true'

# Frontend URL for links in emails, etc.
# Site branding
SITE_NAME = os.environ.get('SITE_NAME', 'MyEcommerce')

FRONTEND_URL = os.environ.get('FRONTEND_URL', 'http://localhost:3000')

# Typesense Configuration
TYPESENSE = {
    'nodes': [{
        'host': os.environ.get('TYPESENSE_HOST', 'localhost'),
        'port': os.environ.get('TYPESENSE_PORT', '8108'),
        'protocol': os.environ.get('TYPESENSE_PROTOCOL', 'http'),
    }],
    'api_key': os.environ.get('TYPESENSE_API_KEY', 'xyz'),
    'connection_timeout_seconds': 2,
    'retry_interval_seconds': 0.1,
    'num_retries': 3,
    'collection_name': 'products',
}

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY = os.environ.get('STRIPE_PUBLISHABLE_KEY', 'pk_test_your_test_key')
STRIPE_SECRET_KEY = os.environ.get('STRIPE_SECRET_KEY', 'sk_test_your_test_key')
STRIPE_WEBHOOK_SECRET = os.environ.get('STRIPE_WEBHOOK_SECRET', 'whsec_your_webhook_secret')
STRIPE_API_VERSION = '2023-10-16'  # Use the latest API version

# Celery Configuration (broker/result backends default to Redis; override via env)
CELERY_BROKER_URL = os.environ.get('CELERY_BROKER_URL', 'redis://127.0.0.1:6379/0')
CELERY_RESULT_BACKEND = os.environ.get('CELERY_RESULT_BACKEND', CELERY_BROKER_URL)
# Allow opting into synchronous execution for local/dev via env
CELERY_TASK_ALWAYS_EAGER = os.environ.get('CELERY_TASK_ALWAYS_EAGER', 'False').lower() == 'true'
CELERY_TASK_EAGER_PROPAGATES = True

# Notification/Inventory settings
INVENTORY_LOW_STOCK_THRESHOLD = int(os.environ.get('INVENTORY_LOW_STOCK_THRESHOLD', '10'))
NOTIFICATIONS_FROM_EMAIL = os.environ.get('DEFAULT_FROM_EMAIL', '<EMAIL>')

# Email configuration (console backend by default; set SMTP env vars for Gmail)
EMAIL_BACKEND = os.environ.get('EMAIL_BACKEND', 'django.core.mail.backends.console.EmailBackend')
EMAIL_HOST = os.environ.get('EMAIL_HOST', 'smtp.gmail.com')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', '587'))
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD')
EMAIL_USE_TLS = os.environ.get('EMAIL_USE_TLS', 'True').lower() == 'true'
DEFAULT_FROM_EMAIL = os.environ.get('DEFAULT_FROM_EMAIL', EMAIL_HOST_USER or '<EMAIL>')

# Password reset token expiration (seconds)
RESET_PASSWORD_TOKEN_TTL_SECONDS = int(os.environ.get('RESET_PASSWORD_TOKEN_TTL_SECONDS', '3600'))  # 1 hour

# Google OAuth (Gmail) login/signup
GOOGLE_OAUTH_ENABLED = os.environ.get('GOOGLE_OAUTH_ENABLED', 'True').lower() == 'true'
GOOGLE_CLIENT_ID = os.environ.get('GOOGLE_CLIENT_ID')


# Google Analytics (GA4) optional configuration for recommendations
GA_ENABLED = os.environ.get('GA_ENABLED', 'False').lower() == 'true'
GA4_PROPERTY_ID = os.environ.get('GA4_PROPERTY_ID')
GA_CREDENTIALS_FILE = os.environ.get('GA_CREDENTIALS_FILE')  # path to service account JSON

# Debounce windows (seconds) for per-event recommendation rebuilds
RECOMMENDATION_REBUILD_DEBOUNCE_SECONDS = int(os.environ.get('RECOMMENDATION_REBUILD_DEBOUNCE_SECONDS', '600'))
RECOMMENDATION_REBUILD_DEBOUNCE_WINDOWS = {
    # Behavior actions
    "view": int(os.environ.get('RECO_DEBOUNCE_VIEW_SECONDS', '900')),          # 15m
    "click": int(os.environ.get('RECO_DEBOUNCE_CLICK_SECONDS', '600')),        # 10m
    "add_to_cart": int(os.environ.get('RECO_DEBOUNCE_ADD_TO_CART_SECONDS', '180')),  # 3m
    "purchase": int(os.environ.get('RECO_DEBOUNCE_PURCHASE_SECONDS', '60')),   # 1m
    # Cart/Order lifecycle
    "cart_change": int(os.environ.get('RECO_DEBOUNCE_CART_SECONDS', '120')),   # 2m
    "cart_signal": int(os.environ.get('RECO_DEBOUNCE_CART_SIGNAL_SECONDS', '120')),
    "checkout": int(os.environ.get('RECO_DEBOUNCE_CHECKOUT_SECONDS', '30')),   # 30s
    "order_saved": int(os.environ.get('RECO_DEBOUNCE_ORDER_SECONDS', '120')),  # 2m
    "payment_succeeded": int(os.environ.get('RECO_DEBOUNCE_PAYMENT_SECONDS', '30')),
    # Manual/API-triggered
    "manual": int(os.environ.get('RECO_DEBOUNCE_MANUAL_SECONDS', '0')),        # 0 => triggers immediately
    # Default/generic fallback
    "generic": int(os.environ.get('RECO_DEBOUNCE_GENERIC_SECONDS', '600')),
}

# Optional: Celery beat schedule for nightly recommendation rebuilds
try:
    from celery.schedules import crontab  # type: ignore
    CELERY_BEAT_SCHEDULE = {
        'build-recommendations-daily': {
            'task': 'api.tasks.build_recommendations_for_all_users',
            'schedule': crontab(hour=3, minute=0),
            'options': {'queue': 'default'},
        },
    }
except Exception:
    CELERY_BEAT_SCHEDULE = {}


# Create logs directory if it doesn't exist
import os
LOGS_DIR = os.path.join(BASE_DIR, 'logs')
if not os.path.exists(LOGS_DIR):
    os.makedirs(LOGS_DIR)

# Comprehensive Logging Configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
        'detailed': {
            'format': '[{asctime}] {levelname} {name} {module}.{funcName}:{lineno} - {message}',
            'style': '{',
        },
        'json': {
            'format': '{{"timestamp": "{asctime}", "level": "{levelname}", "logger": "{name}", "module": "{module}", "function": "{funcName}", "line": {lineno}, "message": "{message}"}}',
            'style': '{',
        },
        'security': {
            'format': '[SECURITY] {asctime} {levelname} {name} - {message}',
            'style': '{',
        },
    },
    'filters': {
        'require_debug_false': {
            '()': 'django.utils.log.RequireDebugFalse',
        },
        'require_debug_true': {
            '()': 'django.utils.log.RequireDebugTrue',
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'detailed',
        },
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(LOGS_DIR, 'django.log'),
            'maxBytes': 1024*1024*50,  # 50 MB
            'backupCount': 5,
            'formatter': 'detailed',
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(LOGS_DIR, 'django_errors.log'),
            'maxBytes': 1024*1024*50,  # 50 MB
            'backupCount': 5,
            'formatter': 'detailed',
        },
        'security_file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(LOGS_DIR, 'security.log'),
            'maxBytes': 1024*1024*50,  # 50 MB
            'backupCount': 10,
            'formatter': 'security',
        },
        'db_file': {
            'level': 'DEBUG' if DEBUG else 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(LOGS_DIR, 'database.log'),
            'maxBytes': 1024*1024*100,  # 100 MB
            'backupCount': 3,
            'formatter': 'detailed',
        },
        'api_file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(LOGS_DIR, 'api.log'),
            'maxBytes': 1024*1024*50,  # 50 MB
            'backupCount': 5,
            'formatter': 'json',
        },
        'performance_file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(LOGS_DIR, 'performance.log'),
            'maxBytes': 1024*1024*50,  # 50 MB
            'backupCount': 5,
            'formatter': 'detailed',
        },
        'auth_file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(LOGS_DIR, 'authentication.log'),
            'maxBytes': 1024*1024*50,  # 50 MB
            'backupCount': 5,
            'formatter': 'security',
        },
        'middleware_file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(LOGS_DIR, 'middleware.log'),
            'maxBytes': 1024*1024*50,  # 50 MB
            'backupCount': 5,
            'formatter': 'detailed',
        },
        'mail_admins': {
            'level': 'ERROR',
            'filters': ['require_debug_false'],
            'class': 'django.utils.log.AdminEmailHandler',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        # Root logger
        '': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        # Django core loggers
        'django': {
            'handlers': ['console', 'file', 'error_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.request': {
            'handlers': ['console', 'file', 'error_file', 'api_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.server': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.template': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.db.backends': {
            'handlers': ['db_file'] if DEBUG else ['console'],
            'level': 'DEBUG' if DEBUG else 'INFO',
            'propagate': False,
        },
        'django.db.backends.schema': {
            'handlers': ['db_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        # Security related loggers
        'django.security': {
            'handlers': ['security_file', 'console', 'error_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.security.csrf': {
            'handlers': ['security_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.contrib.auth': {
            'handlers': ['auth_file', 'security_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.contrib.sessions': {
            'handlers': ['auth_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        # Middleware loggers
        'django.middleware': {
            'handlers': ['middleware_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'corsheaders': {
            'handlers': ['middleware_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        # REST Framework loggers
        'rest_framework': {
            'handlers': ['api_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'rest_framework.authentication': {
            'handlers': ['auth_file', 'api_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'rest_framework.permissions': {
            'handlers': ['auth_file', 'api_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'rest_framework.throttling': {
            'handlers': ['security_file', 'api_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        # Application loggers
        'api': {
            'handlers': ['api_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'api.views': {
            'handlers': ['api_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'api.models': {
            'handlers': ['api_file', 'db_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'api.serializers': {
            'handlers': ['api_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'api.middleware': {
            'handlers': ['middleware_file', 'security_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'api.throttling': {
            'handlers': ['security_file', 'api_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        # Performance monitoring
        'performance': {
            'handlers': ['performance_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        # Third-party loggers
        'stripe': {
            'handlers': ['api_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'typesense': {
            'handlers': ['api_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        # MySQL/Database loggers
        'MySQLdb': {
            'handlers': ['db_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'mysqlclient': {
            'handlers': ['db_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}
