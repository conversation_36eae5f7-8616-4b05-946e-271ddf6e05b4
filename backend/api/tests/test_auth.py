from django.test import override_settings
from django.core.cache import cache
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from unittest.mock import patch, MagicMock
from django.contrib.auth import get_user_model
import json
import re

User = get_user_model()


class PasswordResetTests(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="OldPassw0rd!",
            is_active=True,
        )

    @patch("api.views.send_mail")
    def test_forgot_password_sends_email_and_can_reset(self, mock_send_mail):
        resp = self.client.post("/api/auth/forgot-password/", {"email": self.user.email}, format="json")
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        self.assertTrue(mock_send_mail.called)

        # Extract token from html_message argument
        args, kwargs = mock_send_mail.call_args
        html = kwargs.get("html_message") or ""
        m = re.search(r"token=([A-Za-z0-9_\-\.]+)", html)
        self.assertIsNotNone(m, "reset token not found in email html")
        token = m.group(1)

        # Reset with a strong password
        new_pwd = "NewSecurePass123!"
        resp2 = self.client.post("/api/auth/reset-password/", {"token": token, "new_password": new_pwd}, format="json")
        self.assertEqual(resp2.status_code, status.HTTP_200_OK)

        # Verify login works with new password
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password(new_pwd))

    @patch("api.views.send_mail")
    def test_reset_password_rejects_invalid_or_expired_token(self, mock_send_mail):
        # Request reset to generate a token, then delete it from cache to simulate expiration
        self.client.post("/api/auth/forgot-password/", {"email": self.user.email}, format="json")
        args, kwargs = mock_send_mail.call_args
        html = kwargs.get("html_message") or ""
        m = re.search(r"token=([A-Za-z0-9_\-\.]+)", html)
        token = m.group(1)

        # Simulate expiration by deleting from cache
        cache.delete(f"password_reset:{token}")

        resp = self.client.post("/api/auth/reset-password/", {"token": token, "new_password": "AnotherPass123!"}, format="json")
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("Invalid or expired", json.dumps(resp.data))


class GoogleAuthTests(APITestCase):
    @override_settings(GOOGLE_OAUTH_ENABLED=True, GOOGLE_CLIENT_ID="test-client")
    @patch("api.views.urllib.request.urlopen")
    def test_google_auth_creates_user_and_returns_tokens(self, mock_urlopen):
        # Mock tokeninfo endpoint response
        payload = {
            "aud": "test-client",
            "email": "<EMAIL>",
            "email_verified": "true",
            "name": "G User",
            "exp": 9999999999,
        }

        class _Resp:
            def __enter__(self):
                return self
            def __exit__(self, exc_type, exc, tb):
                return False
            def read(self):
                return json.dumps(payload).encode("utf-8")

        mock_urlopen.return_value = _Resp()

        resp = self.client.post("/api/auth/google/", {"id_token": "dummy"}, format="json")
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        self.assertIn("access", resp.data)
        self.assertIn("refresh", resp.data)
        self.assertTrue(User.objects.filter(email="<EMAIL>").exists())

